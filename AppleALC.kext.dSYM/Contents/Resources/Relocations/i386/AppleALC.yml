---
triple:          'i386-apple-darwin'
binary-path:     '/Users/<USER>/work/AppleALC/AppleALC/build/Release/AppleALC.kext/Contents/MacOS/AppleALC'
relocations:
  - { offsetInCU: 0x35, offset: 0x35, size: 0x4, addend: 0x0, symName: _kmod_info, symObjAddr: 0x0, symBinAddr: 0x1A1FAC, symSize: 0x0 }
  - { offsetInCU: 0x206, offset: 0x206, size: 0x4, addend: 0x0, symName: __realmain, symObjAddr: 0xA8, symBinAddr: 0x1A2054, symSize: 0x0 }
  - { offsetInCU: 0x218, offset: 0x218, size: 0x4, addend: 0x0, symName: __antimain, symObjAddr: 0xAC, symBinAddr: 0x1A2058, symSize: 0x0 }
  - { offsetInCU: 0x22A, offset: 0x22A, size: 0x4, addend: 0x0, symName: __kext_apple_cc, symObjAddr: 0xB0, symBinAddr: 0x1A205C, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x256, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler12createSharedEv, symObjAddr: 0x0, symBinAddr: 0x0, symSize: 0x40 }
  - { offsetInCU: 0x36, offset: 0x265, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler11callbackAlcE, symObjAddr: 0x3E6A8, symBinAddr: 0x1AA688, symSize: 0x0 }
  - { offsetInCU: 0xD603, offset: 0xD832, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler16updatePropertiesEv, symObjAddr: 0x240, symBinAddr: 0x240, symSize: 0xEA0 }
  - { offsetInCU: 0xDCB5, offset: 0xDEE4, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler18eraseRedundantLogsER13KernelPatcherm, symObjAddr: 0x1870, symBinAddr: 0x1870, symSize: 0x80 }
  - { offsetInCU: 0xDCD5, offset: 0xDF04, size: 0x4, addend: 0x0, symName: __ZZN10AlcEnabler18eraseRedundantLogsER13KernelPatchermE13logAssertFind, symObjAddr: 0x4B06, symBinAddr: 0x5C10, symSize: 0x0 }
  - { offsetInCU: 0xDCE7, offset: 0xDF16, size: 0x4, addend: 0x0, symName: __ZZN10AlcEnabler18eraseRedundantLogsER13KernelPatchermE11nullReplace, symObjAddr: 0x4B0E, symBinAddr: 0x5C18, symSize: 0x0 }
  - { offsetInCU: 0xDDD2, offset: 0xE001, size: 0x4, addend: 0x0, symName: __ZL10alcEnabler, symObjAddr: 0x4B18, symBinAddr: 0x1A2060, symSize: 0x0 }
  - { offsetInCU: 0x12A6F, offset: 0x12C9E, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler12createSharedEv, symObjAddr: 0x0, symBinAddr: 0x0, symSize: 0x40 }
  - { offsetInCU: 0x12BE3, offset: 0x12E12, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler4initEv, symObjAddr: 0x40, symBinAddr: 0x40, symSize: 0x120 }
  - { offsetInCU: 0x12E6D, offset: 0x1309C, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler6deinitEv, symObjAddr: 0x160, symBinAddr: 0x160, symSize: 0xE0 }
  - { offsetInCU: 0x13005, offset: 0x13234, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler17validateInjectionEP15IORegistryEntry, symObjAddr: 0x10E0, symBinAddr: 0x10E0, symSize: 0x60 }
  - { offsetInCU: 0x13045, offset: 0x13274, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler22updateDevicePropertiesEP15IORegistryEntryP10DeviceInfoPKcb, symObjAddr: 0x1140, symBinAddr: 0x1140, symSize: 0x350 }
  - { offsetInCU: 0x133D6, offset: 0x13605, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler16insertControllerEjjjbjjP15IORegistryEntry, symObjAddr: 0x1490, symBinAddr: 0x1490, symSize: 0xE0 }
  - { offsetInCU: 0x135CF, offset: 0x137FE, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler8gfxProbeEP9IOServiceS1_Pl, symObjAddr: 0x1570, symBinAddr: 0x1570, symSize: 0x50 }
  - { offsetInCU: 0x13624, offset: 0x13853, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler24AppleHDAController_startEP9IOServiceS1_, symObjAddr: 0x15C0, symBinAddr: 0x15C0, symSize: 0x120 }
  - { offsetInCU: 0x136DB, offset: 0x1390A, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler28IOHDACodecDevice_executeVerbEPvtttPjb, symObjAddr: 0x16E0, symBinAddr: 0x16E0, symSize: 0x10 }
  - { offsetInCU: 0x1374C, offset: 0x1397B, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler14getAudioLayoutEP9IOService, symObjAddr: 0x16F0, symBinAddr: 0x16F0, symSize: 0x130 }
  - { offsetInCU: 0x13805, offset: 0x13A34, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler28handleAudioClientEntitlementEP4taskPKcRP8OSObject, symObjAddr: 0x1820, symBinAddr: 0x1820, symSize: 0x50 }
  - { offsetInCU: 0x13D7C, offset: 0x13FAB, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler11processKextER13KernelPatchermym, symObjAddr: 0x18F0, symBinAddr: 0x18F0, symSize: 0x7C0 }
  - { offsetInCU: 0x14448, offset: 0x14677, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler15grabControllersEv, symObjAddr: 0x20B0, symBinAddr: 0x20B0, symSize: 0x3A0 }
  - { offsetInCU: 0x14662, offset: 0x14891, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler10grabCodecsEv, symObjAddr: 0x2450, symBinAddr: 0x2450, symSize: 0x130 }
  - { offsetInCU: 0x146FF, offset: 0x1492E, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler12applyPatchesER13KernelPatchermPK9KextPatchm, symObjAddr: 0x2580, symBinAddr: 0x2580, symSize: 0x70 }
  - { offsetInCU: 0x1475B, offset: 0x1498A, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler18performPowerChangeEP9IOServicejjPj, symObjAddr: 0x25F0, symBinAddr: 0x25F0, symSize: 0x1F0 }
  - { offsetInCU: 0x14866, offset: 0x14A95, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler19initializePinConfigEP9IOServiceS1_, symObjAddr: 0x27E0, symBinAddr: 0x27E0, symSize: 0x30 }
  - { offsetInCU: 0x1489A, offset: 0x14AC9, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler25initializePinConfigLegacyEP9IOService, symObjAddr: 0x2810, symBinAddr: 0x2810, symSize: 0xB0 }
  - { offsetInCU: 0x148E6, offset: 0x14B15, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler18layoutLoadCallbackEjiPKvjPv, symObjAddr: 0x28C0, symBinAddr: 0x28C0, symSize: 0x50 }
  - { offsetInCU: 0x1494D, offset: 0x14B7C, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler20platformLoadCallbackEjiPKvjPv, symObjAddr: 0x2910, symBinAddr: 0x2910, symSize: 0x50 }
  - { offsetInCU: 0x149B4, offset: 0x14BE3, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler20AppleHDADriver_startEP9IOServiceS1_, symObjAddr: 0x2960, symBinAddr: 0x2960, symSize: 0x30 }
  - { offsetInCU: 0x149E8, offset: 0x14C17, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler28AppleHDAPlatformDriver_startEP9IOServiceS1_, symObjAddr: 0x2990, symBinAddr: 0x2990, symSize: 0x30 }
  - { offsetInCU: 0x14A1B, offset: 0x14C4A, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler19validateControllersEv, symObjAddr: 0x29C0, symBinAddr: 0x29C0, symSize: 0x130 }
  - { offsetInCU: 0x14A72, offset: 0x14CA1, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler14patchPinConfigEP9IOServiceP15IORegistryEntry, symObjAddr: 0x2AF0, symBinAddr: 0x2AF0, symSize: 0x530 }
  - { offsetInCU: 0x14C51, offset: 0x14E80, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler14updateResourceENS_8ResourceERiRPKvRj, symObjAddr: 0x3020, symBinAddr: 0x3020, symSize: 0x270 }
  - { offsetInCU: 0x14E80, offset: 0x150AF, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler11appendCodecEPvP15IORegistryEntry, symObjAddr: 0x3290, symBinAddr: 0x3290, symSize: 0x1A0 }
  - { offsetInCU: 0x1503B, offset: 0x1526A, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler14validateCodecsEv, symObjAddr: 0x3430, symBinAddr: 0x3430, symSize: 0x1C0 }
  - { offsetInCU: 0x15102, offset: 0x15331, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler30replaceAppleHDADriverResourcesEP9IOService, symObjAddr: 0x35F0, symBinAddr: 0x35F0, symSize: 0x640 }
  - { offsetInCU: 0x15332, offset: 0x15561, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler26unserializeCodecDictionaryEPKhj, symObjAddr: 0x3C30, symBinAddr: 0x3C30, symSize: 0x100 }
  - { offsetInCU: 0x1544A, offset: 0x15679, size: 0x4, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_08__invokeEPvR13KernelPatcher', symObjAddr: 0x3D30, symBinAddr: 0x3D30, symSize: 0x10 }
  - { offsetInCU: 0x154FF, offset: 0x1572E, size: 0x4, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_18__invokeEPvR13KernelPatchermym', symObjAddr: 0x3D40, symBinAddr: 0x3D40, symSize: 0x10 }
  - { offsetInCU: 0x1561D, offset: 0x1584C, size: 0x4, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_28__invokeEPvP4taskPKcRP8OSObject', symObjAddr: 0x3D50, symBinAddr: 0x3D50, symSize: 0x44 }
  - { offsetInCU: 0x27, offset: 0x15E3A, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x3DA0, symSize: 0x30 }
  - { offsetInCU: 0x36, offset: 0x15E49, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider10gMetaClassE, symObjAddr: 0x22BAC, symBinAddr: 0x1AA68C, symSize: 0x0 }
  - { offsetInCU: 0x6943, offset: 0x1C756, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider9metaClassE, symObjAddr: 0x438, symBinAddr: 0x5C20, symSize: 0x0 }
  - { offsetInCU: 0x6952, offset: 0x1C765, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider10superClassE, symObjAddr: 0x43C, symBinAddr: 0x5C24, symSize: 0x0 }
  - { offsetInCU: 0x8314, offset: 0x1E127, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x3DA0, symSize: 0x30 }
  - { offsetInCU: 0x8366, offset: 0x1E179, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassD1Ev, symObjAddr: 0x30, symBinAddr: 0x3DD0, symSize: 0x10 }
  - { offsetInCU: 0x839B, offset: 0x1E1AE, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderC2EPK11OSMetaClass, symObjAddr: 0x40, symBinAddr: 0x3DE0, symSize: 0x30 }
  - { offsetInCU: 0x83EC, offset: 0x1E1FF, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderC1EPK11OSMetaClass, symObjAddr: 0x70, symBinAddr: 0x3E10, symSize: 0x30 }
  - { offsetInCU: 0x8444, offset: 0x1E257, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderD2Ev, symObjAddr: 0xA0, symBinAddr: 0x3E40, symSize: 0x10 }
  - { offsetInCU: 0x847D, offset: 0x1E290, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderD1Ev, symObjAddr: 0xB0, symBinAddr: 0x3E50, symSize: 0x10 }
  - { offsetInCU: 0x84CF, offset: 0x1E2E2, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderD0Ev, symObjAddr: 0xC0, symBinAddr: 0x3E60, symSize: 0x30 }
  - { offsetInCU: 0x8529, offset: 0x1E33C, size: 0x4, addend: 0x0, symName: __ZNK21ALCUserClientProvider12getMetaClassEv, symObjAddr: 0xF0, symBinAddr: 0x3E90, symSize: 0x10 }
  - { offsetInCU: 0x854A, offset: 0x1E35D, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassC2Ev, symObjAddr: 0x100, symBinAddr: 0x3EA0, symSize: 0x30 }
  - { offsetInCU: 0x85A0, offset: 0x1E3B3, size: 0x4, addend: 0x0, symName: __ZNK21ALCUserClientProvider9MetaClass5allocEv, symObjAddr: 0x130, symBinAddr: 0x3ED0, symSize: 0x50 }
  - { offsetInCU: 0x85F1, offset: 0x1E404, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderC1Ev, symObjAddr: 0x180, symBinAddr: 0x3F20, symSize: 0x40 }
  - { offsetInCU: 0x8626, offset: 0x1E439, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderC2Ev, symObjAddr: 0x1C0, symBinAddr: 0x3F60, symSize: 0x40 }
  - { offsetInCU: 0x870D, offset: 0x1E520, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider5probeEP9IOServicePl, symObjAddr: 0x200, symBinAddr: 0x3FA0, symSize: 0x100 }
  - { offsetInCU: 0x87ED, offset: 0x1E600, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider5startEP9IOService, symObjAddr: 0x300, symBinAddr: 0x40A0, symSize: 0x60 }
  - { offsetInCU: 0x881F, offset: 0x1E632, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider4stopEP9IOService, symObjAddr: 0x360, symBinAddr: 0x4100, symSize: 0x10 }
  - { offsetInCU: 0x8851, offset: 0x1E664, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider14sendHdaCommandEttt, symObjAddr: 0x370, symBinAddr: 0x4110, symSize: 0x70 }
  - { offsetInCU: 0x88DC, offset: 0x1E6EF, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassD0Ev, symObjAddr: 0x3E0, symBinAddr: 0x4180, symSize: 0x10 }
  - { offsetInCU: 0x895C, offset: 0x1E76F, size: 0x4, addend: 0x0, symName: __GLOBAL__sub_I_ALCUserClientProvider.cpp, symObjAddr: 0x3F0, symBinAddr: 0x4190, symSize: 0x30 }
  - { offsetInCU: 0x89AC, offset: 0x1E7BF, size: 0x4, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x420, symBinAddr: 0x41C0, symSize: 0x17 }
  - { offsetInCU: 0x27, offset: 0x1E82F, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x41E0, symSize: 0x30 }
  - { offsetInCU: 0x36, offset: 0x1E83E, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient10gMetaClassE, symObjAddr: 0x1B9E0, symBinAddr: 0x1AA6A4, symSize: 0x0 }
  - { offsetInCU: 0x6297, offset: 0x24A9F, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient9metaClassE, symObjAddr: 0x3E8, symBinAddr: 0x6148, symSize: 0x0 }
  - { offsetInCU: 0x62A6, offset: 0x24AAE, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient10superClassE, symObjAddr: 0x3EC, symBinAddr: 0x614C, symSize: 0x0 }
  - { offsetInCU: 0x62B5, offset: 0x24ABD, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient14sMethodsLegacyE, symObjAddr: 0x9A0, symBinAddr: 0x1A2134, symSize: 0x0 }
  - { offsetInCU: 0x62E0, offset: 0x24AE8, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x41E0, symSize: 0x30 }
  - { offsetInCU: 0x6332, offset: 0x24B3A, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassD1Ev, symObjAddr: 0x30, symBinAddr: 0x4210, symSize: 0x10 }
  - { offsetInCU: 0x6367, offset: 0x24B6F, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientC2EPK11OSMetaClass, symObjAddr: 0x40, symBinAddr: 0x4220, symSize: 0x40 }
  - { offsetInCU: 0x63B3, offset: 0x24BBB, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientC1EPK11OSMetaClass, symObjAddr: 0x80, symBinAddr: 0x4260, symSize: 0x40 }
  - { offsetInCU: 0x640B, offset: 0x24C13, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientD2Ev, symObjAddr: 0xC0, symBinAddr: 0x42A0, symSize: 0x10 }
  - { offsetInCU: 0x6444, offset: 0x24C4C, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientD1Ev, symObjAddr: 0xD0, symBinAddr: 0x42B0, symSize: 0x10 }
  - { offsetInCU: 0x6496, offset: 0x24C9E, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientD0Ev, symObjAddr: 0xE0, symBinAddr: 0x42C0, symSize: 0x30 }
  - { offsetInCU: 0x64F0, offset: 0x24CF8, size: 0x4, addend: 0x0, symName: __ZNK13ALCUserClient12getMetaClassEv, symObjAddr: 0x110, symBinAddr: 0x42F0, symSize: 0x10 }
  - { offsetInCU: 0x6511, offset: 0x24D19, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassC2Ev, symObjAddr: 0x120, symBinAddr: 0x4300, symSize: 0x30 }
  - { offsetInCU: 0x6567, offset: 0x24D6F, size: 0x4, addend: 0x0, symName: __ZNK13ALCUserClient9MetaClass5allocEv, symObjAddr: 0x150, symBinAddr: 0x4330, symSize: 0x50 }
  - { offsetInCU: 0x65B8, offset: 0x24DC0, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientC1Ev, symObjAddr: 0x1A0, symBinAddr: 0x4380, symSize: 0x40 }
  - { offsetInCU: 0x65BC, offset: 0x24DC4, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientC2Ev, symObjAddr: 0x1E0, symBinAddr: 0x43C0, symSize: 0x40 }
  - { offsetInCU: 0x65ED, offset: 0x24DF5, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientC2Ev, symObjAddr: 0x1E0, symBinAddr: 0x43C0, symSize: 0x40 }
  - { offsetInCU: 0x65F1, offset: 0x24DF9, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient22sendHdaCommandInternalEPS_tttPy, symObjAddr: 0x220, symBinAddr: 0x4400, symSize: 0x40 }
  - { offsetInCU: 0x6609, offset: 0x24E11, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient22sendHdaCommandInternalEPS_tttPy, symObjAddr: 0x220, symBinAddr: 0x4400, symSize: 0x40 }
  - { offsetInCU: 0x6667, offset: 0x24E6F, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient26getTargetAndMethodForIndexEPP9IOServicem, symObjAddr: 0x260, symBinAddr: 0x4440, symSize: 0x20 }
  - { offsetInCU: 0x66A8, offset: 0x24EB0, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient12initWithTaskEP4taskPvmP12OSDictionary, symObjAddr: 0x280, symBinAddr: 0x4460, symSize: 0x40 }
  - { offsetInCU: 0x6703, offset: 0x24F0B, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient5startEP9IOService, symObjAddr: 0x2C0, symBinAddr: 0x44A0, symSize: 0x40 }
  - { offsetInCU: 0x6744, offset: 0x24F4C, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient11clientCloseEv, symObjAddr: 0x300, symBinAddr: 0x44E0, symSize: 0x40 }
  - { offsetInCU: 0x6767, offset: 0x24F6F, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient17methodExecuteVerbEP21ALCUserClientProviderPvP25IOExternalMethodArguments, symObjAddr: 0x340, symBinAddr: 0x4520, symSize: 0x40 }
  - { offsetInCU: 0x67E5, offset: 0x24FED, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassD0Ev, symObjAddr: 0x380, symBinAddr: 0x4560, symSize: 0x10 }
  - { offsetInCU: 0x6865, offset: 0x2506D, size: 0x4, addend: 0x0, symName: __GLOBAL__sub_I_ALCUserClient.cpp, symObjAddr: 0x390, symBinAddr: 0x4570, symSize: 0x40 }
  - { offsetInCU: 0x68B5, offset: 0x250BD, size: 0x4, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x3D0, symBinAddr: 0x45B0, symSize: 0x17 }
  - { offsetInCU: 0x27, offset: 0x2512D, size: 0x4, addend: 0x0, symName: __ZN8AppleALC9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x45D0, symSize: 0x30 }
  - { offsetInCU: 0x3D, offset: 0x25143, size: 0x4, addend: 0x0, symName: _AppleALC_startSuccess, symObjAddr: 0x26D58, symBinAddr: 0x1AA6BC, symSize: 0x0 }
  - { offsetInCU: 0x57, offset: 0x2515D, size: 0x4, addend: 0x0, symName: _AppleALC_debugEnabled, symObjAddr: 0x26D59, symBinAddr: 0x1AA6BD, symSize: 0x0 }
  - { offsetInCU: 0x6A, offset: 0x25170, size: 0x4, addend: 0x0, symName: _AppleALC_debugPrintDelay, symObjAddr: 0x26D5C, symBinAddr: 0x1AA6C0, symSize: 0x0 }
  - { offsetInCU: 0x7D, offset: 0x25183, size: 0x4, addend: 0x0, symName: __ZN8AppleALC10gMetaClassE, symObjAddr: 0x26D60, symBinAddr: 0x1AA6C4, symSize: 0x0 }
  - { offsetInCU: 0x5266, offset: 0x2A36C, size: 0x4, addend: 0x0, symName: __ZN8AppleALC9metaClassE, symObjAddr: 0x498, symBinAddr: 0x66F0, symSize: 0x0 }
  - { offsetInCU: 0x5275, offset: 0x2A37B, size: 0x4, addend: 0x0, symName: __ZN8AppleALC10superClassE, symObjAddr: 0x49C, symBinAddr: 0x66F4, symSize: 0x0 }
  - { offsetInCU: 0x528B, offset: 0x2A391, size: 0x4, addend: 0x0, symName: _AppleALC_selfInstance, symObjAddr: 0x26D78, symBinAddr: 0x1AA6DC, symSize: 0x0 }
  - { offsetInCU: 0x52A2, offset: 0x2A3A8, size: 0x4, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0x9BC, symBinAddr: 0x6C14, symSize: 0x0 }
  - { offsetInCU: 0x52ED, offset: 0x2A3F3, size: 0x4, addend: 0x0, symName: __ZN8AppleALC9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x45D0, symSize: 0x30 }
  - { offsetInCU: 0x533F, offset: 0x2A445, size: 0x4, addend: 0x0, symName: __ZN8AppleALC9MetaClassD1Ev, symObjAddr: 0x30, symBinAddr: 0x4600, symSize: 0x10 }
  - { offsetInCU: 0x5374, offset: 0x2A47A, size: 0x4, addend: 0x0, symName: __ZN8AppleALCC2EPK11OSMetaClass, symObjAddr: 0x40, symBinAddr: 0x4610, symSize: 0x30 }
  - { offsetInCU: 0x53C0, offset: 0x2A4C6, size: 0x4, addend: 0x0, symName: __ZN8AppleALCC1EPK11OSMetaClass, symObjAddr: 0x70, symBinAddr: 0x4640, symSize: 0x30 }
  - { offsetInCU: 0x5418, offset: 0x2A51E, size: 0x4, addend: 0x0, symName: __ZN8AppleALCD2Ev, symObjAddr: 0xA0, symBinAddr: 0x4670, symSize: 0x10 }
  - { offsetInCU: 0x5451, offset: 0x2A557, size: 0x4, addend: 0x0, symName: __ZN8AppleALCD1Ev, symObjAddr: 0xB0, symBinAddr: 0x4680, symSize: 0x10 }
  - { offsetInCU: 0x54A3, offset: 0x2A5A9, size: 0x4, addend: 0x0, symName: __ZN8AppleALCD0Ev, symObjAddr: 0xC0, symBinAddr: 0x4690, symSize: 0x30 }
  - { offsetInCU: 0x54FD, offset: 0x2A603, size: 0x4, addend: 0x0, symName: __ZNK8AppleALC12getMetaClassEv, symObjAddr: 0xF0, symBinAddr: 0x46C0, symSize: 0x10 }
  - { offsetInCU: 0x551E, offset: 0x2A624, size: 0x4, addend: 0x0, symName: __ZN8AppleALC9MetaClassC2Ev, symObjAddr: 0x100, symBinAddr: 0x46D0, symSize: 0x30 }
  - { offsetInCU: 0x5574, offset: 0x2A67A, size: 0x4, addend: 0x0, symName: __ZNK8AppleALC9MetaClass5allocEv, symObjAddr: 0x130, symBinAddr: 0x4700, symSize: 0x40 }
  - { offsetInCU: 0x55C5, offset: 0x2A6CB, size: 0x4, addend: 0x0, symName: __ZN8AppleALCC1Ev, symObjAddr: 0x170, symBinAddr: 0x4740, symSize: 0x40 }
  - { offsetInCU: 0x55FA, offset: 0x2A700, size: 0x4, addend: 0x0, symName: __ZN8AppleALCC2Ev, symObjAddr: 0x1B0, symBinAddr: 0x4780, symSize: 0x40 }
  - { offsetInCU: 0x5616, offset: 0x2A71C, size: 0x4, addend: 0x0, symName: __ZN8AppleALC5probeEP9IOServicePl, symObjAddr: 0x1F0, symBinAddr: 0x47C0, symSize: 0x60 }
  - { offsetInCU: 0x5666, offset: 0x2A76C, size: 0x4, addend: 0x0, symName: __ZN8AppleALC5startEP9IOService, symObjAddr: 0x250, symBinAddr: 0x4820, symSize: 0x60 }
  - { offsetInCU: 0x5698, offset: 0x2A79E, size: 0x4, addend: 0x0, symName: __ZN8AppleALC4stopEP9IOService, symObjAddr: 0x2B0, symBinAddr: 0x4880, symSize: 0x20 }
  - { offsetInCU: 0x573F, offset: 0x2A845, size: 0x4, addend: 0x0, symName: _AppleALC_kern_start, symObjAddr: 0x2D0, symBinAddr: 0x48A0, symSize: 0x160 }
  - { offsetInCU: 0x5828, offset: 0x2A92E, size: 0x4, addend: 0x0, symName: _AppleALC_kern_stop, symObjAddr: 0x430, symBinAddr: 0x4A00, symSize: 0x10 }
  - { offsetInCU: 0x5874, offset: 0x2A97A, size: 0x4, addend: 0x0, symName: __ZN8AppleALC9MetaClassD0Ev, symObjAddr: 0x440, symBinAddr: 0x4A10, symSize: 0x10 }
  - { offsetInCU: 0x58F4, offset: 0x2A9FA, size: 0x4, addend: 0x0, symName: __GLOBAL__sub_I_plugin_start.cpp, symObjAddr: 0x450, symBinAddr: 0x4A20, symSize: 0x30 }
  - { offsetInCU: 0x5944, offset: 0x2AA4A, size: 0x4, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x480, symBinAddr: 0x4A50, symSize: 0x17 }
  - { offsetInCU: 0x27, offset: 0x2AABA, size: 0x4, addend: 0x0, symName: '__ZN3$_08__invokeEv', symObjAddr: 0x0, symBinAddr: 0x4A70, symSize: 0x1D }
  - { offsetInCU: 0x3D, offset: 0x2AAD0, size: 0x4, addend: 0x0, symName: _AppleALC_config, symObjAddr: 0x4C, symBinAddr: 0x1A2158, symSize: 0x0 }
  - { offsetInCU: 0x12B, offset: 0x2ABBE, size: 0x4, addend: 0x0, symName: __ZL10bootargOff, symObjAddr: 0x40, symBinAddr: 0x1A214C, symSize: 0x0 }
  - { offsetInCU: 0x154, offset: 0x2ABE7, size: 0x4, addend: 0x0, symName: __ZL12bootargDebug, symObjAddr: 0x44, symBinAddr: 0x1A2150, symSize: 0x0 }
  - { offsetInCU: 0x16A, offset: 0x2ABFD, size: 0x4, addend: 0x0, symName: __ZL11bootargBeta, symObjAddr: 0x48, symBinAddr: 0x1A2154, symSize: 0x0 }
  - { offsetInCU: 0x20E, offset: 0x2ACA1, size: 0x4, addend: 0x0, symName: '__ZN3$_08__invokeEv', symObjAddr: 0x0, symBinAddr: 0x4A70, symSize: 0x1D }
  - { offsetInCU: 0x35, offset: 0x2AD10, size: 0x4, addend: 0x0, symName: _KextIdAppleHDA, symObjAddr: 0x0, symBinAddr: 0x6C28, symSize: 0x0 }
  - { offsetInCU: 0x54, offset: 0x2AD2F, size: 0x4, addend: 0x0, symName: _KextIdAppleHDAPlatformDriver, symObjAddr: 0x4, symBinAddr: 0x6C2C, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0x2AD42, size: 0x4, addend: 0x0, symName: _KextIdAppleHDAController, symObjAddr: 0x8, symBinAddr: 0x6C30, symSize: 0x0 }
  - { offsetInCU: 0x7A, offset: 0x2AD55, size: 0x4, addend: 0x0, symName: _KextIdAppleGFXHDA, symObjAddr: 0xC, symBinAddr: 0x6C34, symSize: 0x0 }
  - { offsetInCU: 0x8D, offset: 0x2AD68, size: 0x4, addend: 0x0, symName: _KextIdIOHDAFamily, symObjAddr: 0x10, symBinAddr: 0x6C38, symSize: 0x0 }
  - { offsetInCU: 0xA0, offset: 0x2AD7B, size: 0x4, addend: 0x0, symName: _AppleALC_kextList, symObjAddr: 0x19B5FC, symBinAddr: 0x1A219C, symSize: 0x0 }
  - { offsetInCU: 0x112, offset: 0x2ADED, size: 0x4, addend: 0x0, symName: _AppleALC_kextListSize, symObjAddr: 0x14, symBinAddr: 0x6C3C, symSize: 0x0 }
  - { offsetInCU: 0x128, offset: 0x2AE03, size: 0x4, addend: 0x0, symName: _AppleALC_vendorMod, symObjAddr: 0x19C7A4, symBinAddr: 0x1A3344, symSize: 0x0 }
  - { offsetInCU: 0x19B, offset: 0x2AE76, size: 0x4, addend: 0x0, symName: _AppleALC_vendorModSize, symObjAddr: 0x18, symBinAddr: 0x6C40, symSize: 0x0 }
  - { offsetInCU: 0x1B1, offset: 0x2AE8C, size: 0x4, addend: 0x0, symName: _AppleALC_controllerMod, symObjAddr: 0x19D140, symBinAddr: 0x1A3CE0, symSize: 0x0 }
  - { offsetInCU: 0x1D3, offset: 0x2AEAE, size: 0x4, addend: 0x0, symName: _AppleALC_controllerModSize, symObjAddr: 0x1C, symBinAddr: 0x6C44, symSize: 0x0 }
  - { offsetInCU: 0x1E5, offset: 0x2AEC0, size: 0x4, addend: 0x0, symName: __ZL9kextPath0, symObjAddr: 0x19B5E8, symBinAddr: 0x1A2188, symSize: 0x0 }
  - { offsetInCU: 0x207, offset: 0x2AEE2, size: 0x4, addend: 0x0, symName: __ZL9kextPath1, symObjAddr: 0x19B5EC, symBinAddr: 0x1A218C, symSize: 0x0 }
  - { offsetInCU: 0x21D, offset: 0x2AEF8, size: 0x4, addend: 0x0, symName: __ZL9kextPath2, symObjAddr: 0x19B5F0, symBinAddr: 0x1A2190, symSize: 0x0 }
  - { offsetInCU: 0x233, offset: 0x2AF0E, size: 0x4, addend: 0x0, symName: __ZL9kextPath3, symObjAddr: 0x19B5F4, symBinAddr: 0x1A2194, symSize: 0x0 }
  - { offsetInCU: 0x249, offset: 0x2AF24, size: 0x4, addend: 0x0, symName: __ZL9kextPath4, symObjAddr: 0x19B5F8, symBinAddr: 0x1A2198, symSize: 0x0 }
  - { offsetInCU: 0x25F, offset: 0x2AF3A, size: 0x4, addend: 0x0, symName: __ZL11codecModAMD, symObjAddr: 0x1E051C, symBinAddr: 0x1AA6E0, symSize: 0x0 }
  - { offsetInCU: 0x282, offset: 0x2AF5D, size: 0x4, addend: 0x0, symName: __ZL11codecModVIA, symObjAddr: 0x19B674, symBinAddr: 0x1A2214, symSize: 0x0 }
  - { offsetInCU: 0x2A4, offset: 0x2AF7F, size: 0x4, addend: 0x0, symName: __ZL10revisions0, symObjAddr: 0x20, symBinAddr: 0x6C48, symSize: 0x0 }
  - { offsetInCU: 0x2C6, offset: 0x2AFA1, size: 0x4, addend: 0x0, symName: __ZL10platforms0, symObjAddr: 0x24, symBinAddr: 0x6C4C, symSize: 0x0 }
  - { offsetInCU: 0x2E8, offset: 0x2AFC3, size: 0x4, addend: 0x0, symName: __ZL5file0, symObjAddr: 0x194, symBinAddr: 0x6DBC, symSize: 0x0 }
  - { offsetInCU: 0x30B, offset: 0x2AFE6, size: 0x4, addend: 0x0, symName: __ZL5file1, symObjAddr: 0x2EE, symBinAddr: 0x6F16, symSize: 0x0 }
  - { offsetInCU: 0x32E, offset: 0x2B009, size: 0x4, addend: 0x0, symName: __ZL5file2, symObjAddr: 0x449, symBinAddr: 0x7071, symSize: 0x0 }
  - { offsetInCU: 0x351, offset: 0x2B02C, size: 0x4, addend: 0x0, symName: __ZL5file3, symObjAddr: 0x5B1, symBinAddr: 0x71D9, symSize: 0x0 }
  - { offsetInCU: 0x374, offset: 0x2B04F, size: 0x4, addend: 0x0, symName: __ZL8layouts0, symObjAddr: 0x74, symBinAddr: 0x6C9C, symSize: 0x0 }
  - { offsetInCU: 0x38A, offset: 0x2B065, size: 0x4, addend: 0x0, symName: __ZL5file4, symObjAddr: 0x6FF, symBinAddr: 0x7327, symSize: 0x0 }
  - { offsetInCU: 0x3AC, offset: 0x2B087, size: 0x4, addend: 0x0, symName: __ZL5file5, symObjAddr: 0x7DD, symBinAddr: 0x7405, symSize: 0x0 }
  - { offsetInCU: 0x3CF, offset: 0x2B0AA, size: 0x4, addend: 0x0, symName: __ZL5file6, symObjAddr: 0x96C, symBinAddr: 0x7594, symSize: 0x0 }
  - { offsetInCU: 0x3F2, offset: 0x2B0CD, size: 0x4, addend: 0x0, symName: __ZL5file7, symObjAddr: 0xDAB, symBinAddr: 0x79D3, symSize: 0x0 }
  - { offsetInCU: 0x414, offset: 0x2B0EF, size: 0x4, addend: 0x0, symName: __ZL8patches0, symObjAddr: 0x19D728, symBinAddr: 0x1A42C8, symSize: 0x0 }
  - { offsetInCU: 0x436, offset: 0x2B111, size: 0x4, addend: 0x0, symName: __ZL9patchBuf0, symObjAddr: 0xE7C, symBinAddr: 0x7AA4, symSize: 0x0 }
  - { offsetInCU: 0x458, offset: 0x2B133, size: 0x4, addend: 0x0, symName: __ZL9patchBuf1, symObjAddr: 0xE84, symBinAddr: 0x7AAC, symSize: 0x0 }
  - { offsetInCU: 0x46E, offset: 0x2B149, size: 0x4, addend: 0x0, symName: __ZL9patchBuf2, symObjAddr: 0xE8C, symBinAddr: 0x7AB4, symSize: 0x0 }
  - { offsetInCU: 0x484, offset: 0x2B15F, size: 0x4, addend: 0x0, symName: __ZL9patchBuf3, symObjAddr: 0xE94, symBinAddr: 0x7ABC, symSize: 0x0 }
  - { offsetInCU: 0x49A, offset: 0x2B175, size: 0x4, addend: 0x0, symName: __ZL9patchBuf4, symObjAddr: 0xE9C, symBinAddr: 0x7AC4, symSize: 0x0 }
  - { offsetInCU: 0x4B0, offset: 0x2B18B, size: 0x4, addend: 0x0, symName: __ZL9patchBuf5, symObjAddr: 0xEA4, symBinAddr: 0x7ACC, symSize: 0x0 }
  - { offsetInCU: 0x4C6, offset: 0x2B1A1, size: 0x4, addend: 0x0, symName: __ZL9patchBuf6, symObjAddr: 0xEAC, symBinAddr: 0x7AD4, symSize: 0x0 }
  - { offsetInCU: 0x4DC, offset: 0x2B1B7, size: 0x4, addend: 0x0, symName: __ZL9patchBuf7, symObjAddr: 0xEB4, symBinAddr: 0x7ADC, symSize: 0x0 }
  - { offsetInCU: 0x4F2, offset: 0x2B1CD, size: 0x4, addend: 0x0, symName: __ZL9patchBuf8, symObjAddr: 0xEBC, symBinAddr: 0x7AE4, symSize: 0x0 }
  - { offsetInCU: 0x514, offset: 0x2B1EF, size: 0x4, addend: 0x0, symName: __ZL9patchBuf9, symObjAddr: 0xEC0, symBinAddr: 0x7AE8, symSize: 0x0 }
  - { offsetInCU: 0x52A, offset: 0x2B205, size: 0x4, addend: 0x0, symName: __ZL10patchBuf10, symObjAddr: 0xEC4, symBinAddr: 0x7AEC, symSize: 0x0 }
  - { offsetInCU: 0x540, offset: 0x2B21B, size: 0x4, addend: 0x0, symName: __ZL10patchBuf11, symObjAddr: 0xEC8, symBinAddr: 0x7AF0, symSize: 0x0 }
  - { offsetInCU: 0x556, offset: 0x2B231, size: 0x4, addend: 0x0, symName: __ZL10patchBuf12, symObjAddr: 0xECC, symBinAddr: 0x7AF4, symSize: 0x0 }
  - { offsetInCU: 0x56C, offset: 0x2B247, size: 0x4, addend: 0x0, symName: __ZL10revisions1, symObjAddr: 0xC4, symBinAddr: 0x6CEC, symSize: 0x0 }
  - { offsetInCU: 0x583, offset: 0x2B25E, size: 0x4, addend: 0x0, symName: __ZL10platforms1, symObjAddr: 0xC8, symBinAddr: 0x6CF0, symSize: 0x0 }
  - { offsetInCU: 0x599, offset: 0x2B274, size: 0x4, addend: 0x0, symName: __ZL5file8, symObjAddr: 0xED0, symBinAddr: 0x7AF8, symSize: 0x0 }
  - { offsetInCU: 0x5BD, offset: 0x2B298, size: 0x4, addend: 0x0, symName: __ZL5file9, symObjAddr: 0x10D3, symBinAddr: 0x7CFB, symSize: 0x0 }
  - { offsetInCU: 0x5E1, offset: 0x2B2BC, size: 0x4, addend: 0x0, symName: __ZL6file10, symObjAddr: 0x123C, symBinAddr: 0x7E64, symSize: 0x0 }
  - { offsetInCU: 0x605, offset: 0x2B2E0, size: 0x4, addend: 0x0, symName: __ZL8layouts1, symObjAddr: 0x118, symBinAddr: 0x6D40, symSize: 0x0 }
  - { offsetInCU: 0x61C, offset: 0x2B2F7, size: 0x4, addend: 0x0, symName: __ZL6file11, symObjAddr: 0x2676, symBinAddr: 0x929E, symSize: 0x0 }
  - { offsetInCU: 0x63F, offset: 0x2B31A, size: 0x4, addend: 0x0, symName: __ZL6file12, symObjAddr: 0x2743, symBinAddr: 0x936B, symSize: 0x0 }
  - { offsetInCU: 0x662, offset: 0x2B33D, size: 0x4, addend: 0x0, symName: __ZL6file13, symObjAddr: 0x280F, symBinAddr: 0x9437, symSize: 0x0 }
  - { offsetInCU: 0x686, offset: 0x2B361, size: 0x4, addend: 0x0, symName: __ZL6file14, symObjAddr: 0x2D80, symBinAddr: 0x99A8, symSize: 0x0 }
  - { offsetInCU: 0x6A9, offset: 0x2B384, size: 0x4, addend: 0x0, symName: __ZL8patches1, symObjAddr: 0x19D7EC, symBinAddr: 0x1A438C, symSize: 0x0 }
  - { offsetInCU: 0x6CC, offset: 0x2B3A7, size: 0x4, addend: 0x0, symName: __ZL10patchBuf13, symObjAddr: 0x2E3C, symBinAddr: 0x9A64, symSize: 0x0 }
  - { offsetInCU: 0x6E3, offset: 0x2B3BE, size: 0x4, addend: 0x0, symName: __ZL10patchBuf14, symObjAddr: 0x2E40, symBinAddr: 0x9A68, symSize: 0x0 }
  - { offsetInCU: 0x6FA, offset: 0x2B3D5, size: 0x4, addend: 0x0, symName: __ZL10revisions2, symObjAddr: 0x168, symBinAddr: 0x6D90, symSize: 0x0 }
  - { offsetInCU: 0x711, offset: 0x2B3EC, size: 0x4, addend: 0x0, symName: __ZL10platforms2, symObjAddr: 0x16C, symBinAddr: 0x6D94, symSize: 0x0 }
  - { offsetInCU: 0x734, offset: 0x2B40F, size: 0x4, addend: 0x0, symName: __ZL6file15, symObjAddr: 0x2E44, symBinAddr: 0x9A6C, symSize: 0x0 }
  - { offsetInCU: 0x758, offset: 0x2B433, size: 0x4, addend: 0x0, symName: __ZL8layouts2, symObjAddr: 0x180, symBinAddr: 0x6DA8, symSize: 0x0 }
  - { offsetInCU: 0x76F, offset: 0x2B44A, size: 0x4, addend: 0x0, symName: __ZL6file16, symObjAddr: 0x2F91, symBinAddr: 0x9BB9, symSize: 0x0 }
  - { offsetInCU: 0x792, offset: 0x2B46D, size: 0x4, addend: 0x0, symName: __ZL8patches2, symObjAddr: 0x19D8CC, symBinAddr: 0x1A446C, symSize: 0x0 }
  - { offsetInCU: 0x7A9, offset: 0x2B484, size: 0x4, addend: 0x0, symName: __ZL10patchBuf15, symObjAddr: 0x3053, symBinAddr: 0x9C7B, symSize: 0x0 }
  - { offsetInCU: 0x7C0, offset: 0x2B49B, size: 0x4, addend: 0x0, symName: __ZL19codecModCirrusLogic, symObjAddr: 0x19B6EC, symBinAddr: 0x1A228C, symSize: 0x0 }
  - { offsetInCU: 0x7D7, offset: 0x2B4B2, size: 0x4, addend: 0x0, symName: __ZL10revisions3, symObjAddr: 0x3058, symBinAddr: 0x9C80, symSize: 0x0 }
  - { offsetInCU: 0x7EE, offset: 0x2B4C9, size: 0x4, addend: 0x0, symName: __ZL10platforms3, symObjAddr: 0x305C, symBinAddr: 0x9C84, symSize: 0x0 }
  - { offsetInCU: 0x805, offset: 0x2B4E0, size: 0x4, addend: 0x0, symName: __ZL6file17, symObjAddr: 0x34CC, symBinAddr: 0xA0F4, symSize: 0x0 }
  - { offsetInCU: 0x829, offset: 0x2B504, size: 0x4, addend: 0x0, symName: __ZL8layouts3, symObjAddr: 0x3070, symBinAddr: 0x9C98, symSize: 0x0 }
  - { offsetInCU: 0x840, offset: 0x2B51B, size: 0x4, addend: 0x0, symName: __ZL6file18, symObjAddr: 0x400A, symBinAddr: 0xAC32, symSize: 0x0 }
  - { offsetInCU: 0x864, offset: 0x2B53F, size: 0x4, addend: 0x0, symName: __ZL8patches3, symObjAddr: 0x19D990, symBinAddr: 0x1A4530, symSize: 0x0 }
  - { offsetInCU: 0x887, offset: 0x2B562, size: 0x4, addend: 0x0, symName: __ZL10patchBuf16, symObjAddr: 0x438D, symBinAddr: 0xAFB5, symSize: 0x0 }
  - { offsetInCU: 0x89E, offset: 0x2B579, size: 0x4, addend: 0x0, symName: __ZL10revisions4, symObjAddr: 0x3084, symBinAddr: 0x9CAC, symSize: 0x0 }
  - { offsetInCU: 0x8C1, offset: 0x2B59C, size: 0x4, addend: 0x0, symName: __ZL10platforms4, symObjAddr: 0x3090, symBinAddr: 0x9CB8, symSize: 0x0 }
  - { offsetInCU: 0x8E4, offset: 0x2B5BF, size: 0x4, addend: 0x0, symName: __ZL6file19, symObjAddr: 0x4391, symBinAddr: 0xAFB9, symSize: 0x0 }
  - { offsetInCU: 0x908, offset: 0x2B5E3, size: 0x4, addend: 0x0, symName: __ZL8layouts4, symObjAddr: 0x3298, symBinAddr: 0x9EC0, symSize: 0x0 }
  - { offsetInCU: 0x91F, offset: 0x2B5FA, size: 0x4, addend: 0x0, symName: __ZL6file20, symObjAddr: 0x51A6, symBinAddr: 0xBDCE, symSize: 0x0 }
  - { offsetInCU: 0x943, offset: 0x2B61E, size: 0x4, addend: 0x0, symName: __ZL6file21, symObjAddr: 0x6453, symBinAddr: 0xD07B, symSize: 0x0 }
  - { offsetInCU: 0x967, offset: 0x2B642, size: 0x4, addend: 0x0, symName: __ZL6file22, symObjAddr: 0x72DC, symBinAddr: 0xDF04, symSize: 0x0 }
  - { offsetInCU: 0x98B, offset: 0x2B666, size: 0x4, addend: 0x0, symName: __ZL6file23, symObjAddr: 0x811D, symBinAddr: 0xED45, symSize: 0x0 }
  - { offsetInCU: 0x9AF, offset: 0x2B68A, size: 0x4, addend: 0x0, symName: __ZL6file24, symObjAddr: 0x8AA9, symBinAddr: 0xF6D1, symSize: 0x0 }
  - { offsetInCU: 0x9D3, offset: 0x2B6AE, size: 0x4, addend: 0x0, symName: __ZL6file25, symObjAddr: 0x9530, symBinAddr: 0x10158, symSize: 0x0 }
  - { offsetInCU: 0x9F7, offset: 0x2B6D2, size: 0x4, addend: 0x0, symName: __ZL6file26, symObjAddr: 0xA13B, symBinAddr: 0x10D63, symSize: 0x0 }
  - { offsetInCU: 0xA1B, offset: 0x2B6F6, size: 0x4, addend: 0x0, symName: __ZL6file27, symObjAddr: 0xAE7E, symBinAddr: 0x11AA6, symSize: 0x0 }
  - { offsetInCU: 0xA3F, offset: 0x2B71A, size: 0x4, addend: 0x0, symName: __ZL6file28, symObjAddr: 0xC2B6, symBinAddr: 0x12EDE, symSize: 0x0 }
  - { offsetInCU: 0xA56, offset: 0x2B731, size: 0x4, addend: 0x0, symName: __ZL6file29, symObjAddr: 0xD6F0, symBinAddr: 0x14318, symSize: 0x0 }
  - { offsetInCU: 0xA7A, offset: 0x2B755, size: 0x4, addend: 0x0, symName: __ZL6file30, symObjAddr: 0xEA16, symBinAddr: 0x1563E, symSize: 0x0 }
  - { offsetInCU: 0xA9E, offset: 0x2B779, size: 0x4, addend: 0x0, symName: __ZL6file31, symObjAddr: 0xF98F, symBinAddr: 0x165B7, symSize: 0x0 }
  - { offsetInCU: 0xAC2, offset: 0x2B79D, size: 0x4, addend: 0x0, symName: __ZL6file32, symObjAddr: 0x10C2E, symBinAddr: 0x17856, symSize: 0x0 }
  - { offsetInCU: 0xAE6, offset: 0x2B7C1, size: 0x4, addend: 0x0, symName: __ZL6file33, symObjAddr: 0x11A2C, symBinAddr: 0x18654, symSize: 0x0 }
  - { offsetInCU: 0xB0A, offset: 0x2B7E5, size: 0x4, addend: 0x0, symName: __ZL6file34, symObjAddr: 0x12E13, symBinAddr: 0x19A3B, symSize: 0x0 }
  - { offsetInCU: 0xB2E, offset: 0x2B809, size: 0x4, addend: 0x0, symName: __ZL6file35, symObjAddr: 0x141E3, symBinAddr: 0x1AE0B, symSize: 0x0 }
  - { offsetInCU: 0xB52, offset: 0x2B82D, size: 0x4, addend: 0x0, symName: __ZL6file36, symObjAddr: 0x152D8, symBinAddr: 0x1BF00, symSize: 0x0 }
  - { offsetInCU: 0xB76, offset: 0x2B851, size: 0x4, addend: 0x0, symName: __ZL6file37, symObjAddr: 0x15E99, symBinAddr: 0x1CAC1, symSize: 0x0 }
  - { offsetInCU: 0xB9A, offset: 0x2B875, size: 0x4, addend: 0x0, symName: __ZL6file38, symObjAddr: 0x16A44, symBinAddr: 0x1D66C, symSize: 0x0 }
  - { offsetInCU: 0xBBE, offset: 0x2B899, size: 0x4, addend: 0x0, symName: __ZL6file39, symObjAddr: 0x175F1, symBinAddr: 0x1E219, symSize: 0x0 }
  - { offsetInCU: 0xBD5, offset: 0x2B8B0, size: 0x4, addend: 0x0, symName: __ZL6file40, symObjAddr: 0x181B2, symBinAddr: 0x1EDDA, symSize: 0x0 }
  - { offsetInCU: 0xBEC, offset: 0x2B8C7, size: 0x4, addend: 0x0, symName: __ZL6file41, symObjAddr: 0x18D5F, symBinAddr: 0x1F987, symSize: 0x0 }
  - { offsetInCU: 0xC10, offset: 0x2B8EB, size: 0x4, addend: 0x0, symName: __ZL6file42, symObjAddr: 0x19C2D, symBinAddr: 0x20855, symSize: 0x0 }
  - { offsetInCU: 0xC34, offset: 0x2B90F, size: 0x4, addend: 0x0, symName: __ZL6file43, symObjAddr: 0x1A7F8, symBinAddr: 0x21420, symSize: 0x0 }
  - { offsetInCU: 0xC58, offset: 0x2B933, size: 0x4, addend: 0x0, symName: __ZL6file44, symObjAddr: 0x1AE5E, symBinAddr: 0x21A86, symSize: 0x0 }
  - { offsetInCU: 0xC7C, offset: 0x2B957, size: 0x4, addend: 0x0, symName: __ZL6file45, symObjAddr: 0x1B609, symBinAddr: 0x22231, symSize: 0x0 }
  - { offsetInCU: 0xC93, offset: 0x2B96E, size: 0x4, addend: 0x0, symName: __ZL8patches4, symObjAddr: 0x1E052C, symBinAddr: 0x1AA6F0, symSize: 0x0 }
  - { offsetInCU: 0xCB6, offset: 0x2B991, size: 0x4, addend: 0x0, symName: __ZL10revisions5, symObjAddr: 0x34A0, symBinAddr: 0xA0C8, symSize: 0x0 }
  - { offsetInCU: 0xCCD, offset: 0x2B9A8, size: 0x4, addend: 0x0, symName: __ZL10platforms5, symObjAddr: 0x34A4, symBinAddr: 0xA0CC, symSize: 0x0 }
  - { offsetInCU: 0xCE4, offset: 0x2B9BF, size: 0x4, addend: 0x0, symName: __ZL6file46, symObjAddr: 0x1BDB4, symBinAddr: 0x229DC, symSize: 0x0 }
  - { offsetInCU: 0xD08, offset: 0x2B9E3, size: 0x4, addend: 0x0, symName: __ZL8layouts5, symObjAddr: 0x34B8, symBinAddr: 0xA0E0, symSize: 0x0 }
  - { offsetInCU: 0xD1F, offset: 0x2B9FA, size: 0x4, addend: 0x0, symName: __ZL6file47, symObjAddr: 0x1C7EF, symBinAddr: 0x23417, symSize: 0x0 }
  - { offsetInCU: 0xD43, offset: 0x2BA1E, size: 0x4, addend: 0x0, symName: __ZL8patches5, symObjAddr: 0x19DA38, symBinAddr: 0x1A45D8, symSize: 0x0 }
  - { offsetInCU: 0xD66, offset: 0x2BA41, size: 0x4, addend: 0x0, symName: __ZL10patchBuf17, symObjAddr: 0x1D5AF, symBinAddr: 0x241D7, symSize: 0x0 }
  - { offsetInCU: 0xD7D, offset: 0x2BA58, size: 0x4, addend: 0x0, symName: __ZL10patchBuf18, symObjAddr: 0x1D5B3, symBinAddr: 0x241DB, symSize: 0x0 }
  - { offsetInCU: 0xD94, offset: 0x2BA6F, size: 0x4, addend: 0x0, symName: __ZL10patchBuf19, symObjAddr: 0x1D5B7, symBinAddr: 0x241DF, symSize: 0x0 }
  - { offsetInCU: 0xDAB, offset: 0x2BA86, size: 0x4, addend: 0x0, symName: __ZL10patchBuf20, symObjAddr: 0x1D5BB, symBinAddr: 0x241E3, symSize: 0x0 }
  - { offsetInCU: 0xDC2, offset: 0x2BA9D, size: 0x4, addend: 0x0, symName: __ZL10patchBuf21, symObjAddr: 0x1D5BF, symBinAddr: 0x241E7, symSize: 0x0 }
  - { offsetInCU: 0xDD9, offset: 0x2BAB4, size: 0x4, addend: 0x0, symName: __ZL21codecModAnalogDevices, symObjAddr: 0x19B764, symBinAddr: 0x1A2304, symSize: 0x0 }
  - { offsetInCU: 0xDFC, offset: 0x2BAD7, size: 0x4, addend: 0x0, symName: __ZL10platforms6, symObjAddr: 0x1D5C4, symBinAddr: 0x241EC, symSize: 0x0 }
  - { offsetInCU: 0xE13, offset: 0x2BAEE, size: 0x4, addend: 0x0, symName: __ZL6file48, symObjAddr: 0x1D784, symBinAddr: 0x243AC, symSize: 0x0 }
  - { offsetInCU: 0xE37, offset: 0x2BB12, size: 0x4, addend: 0x0, symName: __ZL8layouts6, symObjAddr: 0x1D5D8, symBinAddr: 0x24200, symSize: 0x0 }
  - { offsetInCU: 0xE4E, offset: 0x2BB29, size: 0x4, addend: 0x0, symName: __ZL6file49, symObjAddr: 0x1D92E, symBinAddr: 0x24556, symSize: 0x0 }
  - { offsetInCU: 0xE72, offset: 0x2BB4D, size: 0x4, addend: 0x0, symName: __ZL8patches6, symObjAddr: 0x19DB6C, symBinAddr: 0x1A470C, symSize: 0x0 }
  - { offsetInCU: 0xE89, offset: 0x2BB64, size: 0x4, addend: 0x0, symName: __ZL10patchBuf22, symObjAddr: 0x1DCBB, symBinAddr: 0x248E3, symSize: 0x0 }
  - { offsetInCU: 0xEA0, offset: 0x2BB7B, size: 0x4, addend: 0x0, symName: __ZL10platforms7, symObjAddr: 0x1D5EC, symBinAddr: 0x24214, symSize: 0x0 }
  - { offsetInCU: 0xEB7, offset: 0x2BB92, size: 0x4, addend: 0x0, symName: __ZL6file50, symObjAddr: 0x1DCBF, symBinAddr: 0x248E7, symSize: 0x0 }
  - { offsetInCU: 0xEDB, offset: 0x2BBB6, size: 0x4, addend: 0x0, symName: __ZL8layouts7, symObjAddr: 0x1D600, symBinAddr: 0x24228, symSize: 0x0 }
  - { offsetInCU: 0xEF2, offset: 0x2BBCD, size: 0x4, addend: 0x0, symName: __ZL6file51, symObjAddr: 0x1DE00, symBinAddr: 0x24A28, symSize: 0x0 }
  - { offsetInCU: 0xF16, offset: 0x2BBF1, size: 0x4, addend: 0x0, symName: __ZL8patches7, symObjAddr: 0x19DC30, symBinAddr: 0x1A47D0, symSize: 0x0 }
  - { offsetInCU: 0xF2D, offset: 0x2BC08, size: 0x4, addend: 0x0, symName: __ZL10patchBuf23, symObjAddr: 0x1E16A, symBinAddr: 0x24D92, symSize: 0x0 }
  - { offsetInCU: 0xF44, offset: 0x2BC1F, size: 0x4, addend: 0x0, symName: __ZL10patchBuf24, symObjAddr: 0x1E16E, symBinAddr: 0x24D96, symSize: 0x0 }
  - { offsetInCU: 0xF5B, offset: 0x2BC36, size: 0x4, addend: 0x0, symName: __ZL10platforms8, symObjAddr: 0x1D614, symBinAddr: 0x2423C, symSize: 0x0 }
  - { offsetInCU: 0xF7E, offset: 0x2BC59, size: 0x4, addend: 0x0, symName: __ZL6file52, symObjAddr: 0x1E172, symBinAddr: 0x24D9A, symSize: 0x0 }
  - { offsetInCU: 0xFA2, offset: 0x2BC7D, size: 0x4, addend: 0x0, symName: __ZL8layouts8, symObjAddr: 0x1D63C, symBinAddr: 0x24264, symSize: 0x0 }
  - { offsetInCU: 0xFB9, offset: 0x2BC94, size: 0x4, addend: 0x0, symName: __ZL6file53, symObjAddr: 0x1E38B, symBinAddr: 0x24FB3, symSize: 0x0 }
  - { offsetInCU: 0xFDD, offset: 0x2BCB8, size: 0x4, addend: 0x0, symName: __ZL6file54, symObjAddr: 0x1E983, symBinAddr: 0x255AB, symSize: 0x0 }
  - { offsetInCU: 0xFF4, offset: 0x2BCCF, size: 0x4, addend: 0x0, symName: __ZL8patches8, symObjAddr: 0x19DD10, symBinAddr: 0x1A48B0, symSize: 0x0 }
  - { offsetInCU: 0x100B, offset: 0x2BCE6, size: 0x4, addend: 0x0, symName: __ZL10patchBuf25, symObjAddr: 0x1EF7B, symBinAddr: 0x25BA3, symSize: 0x0 }
  - { offsetInCU: 0x1022, offset: 0x2BCFD, size: 0x4, addend: 0x0, symName: __ZL10revisions6, symObjAddr: 0x1D664, symBinAddr: 0x2428C, symSize: 0x0 }
  - { offsetInCU: 0x1039, offset: 0x2BD14, size: 0x4, addend: 0x0, symName: __ZL10platforms9, symObjAddr: 0x1D668, symBinAddr: 0x24290, symSize: 0x0 }
  - { offsetInCU: 0x105C, offset: 0x2BD37, size: 0x4, addend: 0x0, symName: __ZL6file55, symObjAddr: 0x1EF7F, symBinAddr: 0x25BA7, symSize: 0x0 }
  - { offsetInCU: 0x1080, offset: 0x2BD5B, size: 0x4, addend: 0x0, symName: __ZL6file56, symObjAddr: 0x1F0C2, symBinAddr: 0x25CEA, symSize: 0x0 }
  - { offsetInCU: 0x10A4, offset: 0x2BD7F, size: 0x4, addend: 0x0, symName: __ZL6file57, symObjAddr: 0x1F208, symBinAddr: 0x25E30, symSize: 0x0 }
  - { offsetInCU: 0x10C8, offset: 0x2BDA3, size: 0x4, addend: 0x0, symName: __ZL8layouts9, symObjAddr: 0x1D6A4, symBinAddr: 0x242CC, symSize: 0x0 }
  - { offsetInCU: 0x10DF, offset: 0x2BDBA, size: 0x4, addend: 0x0, symName: __ZL6file58, symObjAddr: 0x1F361, symBinAddr: 0x25F89, symSize: 0x0 }
  - { offsetInCU: 0x1103, offset: 0x2BDDE, size: 0x4, addend: 0x0, symName: __ZL6file59, symObjAddr: 0x1FA1D, symBinAddr: 0x26645, symSize: 0x0 }
  - { offsetInCU: 0x1127, offset: 0x2BE02, size: 0x4, addend: 0x0, symName: __ZL6file60, symObjAddr: 0x200D8, symBinAddr: 0x26D00, symSize: 0x0 }
  - { offsetInCU: 0x113E, offset: 0x2BE19, size: 0x4, addend: 0x0, symName: __ZL8patches9, symObjAddr: 0x19DDD4, symBinAddr: 0x1A4974, symSize: 0x0 }
  - { offsetInCU: 0x1155, offset: 0x2BE30, size: 0x4, addend: 0x0, symName: __ZL10patchBuf26, symObjAddr: 0x20793, symBinAddr: 0x273BB, symSize: 0x0 }
  - { offsetInCU: 0x116C, offset: 0x2BE47, size: 0x4, addend: 0x0, symName: __ZL11platforms10, symObjAddr: 0x1D6E0, symBinAddr: 0x24308, symSize: 0x0 }
  - { offsetInCU: 0x1183, offset: 0x2BE5E, size: 0x4, addend: 0x0, symName: __ZL6file61, symObjAddr: 0x20797, symBinAddr: 0x273BF, symSize: 0x0 }
  - { offsetInCU: 0x119A, offset: 0x2BE75, size: 0x4, addend: 0x0, symName: __ZL6file62, symObjAddr: 0x209B0, symBinAddr: 0x275D8, symSize: 0x0 }
  - { offsetInCU: 0x11BE, offset: 0x2BE99, size: 0x4, addend: 0x0, symName: __ZL9layouts10, symObjAddr: 0x1D71C, symBinAddr: 0x24344, symSize: 0x0 }
  - { offsetInCU: 0x11D5, offset: 0x2BEB0, size: 0x4, addend: 0x0, symName: __ZL6file63, symObjAddr: 0x20B66, symBinAddr: 0x2778E, symSize: 0x0 }
  - { offsetInCU: 0x11F9, offset: 0x2BED4, size: 0x4, addend: 0x0, symName: __ZL6file64, symObjAddr: 0x2115D, symBinAddr: 0x27D85, symSize: 0x0 }
  - { offsetInCU: 0x1210, offset: 0x2BEEB, size: 0x4, addend: 0x0, symName: __ZL6file65, symObjAddr: 0x21754, symBinAddr: 0x2837C, symSize: 0x0 }
  - { offsetInCU: 0x1234, offset: 0x2BF0F, size: 0x4, addend: 0x0, symName: __ZL9patches10, symObjAddr: 0x19DE98, symBinAddr: 0x1A4A38, symSize: 0x0 }
  - { offsetInCU: 0x124B, offset: 0x2BF26, size: 0x4, addend: 0x0, symName: __ZL10revisions7, symObjAddr: 0x1D758, symBinAddr: 0x24380, symSize: 0x0 }
  - { offsetInCU: 0x1262, offset: 0x2BF3D, size: 0x4, addend: 0x0, symName: __ZL11platforms11, symObjAddr: 0x1D75C, symBinAddr: 0x24384, symSize: 0x0 }
  - { offsetInCU: 0x1279, offset: 0x2BF54, size: 0x4, addend: 0x0, symName: __ZL6file66, symObjAddr: 0x21AE4, symBinAddr: 0x2870C, symSize: 0x0 }
  - { offsetInCU: 0x129D, offset: 0x2BF78, size: 0x4, addend: 0x0, symName: __ZL9layouts11, symObjAddr: 0x1D770, symBinAddr: 0x24398, symSize: 0x0 }
  - { offsetInCU: 0x12B4, offset: 0x2BF8F, size: 0x4, addend: 0x0, symName: __ZL6file67, symObjAddr: 0x21C43, symBinAddr: 0x2886B, symSize: 0x0 }
  - { offsetInCU: 0x12D8, offset: 0x2BFB3, size: 0x4, addend: 0x0, symName: __ZL9patches11, symObjAddr: 0x19DF40, symBinAddr: 0x1A4AE0, symSize: 0x0 }
  - { offsetInCU: 0x12EF, offset: 0x2BFCA, size: 0x4, addend: 0x0, symName: __ZL13codecModIntel, symObjAddr: 0x1E0520, symBinAddr: 0x1AA6E4, symSize: 0x0 }
  - { offsetInCU: 0x1306, offset: 0x2BFE1, size: 0x4, addend: 0x0, symName: __ZL16codecModConexant, symObjAddr: 0x19B854, symBinAddr: 0x1A23F4, symSize: 0x0 }
  - { offsetInCU: 0x1329, offset: 0x2C004, size: 0x4, addend: 0x0, symName: __ZL10revisions8, symObjAddr: 0x222F0, symBinAddr: 0x28F18, symSize: 0x0 }
  - { offsetInCU: 0x1340, offset: 0x2C01B, size: 0x4, addend: 0x0, symName: __ZL11platforms12, symObjAddr: 0x222F4, symBinAddr: 0x28F1C, symSize: 0x0 }
  - { offsetInCU: 0x1357, offset: 0x2C032, size: 0x4, addend: 0x0, symName: __ZL6file68, symObjAddr: 0x22A64, symBinAddr: 0x2968C, symSize: 0x0 }
  - { offsetInCU: 0x137B, offset: 0x2C056, size: 0x4, addend: 0x0, symName: __ZL9layouts12, symObjAddr: 0x22308, symBinAddr: 0x28F30, symSize: 0x0 }
  - { offsetInCU: 0x1392, offset: 0x2C06D, size: 0x4, addend: 0x0, symName: __ZL6file69, symObjAddr: 0x22BB7, symBinAddr: 0x297DF, symSize: 0x0 }
  - { offsetInCU: 0x13B6, offset: 0x2C091, size: 0x4, addend: 0x0, symName: __ZL9patches12, symObjAddr: 0x19E004, symBinAddr: 0x1A4BA4, symSize: 0x0 }
  - { offsetInCU: 0x13CD, offset: 0x2C0A8, size: 0x4, addend: 0x0, symName: __ZL10patchBuf27, symObjAddr: 0x23221, symBinAddr: 0x29E49, symSize: 0x0 }
  - { offsetInCU: 0x13E4, offset: 0x2C0BF, size: 0x4, addend: 0x0, symName: __ZL11platforms13, symObjAddr: 0x2231C, symBinAddr: 0x28F44, symSize: 0x0 }
  - { offsetInCU: 0x13FB, offset: 0x2C0D6, size: 0x4, addend: 0x0, symName: __ZL6file70, symObjAddr: 0x23225, symBinAddr: 0x29E4D, symSize: 0x0 }
  - { offsetInCU: 0x141F, offset: 0x2C0FA, size: 0x4, addend: 0x0, symName: __ZL6file71, symObjAddr: 0x23354, symBinAddr: 0x29F7C, symSize: 0x0 }
  - { offsetInCU: 0x1443, offset: 0x2C11E, size: 0x4, addend: 0x0, symName: __ZL9layouts13, symObjAddr: 0x22344, symBinAddr: 0x28F6C, symSize: 0x0 }
  - { offsetInCU: 0x145A, offset: 0x2C135, size: 0x4, addend: 0x0, symName: __ZL6file72, symObjAddr: 0x2347F, symBinAddr: 0x2A0A7, symSize: 0x0 }
  - { offsetInCU: 0x147E, offset: 0x2C159, size: 0x4, addend: 0x0, symName: __ZL6file73, symObjAddr: 0x235C1, symBinAddr: 0x2A1E9, symSize: 0x0 }
  - { offsetInCU: 0x1495, offset: 0x2C170, size: 0x4, addend: 0x0, symName: __ZL9patches13, symObjAddr: 0x19E0C8, symBinAddr: 0x1A4C68, symSize: 0x0 }
  - { offsetInCU: 0x14AC, offset: 0x2C187, size: 0x4, addend: 0x0, symName: __ZL10patchBuf28, symObjAddr: 0x23703, symBinAddr: 0x2A32B, symSize: 0x0 }
  - { offsetInCU: 0x14C3, offset: 0x2C19E, size: 0x4, addend: 0x0, symName: __ZL10patchBuf29, symObjAddr: 0x23707, symBinAddr: 0x2A32F, symSize: 0x0 }
  - { offsetInCU: 0x14E6, offset: 0x2C1C1, size: 0x4, addend: 0x0, symName: __ZL10patchBuf30, symObjAddr: 0x2370C, symBinAddr: 0x2A334, symSize: 0x0 }
  - { offsetInCU: 0x14FD, offset: 0x2C1D8, size: 0x4, addend: 0x0, symName: __ZL11platforms14, symObjAddr: 0x2236C, symBinAddr: 0x28F94, symSize: 0x0 }
  - { offsetInCU: 0x1514, offset: 0x2C1EF, size: 0x4, addend: 0x0, symName: __ZL6file74, symObjAddr: 0x23711, symBinAddr: 0x2A339, symSize: 0x0 }
  - { offsetInCU: 0x1538, offset: 0x2C213, size: 0x4, addend: 0x0, symName: __ZL9layouts14, symObjAddr: 0x22380, symBinAddr: 0x28FA8, symSize: 0x0 }
  - { offsetInCU: 0x154F, offset: 0x2C22A, size: 0x4, addend: 0x0, symName: __ZL6file75, symObjAddr: 0x23867, symBinAddr: 0x2A48F, symSize: 0x0 }
  - { offsetInCU: 0x1572, offset: 0x2C24D, size: 0x4, addend: 0x0, symName: __ZL9patches14, symObjAddr: 0x19E18C, symBinAddr: 0x1A4D2C, symSize: 0x0 }
  - { offsetInCU: 0x1589, offset: 0x2C264, size: 0x4, addend: 0x0, symName: __ZL10patchBuf31, symObjAddr: 0x23943, symBinAddr: 0x2A56B, symSize: 0x0 }
  - { offsetInCU: 0x15A0, offset: 0x2C27B, size: 0x4, addend: 0x0, symName: __ZL11platforms15, symObjAddr: 0x22394, symBinAddr: 0x28FBC, symSize: 0x0 }
  - { offsetInCU: 0x15B7, offset: 0x2C292, size: 0x4, addend: 0x0, symName: __ZL6file76, symObjAddr: 0x23947, symBinAddr: 0x2A56F, symSize: 0x0 }
  - { offsetInCU: 0x15CE, offset: 0x2C2A9, size: 0x4, addend: 0x0, symName: __ZL6file77, symObjAddr: 0x23A95, symBinAddr: 0x2A6BD, symSize: 0x0 }
  - { offsetInCU: 0x15F2, offset: 0x2C2CD, size: 0x4, addend: 0x0, symName: __ZL9layouts15, symObjAddr: 0x223BC, symBinAddr: 0x28FE4, symSize: 0x0 }
  - { offsetInCU: 0x1609, offset: 0x2C2E4, size: 0x4, addend: 0x0, symName: __ZL6file78, symObjAddr: 0x23BE5, symBinAddr: 0x2A80D, symSize: 0x0 }
  - { offsetInCU: 0x162D, offset: 0x2C308, size: 0x4, addend: 0x0, symName: __ZL6file79, symObjAddr: 0x23D55, symBinAddr: 0x2A97D, symSize: 0x0 }
  - { offsetInCU: 0x1651, offset: 0x2C32C, size: 0x4, addend: 0x0, symName: __ZL9patches15, symObjAddr: 0x19E26C, symBinAddr: 0x1A4E0C, symSize: 0x0 }
  - { offsetInCU: 0x1668, offset: 0x2C343, size: 0x4, addend: 0x0, symName: __ZL10patchBuf32, symObjAddr: 0x23EC7, symBinAddr: 0x2AAEF, symSize: 0x0 }
  - { offsetInCU: 0x167F, offset: 0x2C35A, size: 0x4, addend: 0x0, symName: __ZL11platforms16, symObjAddr: 0x223E4, symBinAddr: 0x2900C, symSize: 0x0 }
  - { offsetInCU: 0x1696, offset: 0x2C371, size: 0x4, addend: 0x0, symName: __ZL6file80, symObjAddr: 0x23ECB, symBinAddr: 0x2AAF3, symSize: 0x0 }
  - { offsetInCU: 0x16BA, offset: 0x2C395, size: 0x4, addend: 0x0, symName: __ZL6file81, symObjAddr: 0x23FE8, symBinAddr: 0x2AC10, symSize: 0x0 }
  - { offsetInCU: 0x16DE, offset: 0x2C3B9, size: 0x4, addend: 0x0, symName: __ZL9layouts16, symObjAddr: 0x2240C, symBinAddr: 0x29034, symSize: 0x0 }
  - { offsetInCU: 0x16F5, offset: 0x2C3D0, size: 0x4, addend: 0x0, symName: __ZL6file82, symObjAddr: 0x24108, symBinAddr: 0x2AD30, symSize: 0x0 }
  - { offsetInCU: 0x1719, offset: 0x2C3F4, size: 0x4, addend: 0x0, symName: __ZL6file83, symObjAddr: 0x2446A, symBinAddr: 0x2B092, symSize: 0x0 }
  - { offsetInCU: 0x173D, offset: 0x2C418, size: 0x4, addend: 0x0, symName: __ZL9patches16, symObjAddr: 0x19E330, symBinAddr: 0x1A4ED0, symSize: 0x0 }
  - { offsetInCU: 0x1754, offset: 0x2C42F, size: 0x4, addend: 0x0, symName: __ZL10patchBuf33, symObjAddr: 0x247D3, symBinAddr: 0x2B3FB, symSize: 0x0 }
  - { offsetInCU: 0x176B, offset: 0x2C446, size: 0x4, addend: 0x0, symName: __ZL10revisions9, symObjAddr: 0x22434, symBinAddr: 0x2905C, symSize: 0x0 }
  - { offsetInCU: 0x1782, offset: 0x2C45D, size: 0x4, addend: 0x0, symName: __ZL11platforms17, symObjAddr: 0x22438, symBinAddr: 0x29060, symSize: 0x0 }
  - { offsetInCU: 0x1799, offset: 0x2C474, size: 0x4, addend: 0x0, symName: __ZL6file84, symObjAddr: 0x247D7, symBinAddr: 0x2B3FF, symSize: 0x0 }
  - { offsetInCU: 0x17BD, offset: 0x2C498, size: 0x4, addend: 0x0, symName: __ZL6file85, symObjAddr: 0x2531A, symBinAddr: 0x2BF42, symSize: 0x0 }
  - { offsetInCU: 0x17D4, offset: 0x2C4AF, size: 0x4, addend: 0x0, symName: __ZL6file86, symObjAddr: 0x25E5D, symBinAddr: 0x2CA85, symSize: 0x0 }
  - { offsetInCU: 0x17F8, offset: 0x2C4D3, size: 0x4, addend: 0x0, symName: __ZL6file87, symObjAddr: 0x25F92, symBinAddr: 0x2CBBA, symSize: 0x0 }
  - { offsetInCU: 0x181C, offset: 0x2C4F7, size: 0x4, addend: 0x0, symName: __ZL9layouts17, symObjAddr: 0x22488, symBinAddr: 0x290B0, symSize: 0x0 }
  - { offsetInCU: 0x1833, offset: 0x2C50E, size: 0x4, addend: 0x0, symName: __ZL6file88, symObjAddr: 0x26ADE, symBinAddr: 0x2D706, symSize: 0x0 }
  - { offsetInCU: 0x1857, offset: 0x2C532, size: 0x4, addend: 0x0, symName: __ZL6file89, symObjAddr: 0x26E59, symBinAddr: 0x2DA81, symSize: 0x0 }
  - { offsetInCU: 0x187B, offset: 0x2C556, size: 0x4, addend: 0x0, symName: __ZL6file90, symObjAddr: 0x271D2, symBinAddr: 0x2DDFA, symSize: 0x0 }
  - { offsetInCU: 0x189F, offset: 0x2C57A, size: 0x4, addend: 0x0, symName: __ZL6file91, symObjAddr: 0x27570, symBinAddr: 0x2E198, symSize: 0x0 }
  - { offsetInCU: 0x18C3, offset: 0x2C59E, size: 0x4, addend: 0x0, symName: __ZL9patches17, symObjAddr: 0x19E3F4, symBinAddr: 0x1A4F94, symSize: 0x0 }
  - { offsetInCU: 0x18E6, offset: 0x2C5C1, size: 0x4, addend: 0x0, symName: __ZL10patchBuf34, symObjAddr: 0x278EF, symBinAddr: 0x2E517, symSize: 0x0 }
  - { offsetInCU: 0x18FD, offset: 0x2C5D8, size: 0x4, addend: 0x0, symName: __ZL11platforms18, symObjAddr: 0x224D8, symBinAddr: 0x29100, symSize: 0x0 }
  - { offsetInCU: 0x1914, offset: 0x2C5EF, size: 0x4, addend: 0x0, symName: __ZL6file92, symObjAddr: 0x278F3, symBinAddr: 0x2E51B, symSize: 0x0 }
  - { offsetInCU: 0x1938, offset: 0x2C613, size: 0x4, addend: 0x0, symName: __ZL6file93, symObjAddr: 0x27A25, symBinAddr: 0x2E64D, symSize: 0x0 }
  - { offsetInCU: 0x195C, offset: 0x2C637, size: 0x4, addend: 0x0, symName: __ZL6file94, symObjAddr: 0x27B85, symBinAddr: 0x2E7AD, symSize: 0x0 }
  - { offsetInCU: 0x1980, offset: 0x2C65B, size: 0x4, addend: 0x0, symName: __ZL6file95, symObjAddr: 0x27CB8, symBinAddr: 0x2E8E0, symSize: 0x0 }
  - { offsetInCU: 0x19A4, offset: 0x2C67F, size: 0x4, addend: 0x0, symName: __ZL9layouts18, symObjAddr: 0x22528, symBinAddr: 0x29150, symSize: 0x0 }
  - { offsetInCU: 0x19BB, offset: 0x2C696, size: 0x4, addend: 0x0, symName: __ZL6file96, symObjAddr: 0x27DF0, symBinAddr: 0x2EA18, symSize: 0x0 }
  - { offsetInCU: 0x19DF, offset: 0x2C6BA, size: 0x4, addend: 0x0, symName: __ZL6file97, symObjAddr: 0x28181, symBinAddr: 0x2EDA9, symSize: 0x0 }
  - { offsetInCU: 0x1A03, offset: 0x2C6DE, size: 0x4, addend: 0x0, symName: __ZL6file98, symObjAddr: 0x28979, symBinAddr: 0x2F5A1, symSize: 0x0 }
  - { offsetInCU: 0x1A27, offset: 0x2C702, size: 0x4, addend: 0x0, symName: __ZL6file99, symObjAddr: 0x28CEF, symBinAddr: 0x2F917, symSize: 0x0 }
  - { offsetInCU: 0x1A3E, offset: 0x2C719, size: 0x4, addend: 0x0, symName: __ZL9patches18, symObjAddr: 0x19E4F0, symBinAddr: 0x1A5090, symSize: 0x0 }
  - { offsetInCU: 0x1A55, offset: 0x2C730, size: 0x4, addend: 0x0, symName: __ZL10patchBuf35, symObjAddr: 0x2907F, symBinAddr: 0x2FCA7, symSize: 0x0 }
  - { offsetInCU: 0x1A6C, offset: 0x2C747, size: 0x4, addend: 0x0, symName: __ZL11platforms19, symObjAddr: 0x22578, symBinAddr: 0x291A0, symSize: 0x0 }
  - { offsetInCU: 0x1A83, offset: 0x2C75E, size: 0x4, addend: 0x0, symName: __ZL7file100, symObjAddr: 0x29083, symBinAddr: 0x2FCAB, symSize: 0x0 }
  - { offsetInCU: 0x1AA7, offset: 0x2C782, size: 0x4, addend: 0x0, symName: __ZL7file101, symObjAddr: 0x29B48, symBinAddr: 0x30770, symSize: 0x0 }
  - { offsetInCU: 0x1ACB, offset: 0x2C7A6, size: 0x4, addend: 0x0, symName: __ZL9layouts19, symObjAddr: 0x225A0, symBinAddr: 0x291C8, symSize: 0x0 }
  - { offsetInCU: 0x1AE2, offset: 0x2C7BD, size: 0x4, addend: 0x0, symName: __ZL7file102, symObjAddr: 0x2A6A1, symBinAddr: 0x312C9, symSize: 0x0 }
  - { offsetInCU: 0x1B06, offset: 0x2C7E1, size: 0x4, addend: 0x0, symName: __ZL7file103, symObjAddr: 0x2AA36, symBinAddr: 0x3165E, symSize: 0x0 }
  - { offsetInCU: 0x1B1D, offset: 0x2C7F8, size: 0x4, addend: 0x0, symName: __ZL9patches19, symObjAddr: 0x19E5D0, symBinAddr: 0x1A5170, symSize: 0x0 }
  - { offsetInCU: 0x1B34, offset: 0x2C80F, size: 0x4, addend: 0x0, symName: __ZL10patchBuf36, symObjAddr: 0x2ADB9, symBinAddr: 0x319E1, symSize: 0x0 }
  - { offsetInCU: 0x1B4B, offset: 0x2C826, size: 0x4, addend: 0x0, symName: __ZL11platforms20, symObjAddr: 0x225C8, symBinAddr: 0x291F0, symSize: 0x0 }
  - { offsetInCU: 0x1B62, offset: 0x2C83D, size: 0x4, addend: 0x0, symName: __ZL7file104, symObjAddr: 0x2ADBD, symBinAddr: 0x319E5, symSize: 0x0 }
  - { offsetInCU: 0x1B86, offset: 0x2C861, size: 0x4, addend: 0x0, symName: __ZL9layouts20, symObjAddr: 0x225DC, symBinAddr: 0x29204, symSize: 0x0 }
  - { offsetInCU: 0x1B9D, offset: 0x2C878, size: 0x4, addend: 0x0, symName: __ZL7file105, symObjAddr: 0x2AEE7, symBinAddr: 0x31B0F, symSize: 0x0 }
  - { offsetInCU: 0x1BC1, offset: 0x2C89C, size: 0x4, addend: 0x0, symName: __ZL9patches20, symObjAddr: 0x19E6CC, symBinAddr: 0x1A526C, symSize: 0x0 }
  - { offsetInCU: 0x1BD8, offset: 0x2C8B3, size: 0x4, addend: 0x0, symName: __ZL10patchBuf37, symObjAddr: 0x2B28C, symBinAddr: 0x31EB4, symSize: 0x0 }
  - { offsetInCU: 0x1BEF, offset: 0x2C8CA, size: 0x4, addend: 0x0, symName: __ZL11platforms21, symObjAddr: 0x225F0, symBinAddr: 0x29218, symSize: 0x0 }
  - { offsetInCU: 0x1C06, offset: 0x2C8E1, size: 0x4, addend: 0x0, symName: __ZL7file106, symObjAddr: 0x2B290, symBinAddr: 0x31EB8, symSize: 0x0 }
  - { offsetInCU: 0x1C1D, offset: 0x2C8F8, size: 0x4, addend: 0x0, symName: __ZL7file107, symObjAddr: 0x2B3BF, symBinAddr: 0x31FE7, symSize: 0x0 }
  - { offsetInCU: 0x1C41, offset: 0x2C91C, size: 0x4, addend: 0x0, symName: __ZL7file108, symObjAddr: 0x2B4DA, symBinAddr: 0x32102, symSize: 0x0 }
  - { offsetInCU: 0x1C58, offset: 0x2C933, size: 0x4, addend: 0x0, symName: __ZL9layouts21, symObjAddr: 0x2262C, symBinAddr: 0x29254, symSize: 0x0 }
  - { offsetInCU: 0x1C6F, offset: 0x2C94A, size: 0x4, addend: 0x0, symName: __ZL7file109, symObjAddr: 0x2B609, symBinAddr: 0x32231, symSize: 0x0 }
  - { offsetInCU: 0x1C93, offset: 0x2C96E, size: 0x4, addend: 0x0, symName: __ZL7file110, symObjAddr: 0x2B9A0, symBinAddr: 0x325C8, symSize: 0x0 }
  - { offsetInCU: 0x1CAA, offset: 0x2C985, size: 0x4, addend: 0x0, symName: __ZL7file111, symObjAddr: 0x2BD37, symBinAddr: 0x3295F, symSize: 0x0 }
  - { offsetInCU: 0x1CCE, offset: 0x2C9A9, size: 0x4, addend: 0x0, symName: __ZL9patches21, symObjAddr: 0x19E790, symBinAddr: 0x1A5330, symSize: 0x0 }
  - { offsetInCU: 0x1CE5, offset: 0x2C9C0, size: 0x4, addend: 0x0, symName: __ZL10patchBuf38, symObjAddr: 0x2C0CD, symBinAddr: 0x32CF5, symSize: 0x0 }
  - { offsetInCU: 0x1CFC, offset: 0x2C9D7, size: 0x4, addend: 0x0, symName: __ZL11platforms22, symObjAddr: 0x22668, symBinAddr: 0x29290, symSize: 0x0 }
  - { offsetInCU: 0x1D13, offset: 0x2C9EE, size: 0x4, addend: 0x0, symName: __ZL7file112, symObjAddr: 0x2C0D1, symBinAddr: 0x32CF9, symSize: 0x0 }
  - { offsetInCU: 0x1D37, offset: 0x2CA12, size: 0x4, addend: 0x0, symName: __ZL9layouts22, symObjAddr: 0x2267C, symBinAddr: 0x292A4, symSize: 0x0 }
  - { offsetInCU: 0x1D4E, offset: 0x2CA29, size: 0x4, addend: 0x0, symName: __ZL7file113, symObjAddr: 0x2C20B, symBinAddr: 0x32E33, symSize: 0x0 }
  - { offsetInCU: 0x1D72, offset: 0x2CA4D, size: 0x4, addend: 0x0, symName: __ZL9patches22, symObjAddr: 0x19E854, symBinAddr: 0x1A53F4, symSize: 0x0 }
  - { offsetInCU: 0x1D89, offset: 0x2CA64, size: 0x4, addend: 0x0, symName: __ZL10patchBuf39, symObjAddr: 0x2C503, symBinAddr: 0x3312B, symSize: 0x0 }
  - { offsetInCU: 0x1DA0, offset: 0x2CA7B, size: 0x4, addend: 0x0, symName: __ZL11platforms23, symObjAddr: 0x22690, symBinAddr: 0x292B8, symSize: 0x0 }
  - { offsetInCU: 0x1DB7, offset: 0x2CA92, size: 0x4, addend: 0x0, symName: __ZL7file114, symObjAddr: 0x2C507, symBinAddr: 0x3312F, symSize: 0x0 }
  - { offsetInCU: 0x1DDB, offset: 0x2CAB6, size: 0x4, addend: 0x0, symName: __ZL7file115, symObjAddr: 0x2C644, symBinAddr: 0x3326C, symSize: 0x0 }
  - { offsetInCU: 0x1DF2, offset: 0x2CACD, size: 0x4, addend: 0x0, symName: __ZL9layouts23, symObjAddr: 0x226B8, symBinAddr: 0x292E0, symSize: 0x0 }
  - { offsetInCU: 0x1E09, offset: 0x2CAE4, size: 0x4, addend: 0x0, symName: __ZL7file116, symObjAddr: 0x2C786, symBinAddr: 0x333AE, symSize: 0x0 }
  - { offsetInCU: 0x1E20, offset: 0x2CAFB, size: 0x4, addend: 0x0, symName: __ZL7file117, symObjAddr: 0x2C8F6, symBinAddr: 0x3351E, symSize: 0x0 }
  - { offsetInCU: 0x1E44, offset: 0x2CB1F, size: 0x4, addend: 0x0, symName: __ZL9patches23, symObjAddr: 0x19E934, symBinAddr: 0x1A54D4, symSize: 0x0 }
  - { offsetInCU: 0x1E5B, offset: 0x2CB36, size: 0x4, addend: 0x0, symName: __ZL10patchBuf40, symObjAddr: 0x2CA6A, symBinAddr: 0x33692, symSize: 0x0 }
  - { offsetInCU: 0x1E72, offset: 0x2CB4D, size: 0x4, addend: 0x0, symName: __ZL11platforms24, symObjAddr: 0x226E0, symBinAddr: 0x29308, symSize: 0x0 }
  - { offsetInCU: 0x1E89, offset: 0x2CB64, size: 0x4, addend: 0x0, symName: __ZL7file118, symObjAddr: 0x2CA6E, symBinAddr: 0x33696, symSize: 0x0 }
  - { offsetInCU: 0x1EAD, offset: 0x2CB88, size: 0x4, addend: 0x0, symName: __ZL7file119, symObjAddr: 0x2CBC0, symBinAddr: 0x337E8, symSize: 0x0 }
  - { offsetInCU: 0x1ED1, offset: 0x2CBAC, size: 0x4, addend: 0x0, symName: __ZL9layouts24, symObjAddr: 0x22708, symBinAddr: 0x29330, symSize: 0x0 }
  - { offsetInCU: 0x1EE8, offset: 0x2CBC3, size: 0x4, addend: 0x0, symName: __ZL7file120, symObjAddr: 0x2CD15, symBinAddr: 0x3393D, symSize: 0x0 }
  - { offsetInCU: 0x1F0B, offset: 0x2CBE6, size: 0x4, addend: 0x0, symName: __ZL7file121, symObjAddr: 0x2CDE4, symBinAddr: 0x33A0C, symSize: 0x0 }
  - { offsetInCU: 0x1F2F, offset: 0x2CC0A, size: 0x4, addend: 0x0, symName: __ZL9patches24, symObjAddr: 0x19EA14, symBinAddr: 0x1A55B4, symSize: 0x0 }
  - { offsetInCU: 0x1F46, offset: 0x2CC21, size: 0x4, addend: 0x0, symName: __ZL10patchBuf41, symObjAddr: 0x2D5CF, symBinAddr: 0x341F7, symSize: 0x0 }
  - { offsetInCU: 0x1F5D, offset: 0x2CC38, size: 0x4, addend: 0x0, symName: __ZL11revisions10, symObjAddr: 0x22730, symBinAddr: 0x29358, symSize: 0x0 }
  - { offsetInCU: 0x1F74, offset: 0x2CC4F, size: 0x4, addend: 0x0, symName: __ZL11platforms25, symObjAddr: 0x2273C, symBinAddr: 0x29364, symSize: 0x0 }
  - { offsetInCU: 0x1F97, offset: 0x2CC72, size: 0x4, addend: 0x0, symName: __ZL7file122, symObjAddr: 0x2D5D3, symBinAddr: 0x341FB, symSize: 0x0 }
  - { offsetInCU: 0x1FAE, offset: 0x2CC89, size: 0x4, addend: 0x0, symName: __ZL7file123, symObjAddr: 0x2D710, symBinAddr: 0x34338, symSize: 0x0 }
  - { offsetInCU: 0x1FC5, offset: 0x2CCA0, size: 0x4, addend: 0x0, symName: __ZL7file124, symObjAddr: 0x2D842, symBinAddr: 0x3446A, symSize: 0x0 }
  - { offsetInCU: 0x1FE9, offset: 0x2CCC4, size: 0x4, addend: 0x0, symName: __ZL7file125, symObjAddr: 0x2E6D3, symBinAddr: 0x352FB, symSize: 0x0 }
  - { offsetInCU: 0x200D, offset: 0x2CCE8, size: 0x4, addend: 0x0, symName: __ZL7file126, symObjAddr: 0x2F54D, symBinAddr: 0x36175, symSize: 0x0 }
  - { offsetInCU: 0x2031, offset: 0x2CD0C, size: 0x4, addend: 0x0, symName: __ZL9layouts25, symObjAddr: 0x227A0, symBinAddr: 0x293C8, symSize: 0x0 }
  - { offsetInCU: 0x2048, offset: 0x2CD23, size: 0x4, addend: 0x0, symName: __ZL7file127, symObjAddr: 0x2F686, symBinAddr: 0x362AE, symSize: 0x0 }
  - { offsetInCU: 0x205F, offset: 0x2CD3A, size: 0x4, addend: 0x0, symName: __ZL7file128, symObjAddr: 0x2FA1C, symBinAddr: 0x36644, symSize: 0x0 }
  - { offsetInCU: 0x2083, offset: 0x2CD5E, size: 0x4, addend: 0x0, symName: __ZL7file129, symObjAddr: 0x2FDBB, symBinAddr: 0x369E3, symSize: 0x0 }
  - { offsetInCU: 0x20A7, offset: 0x2CD82, size: 0x4, addend: 0x0, symName: __ZL7file130, symObjAddr: 0x30172, symBinAddr: 0x36D9A, symSize: 0x0 }
  - { offsetInCU: 0x20BE, offset: 0x2CD99, size: 0x4, addend: 0x0, symName: __ZL7file131, symObjAddr: 0x30529, symBinAddr: 0x37151, symSize: 0x0 }
  - { offsetInCU: 0x20D5, offset: 0x2CDB0, size: 0x4, addend: 0x0, symName: __ZL9patches25, symObjAddr: 0x19EB10, symBinAddr: 0x1A56B0, symSize: 0x0 }
  - { offsetInCU: 0x20EC, offset: 0x2CDC7, size: 0x4, addend: 0x0, symName: __ZL10patchBuf42, symObjAddr: 0x308C0, symBinAddr: 0x374E8, symSize: 0x0 }
  - { offsetInCU: 0x2103, offset: 0x2CDDE, size: 0x4, addend: 0x0, symName: __ZL11platforms26, symObjAddr: 0x22804, symBinAddr: 0x2942C, symSize: 0x0 }
  - { offsetInCU: 0x211A, offset: 0x2CDF5, size: 0x4, addend: 0x0, symName: __ZL7file132, symObjAddr: 0x308C4, symBinAddr: 0x374EC, symSize: 0x0 }
  - { offsetInCU: 0x213E, offset: 0x2CE19, size: 0x4, addend: 0x0, symName: __ZL7file133, symObjAddr: 0x309E0, symBinAddr: 0x37608, symSize: 0x0 }
  - { offsetInCU: 0x2162, offset: 0x2CE3D, size: 0x4, addend: 0x0, symName: __ZL9layouts26, symObjAddr: 0x2282C, symBinAddr: 0x29454, symSize: 0x0 }
  - { offsetInCU: 0x2179, offset: 0x2CE54, size: 0x4, addend: 0x0, symName: __ZL7file134, symObjAddr: 0x30AFE, symBinAddr: 0x37726, symSize: 0x0 }
  - { offsetInCU: 0x2190, offset: 0x2CE6B, size: 0x4, addend: 0x0, symName: __ZL7file135, symObjAddr: 0x30E60, symBinAddr: 0x37A88, symSize: 0x0 }
  - { offsetInCU: 0x21A7, offset: 0x2CE82, size: 0x4, addend: 0x0, symName: __ZL9patches26, symObjAddr: 0x19EC0C, symBinAddr: 0x1A57AC, symSize: 0x0 }
  - { offsetInCU: 0x21BE, offset: 0x2CE99, size: 0x4, addend: 0x0, symName: __ZL10patchBuf43, symObjAddr: 0x311C9, symBinAddr: 0x37DF1, symSize: 0x0 }
  - { offsetInCU: 0x21D5, offset: 0x2CEB0, size: 0x4, addend: 0x0, symName: __ZL11platforms27, symObjAddr: 0x22854, symBinAddr: 0x2947C, symSize: 0x0 }
  - { offsetInCU: 0x21EC, offset: 0x2CEC7, size: 0x4, addend: 0x0, symName: __ZL7file136, symObjAddr: 0x311CD, symBinAddr: 0x37DF5, symSize: 0x0 }
  - { offsetInCU: 0x2210, offset: 0x2CEEB, size: 0x4, addend: 0x0, symName: __ZL9layouts27, symObjAddr: 0x22868, symBinAddr: 0x29490, symSize: 0x0 }
  - { offsetInCU: 0x2227, offset: 0x2CF02, size: 0x4, addend: 0x0, symName: __ZL7file137, symObjAddr: 0x3135F, symBinAddr: 0x37F87, symSize: 0x0 }
  - { offsetInCU: 0x224B, offset: 0x2CF26, size: 0x4, addend: 0x0, symName: __ZL9patches27, symObjAddr: 0x19ECD0, symBinAddr: 0x1A5870, symSize: 0x0 }
  - { offsetInCU: 0x2262, offset: 0x2CF3D, size: 0x4, addend: 0x0, symName: __ZL10patchBuf44, symObjAddr: 0x31857, symBinAddr: 0x3847F, symSize: 0x0 }
  - { offsetInCU: 0x2279, offset: 0x2CF54, size: 0x4, addend: 0x0, symName: __ZL11platforms28, symObjAddr: 0x2287C, symBinAddr: 0x294A4, symSize: 0x0 }
  - { offsetInCU: 0x2290, offset: 0x2CF6B, size: 0x4, addend: 0x0, symName: __ZL7file138, symObjAddr: 0x3185B, symBinAddr: 0x38483, symSize: 0x0 }
  - { offsetInCU: 0x22A7, offset: 0x2CF82, size: 0x4, addend: 0x0, symName: __ZL7file139, symObjAddr: 0x319A9, symBinAddr: 0x385D1, symSize: 0x0 }
  - { offsetInCU: 0x22CB, offset: 0x2CFA6, size: 0x4, addend: 0x0, symName: __ZL9layouts28, symObjAddr: 0x228A4, symBinAddr: 0x294CC, symSize: 0x0 }
  - { offsetInCU: 0x22E2, offset: 0x2CFBD, size: 0x4, addend: 0x0, symName: __ZL7file140, symObjAddr: 0x31AF5, symBinAddr: 0x3871D, symSize: 0x0 }
  - { offsetInCU: 0x22F9, offset: 0x2CFD4, size: 0x4, addend: 0x0, symName: __ZL7file141, symObjAddr: 0x31C65, symBinAddr: 0x3888D, symSize: 0x0 }
  - { offsetInCU: 0x2310, offset: 0x2CFEB, size: 0x4, addend: 0x0, symName: __ZL9patches28, symObjAddr: 0x19EDB0, symBinAddr: 0x1A5950, symSize: 0x0 }
  - { offsetInCU: 0x2327, offset: 0x2D002, size: 0x4, addend: 0x0, symName: __ZL10patchBuf45, symObjAddr: 0x31DD5, symBinAddr: 0x389FD, symSize: 0x0 }
  - { offsetInCU: 0x233E, offset: 0x2D019, size: 0x4, addend: 0x0, symName: __ZL10patchBuf46, symObjAddr: 0x31DD9, symBinAddr: 0x38A01, symSize: 0x0 }
  - { offsetInCU: 0x2361, offset: 0x2D03C, size: 0x4, addend: 0x0, symName: __ZL10patchBuf47, symObjAddr: 0x31DDF, symBinAddr: 0x38A07, symSize: 0x0 }
  - { offsetInCU: 0x2378, offset: 0x2D053, size: 0x4, addend: 0x0, symName: __ZL10patchBuf48, symObjAddr: 0x31DE5, symBinAddr: 0x38A0D, symSize: 0x0 }
  - { offsetInCU: 0x238F, offset: 0x2D06A, size: 0x4, addend: 0x0, symName: __ZL10patchBuf49, symObjAddr: 0x31DEB, symBinAddr: 0x38A13, symSize: 0x0 }
  - { offsetInCU: 0x23A6, offset: 0x2D081, size: 0x4, addend: 0x0, symName: __ZL11platforms29, symObjAddr: 0x228CC, symBinAddr: 0x294F4, symSize: 0x0 }
  - { offsetInCU: 0x23BD, offset: 0x2D098, size: 0x4, addend: 0x0, symName: __ZL7file142, symObjAddr: 0x31DF1, symBinAddr: 0x38A19, symSize: 0x0 }
  - { offsetInCU: 0x23D4, offset: 0x2D0AF, size: 0x4, addend: 0x0, symName: __ZL7file143, symObjAddr: 0x31F3F, symBinAddr: 0x38B67, symSize: 0x0 }
  - { offsetInCU: 0x23EB, offset: 0x2D0C6, size: 0x4, addend: 0x0, symName: __ZL7file144, symObjAddr: 0x32071, symBinAddr: 0x38C99, symSize: 0x0 }
  - { offsetInCU: 0x2402, offset: 0x2D0DD, size: 0x4, addend: 0x0, symName: __ZL7file145, symObjAddr: 0x321B3, symBinAddr: 0x38DDB, symSize: 0x0 }
  - { offsetInCU: 0x2419, offset: 0x2D0F4, size: 0x4, addend: 0x0, symName: __ZL7file146, symObjAddr: 0x322E6, symBinAddr: 0x38F0E, symSize: 0x0 }
  - { offsetInCU: 0x243D, offset: 0x2D118, size: 0x4, addend: 0x0, symName: __ZL9layouts29, symObjAddr: 0x22930, symBinAddr: 0x29558, symSize: 0x0 }
  - { offsetInCU: 0x2454, offset: 0x2D12F, size: 0x4, addend: 0x0, symName: __ZL7file147, symObjAddr: 0x32437, symBinAddr: 0x3905F, symSize: 0x0 }
  - { offsetInCU: 0x2478, offset: 0x2D153, size: 0x4, addend: 0x0, symName: __ZL7file148, symObjAddr: 0x325A8, symBinAddr: 0x391D0, symSize: 0x0 }
  - { offsetInCU: 0x248F, offset: 0x2D16A, size: 0x4, addend: 0x0, symName: __ZL7file149, symObjAddr: 0x326EB, symBinAddr: 0x39313, symSize: 0x0 }
  - { offsetInCU: 0x24A6, offset: 0x2D181, size: 0x4, addend: 0x0, symName: __ZL7file150, symObjAddr: 0x3285C, symBinAddr: 0x39484, symSize: 0x0 }
  - { offsetInCU: 0x24BD, offset: 0x2D198, size: 0x4, addend: 0x0, symName: __ZL7file151, symObjAddr: 0x3299E, symBinAddr: 0x395C6, symSize: 0x0 }
  - { offsetInCU: 0x24D4, offset: 0x2D1AF, size: 0x4, addend: 0x0, symName: __ZL9patches29, symObjAddr: 0x19EEE4, symBinAddr: 0x1A5A84, symSize: 0x0 }
  - { offsetInCU: 0x24EB, offset: 0x2D1C6, size: 0x4, addend: 0x0, symName: __ZL10patchBuf50, symObjAddr: 0x32B10, symBinAddr: 0x39738, symSize: 0x0 }
  - { offsetInCU: 0x2502, offset: 0x2D1DD, size: 0x4, addend: 0x0, symName: __ZL11platforms30, symObjAddr: 0x22994, symBinAddr: 0x295BC, symSize: 0x0 }
  - { offsetInCU: 0x2519, offset: 0x2D1F4, size: 0x4, addend: 0x0, symName: __ZL7file152, symObjAddr: 0x32B14, symBinAddr: 0x3973C, symSize: 0x0 }
  - { offsetInCU: 0x2530, offset: 0x2D20B, size: 0x4, addend: 0x0, symName: __ZL7file153, symObjAddr: 0x32C65, symBinAddr: 0x3988D, symSize: 0x0 }
  - { offsetInCU: 0x2547, offset: 0x2D222, size: 0x4, addend: 0x0, symName: __ZL9layouts30, symObjAddr: 0x229BC, symBinAddr: 0x295E4, symSize: 0x0 }
  - { offsetInCU: 0x255E, offset: 0x2D239, size: 0x4, addend: 0x0, symName: __ZL7file154, symObjAddr: 0x32DB6, symBinAddr: 0x399DE, symSize: 0x0 }
  - { offsetInCU: 0x2582, offset: 0x2D25D, size: 0x4, addend: 0x0, symName: __ZL7file155, symObjAddr: 0x32EB6, symBinAddr: 0x39ADE, symSize: 0x0 }
  - { offsetInCU: 0x2599, offset: 0x2D274, size: 0x4, addend: 0x0, symName: __ZL9patches30, symObjAddr: 0x19EFA8, symBinAddr: 0x1A5B48, symSize: 0x0 }
  - { offsetInCU: 0x25B0, offset: 0x2D28B, size: 0x4, addend: 0x0, symName: __ZL10patchBuf51, symObjAddr: 0x32FB6, symBinAddr: 0x39BDE, symSize: 0x0 }
  - { offsetInCU: 0x25C7, offset: 0x2D2A2, size: 0x4, addend: 0x0, symName: __ZL11revisions11, symObjAddr: 0x229E4, symBinAddr: 0x2960C, symSize: 0x0 }
  - { offsetInCU: 0x25EA, offset: 0x2D2C5, size: 0x4, addend: 0x0, symName: __ZL11platforms31, symObjAddr: 0x229EC, symBinAddr: 0x29614, symSize: 0x0 }
  - { offsetInCU: 0x2601, offset: 0x2D2DC, size: 0x4, addend: 0x0, symName: __ZL7file156, symObjAddr: 0x32FBA, symBinAddr: 0x39BE2, symSize: 0x0 }
  - { offsetInCU: 0x2618, offset: 0x2D2F3, size: 0x4, addend: 0x0, symName: __ZL7file157, symObjAddr: 0x330F7, symBinAddr: 0x39D1F, symSize: 0x0 }
  - { offsetInCU: 0x263C, offset: 0x2D317, size: 0x4, addend: 0x0, symName: __ZL7file158, symObjAddr: 0x33C11, symBinAddr: 0x3A839, symSize: 0x0 }
  - { offsetInCU: 0x2660, offset: 0x2D33B, size: 0x4, addend: 0x0, symName: __ZL9layouts31, symObjAddr: 0x22A28, symBinAddr: 0x29650, symSize: 0x0 }
  - { offsetInCU: 0x2677, offset: 0x2D352, size: 0x4, addend: 0x0, symName: __ZL7file159, symObjAddr: 0x3472D, symBinAddr: 0x3B355, symSize: 0x0 }
  - { offsetInCU: 0x269A, offset: 0x2D375, size: 0x4, addend: 0x0, symName: __ZL7file160, symObjAddr: 0x3480E, symBinAddr: 0x3B436, symSize: 0x0 }
  - { offsetInCU: 0x26BE, offset: 0x2D399, size: 0x4, addend: 0x0, symName: __ZL7file161, symObjAddr: 0x357BC, symBinAddr: 0x3C3E4, symSize: 0x0 }
  - { offsetInCU: 0x26E2, offset: 0x2D3BD, size: 0x4, addend: 0x0, symName: __ZL9patches31, symObjAddr: 0x19F088, symBinAddr: 0x1A5C28, symSize: 0x0 }
  - { offsetInCU: 0x2705, offset: 0x2D3E0, size: 0x4, addend: 0x0, symName: __ZL10patchBuf52, symObjAddr: 0x36767, symBinAddr: 0x3D38F, symSize: 0x0 }
  - { offsetInCU: 0x271C, offset: 0x2D3F7, size: 0x4, addend: 0x0, symName: __ZL10patchBuf53, symObjAddr: 0x3676B, symBinAddr: 0x3D393, symSize: 0x0 }
  - { offsetInCU: 0x2733, offset: 0x2D40E, size: 0x4, addend: 0x0, symName: __ZL10patchBuf54, symObjAddr: 0x36771, symBinAddr: 0x3D399, symSize: 0x0 }
  - { offsetInCU: 0x274A, offset: 0x2D425, size: 0x4, addend: 0x0, symName: __ZL10patchBuf55, symObjAddr: 0x36777, symBinAddr: 0x3D39F, symSize: 0x0 }
  - { offsetInCU: 0x276D, offset: 0x2D448, size: 0x4, addend: 0x0, symName: __ZL10patchBuf56, symObjAddr: 0x36780, symBinAddr: 0x3D3A8, symSize: 0x0 }
  - { offsetInCU: 0x2784, offset: 0x2D45F, size: 0x4, addend: 0x0, symName: __ZL11codecModIDT, symObjAddr: 0x19BB74, symBinAddr: 0x1A2714, symSize: 0x0 }
  - { offsetInCU: 0x27A7, offset: 0x2D482, size: 0x4, addend: 0x0, symName: __ZL11platforms32, symObjAddr: 0x3678C, symBinAddr: 0x3D3B4, symSize: 0x0 }
  - { offsetInCU: 0x27BE, offset: 0x2D499, size: 0x4, addend: 0x0, symName: __ZL7file162, symObjAddr: 0x36CC4, symBinAddr: 0x3D8EC, symSize: 0x0 }
  - { offsetInCU: 0x27D5, offset: 0x2D4B0, size: 0x4, addend: 0x0, symName: __ZL9layouts32, symObjAddr: 0x367B4, symBinAddr: 0x3D3DC, symSize: 0x0 }
  - { offsetInCU: 0x27EC, offset: 0x2D4C7, size: 0x4, addend: 0x0, symName: __ZL7file163, symObjAddr: 0x36E0A, symBinAddr: 0x3DA32, symSize: 0x0 }
  - { offsetInCU: 0x280F, offset: 0x2D4EA, size: 0x4, addend: 0x0, symName: __ZL9patches32, symObjAddr: 0x19F1D8, symBinAddr: 0x1A5D78, symSize: 0x0 }
  - { offsetInCU: 0x2826, offset: 0x2D501, size: 0x4, addend: 0x0, symName: __ZL10patchBuf57, symObjAddr: 0x36EFF, symBinAddr: 0x3DB27, symSize: 0x0 }
  - { offsetInCU: 0x283D, offset: 0x2D518, size: 0x4, addend: 0x0, symName: __ZL11revisions12, symObjAddr: 0x367DC, symBinAddr: 0x3D404, symSize: 0x0 }
  - { offsetInCU: 0x2854, offset: 0x2D52F, size: 0x4, addend: 0x0, symName: __ZL11platforms33, symObjAddr: 0x367E0, symBinAddr: 0x3D408, symSize: 0x0 }
  - { offsetInCU: 0x286B, offset: 0x2D546, size: 0x4, addend: 0x0, symName: __ZL7file164, symObjAddr: 0x36F03, symBinAddr: 0x3DB2B, symSize: 0x0 }
  - { offsetInCU: 0x288F, offset: 0x2D56A, size: 0x4, addend: 0x0, symName: __ZL7file165, symObjAddr: 0x37158, symBinAddr: 0x3DD80, symSize: 0x0 }
  - { offsetInCU: 0x28B3, offset: 0x2D58E, size: 0x4, addend: 0x0, symName: __ZL9layouts33, symObjAddr: 0x36808, symBinAddr: 0x3D430, symSize: 0x0 }
  - { offsetInCU: 0x28CA, offset: 0x2D5A5, size: 0x4, addend: 0x0, symName: __ZL7file166, symObjAddr: 0x372D2, symBinAddr: 0x3DEFA, symSize: 0x0 }
  - { offsetInCU: 0x28EE, offset: 0x2D5C9, size: 0x4, addend: 0x0, symName: __ZL7file167, symObjAddr: 0x3753E, symBinAddr: 0x3E166, symSize: 0x0 }
  - { offsetInCU: 0x2912, offset: 0x2D5ED, size: 0x4, addend: 0x0, symName: __ZL9patches33, symObjAddr: 0x19F29C, symBinAddr: 0x1A5E3C, symSize: 0x0 }
  - { offsetInCU: 0x2929, offset: 0x2D604, size: 0x4, addend: 0x0, symName: __ZL10patchBuf58, symObjAddr: 0x38B5D, symBinAddr: 0x3F785, symSize: 0x0 }
  - { offsetInCU: 0x2940, offset: 0x2D61B, size: 0x4, addend: 0x0, symName: __ZL11platforms34, symObjAddr: 0x36830, symBinAddr: 0x3D458, symSize: 0x0 }
  - { offsetInCU: 0x2957, offset: 0x2D632, size: 0x4, addend: 0x0, symName: __ZL7file168, symObjAddr: 0x38B61, symBinAddr: 0x3F789, symSize: 0x0 }
  - { offsetInCU: 0x297B, offset: 0x2D656, size: 0x4, addend: 0x0, symName: __ZL7file169, symObjAddr: 0x38CA1, symBinAddr: 0x3F8C9, symSize: 0x0 }
  - { offsetInCU: 0x2992, offset: 0x2D66D, size: 0x4, addend: 0x0, symName: __ZL9layouts34, symObjAddr: 0x36858, symBinAddr: 0x3D480, symSize: 0x0 }
  - { offsetInCU: 0x29A9, offset: 0x2D684, size: 0x4, addend: 0x0, symName: __ZL7file170, symObjAddr: 0x38DDB, symBinAddr: 0x3FA03, symSize: 0x0 }
  - { offsetInCU: 0x29CD, offset: 0x2D6A8, size: 0x4, addend: 0x0, symName: __ZL7file171, symObjAddr: 0x3A31A, symBinAddr: 0x40F42, symSize: 0x0 }
  - { offsetInCU: 0x29E4, offset: 0x2D6BF, size: 0x4, addend: 0x0, symName: __ZL9patches34, symObjAddr: 0x19F360, symBinAddr: 0x1A5F00, symSize: 0x0 }
  - { offsetInCU: 0x29FB, offset: 0x2D6D6, size: 0x4, addend: 0x0, symName: __ZL10patchBuf59, symObjAddr: 0x3A586, symBinAddr: 0x411AE, symSize: 0x0 }
  - { offsetInCU: 0x2A12, offset: 0x2D6ED, size: 0x4, addend: 0x0, symName: __ZL11revisions13, symObjAddr: 0x36880, symBinAddr: 0x3D4A8, symSize: 0x0 }
  - { offsetInCU: 0x2A29, offset: 0x2D704, size: 0x4, addend: 0x0, symName: __ZL11platforms35, symObjAddr: 0x36888, symBinAddr: 0x3D4B0, symSize: 0x0 }
  - { offsetInCU: 0x2A40, offset: 0x2D71B, size: 0x4, addend: 0x0, symName: __ZL7file172, symObjAddr: 0x3A58A, symBinAddr: 0x411B2, symSize: 0x0 }
  - { offsetInCU: 0x2A57, offset: 0x2D732, size: 0x4, addend: 0x0, symName: __ZL7file173, symObjAddr: 0x3A6CA, symBinAddr: 0x412F2, symSize: 0x0 }
  - { offsetInCU: 0x2A7B, offset: 0x2D756, size: 0x4, addend: 0x0, symName: __ZL7file174, symObjAddr: 0x3A812, symBinAddr: 0x4143A, symSize: 0x0 }
  - { offsetInCU: 0x2A92, offset: 0x2D76D, size: 0x4, addend: 0x0, symName: __ZL7file175, symObjAddr: 0x3A944, symBinAddr: 0x4156C, symSize: 0x0 }
  - { offsetInCU: 0x2AB6, offset: 0x2D791, size: 0x4, addend: 0x0, symName: __ZL7file176, symObjAddr: 0x3AA7F, symBinAddr: 0x416A7, symSize: 0x0 }
  - { offsetInCU: 0x2ADA, offset: 0x2D7B5, size: 0x4, addend: 0x0, symName: __ZL9layouts35, symObjAddr: 0x368EC, symBinAddr: 0x3D514, symSize: 0x0 }
  - { offsetInCU: 0x2AF1, offset: 0x2D7CC, size: 0x4, addend: 0x0, symName: __ZL7file177, symObjAddr: 0x3ABC6, symBinAddr: 0x417EE, symSize: 0x0 }
  - { offsetInCU: 0x2B15, offset: 0x2D7F0, size: 0x4, addend: 0x0, symName: __ZL7file178, symObjAddr: 0x3C0D9, symBinAddr: 0x42D01, symSize: 0x0 }
  - { offsetInCU: 0x2B39, offset: 0x2D814, size: 0x4, addend: 0x0, symName: __ZL7file179, symObjAddr: 0x3C325, symBinAddr: 0x42F4D, symSize: 0x0 }
  - { offsetInCU: 0x2B5D, offset: 0x2D838, size: 0x4, addend: 0x0, symName: __ZL7file180, symObjAddr: 0x3C572, symBinAddr: 0x4319A, symSize: 0x0 }
  - { offsetInCU: 0x2B81, offset: 0x2D85C, size: 0x4, addend: 0x0, symName: __ZL7file181, symObjAddr: 0x3C7A3, symBinAddr: 0x433CB, symSize: 0x0 }
  - { offsetInCU: 0x2BA5, offset: 0x2D880, size: 0x4, addend: 0x0, symName: __ZL9patches35, symObjAddr: 0x19F440, symBinAddr: 0x1A5FE0, symSize: 0x0 }
  - { offsetInCU: 0x2BBC, offset: 0x2D897, size: 0x4, addend: 0x0, symName: __ZL10patchBuf60, symObjAddr: 0x3C9F1, symBinAddr: 0x43619, symSize: 0x0 }
  - { offsetInCU: 0x2BD3, offset: 0x2D8AE, size: 0x4, addend: 0x0, symName: __ZL11platforms36, symObjAddr: 0x36950, symBinAddr: 0x3D578, symSize: 0x0 }
  - { offsetInCU: 0x2BF6, offset: 0x2D8D1, size: 0x4, addend: 0x0, symName: __ZL7file182, symObjAddr: 0x3C9F5, symBinAddr: 0x4361D, symSize: 0x0 }
  - { offsetInCU: 0x2C1A, offset: 0x2D8F5, size: 0x4, addend: 0x0, symName: __ZL7file183, symObjAddr: 0x3CB21, symBinAddr: 0x43749, symSize: 0x0 }
  - { offsetInCU: 0x2C31, offset: 0x2D90C, size: 0x4, addend: 0x0, symName: __ZL7file184, symObjAddr: 0x3CC68, symBinAddr: 0x43890, symSize: 0x0 }
  - { offsetInCU: 0x2C55, offset: 0x2D930, size: 0x4, addend: 0x0, symName: __ZL7file185, symObjAddr: 0x3D8A7, symBinAddr: 0x444CF, symSize: 0x0 }
  - { offsetInCU: 0x2C79, offset: 0x2D954, size: 0x4, addend: 0x0, symName: __ZL7file186, symObjAddr: 0x3E380, symBinAddr: 0x44FA8, symSize: 0x0 }
  - { offsetInCU: 0x2C9D, offset: 0x2D978, size: 0x4, addend: 0x0, symName: __ZL7file187, symObjAddr: 0x3EE5B, symBinAddr: 0x45A83, symSize: 0x0 }
  - { offsetInCU: 0x2CB4, offset: 0x2D98F, size: 0x4, addend: 0x0, symName: __ZL7file188, symObjAddr: 0x3EFA9, symBinAddr: 0x45BD1, symSize: 0x0 }
  - { offsetInCU: 0x2CCB, offset: 0x2D9A6, size: 0x4, addend: 0x0, symName: __ZL9layouts36, symObjAddr: 0x369DC, symBinAddr: 0x3D604, symSize: 0x0 }
  - { offsetInCU: 0x2CE2, offset: 0x2D9BD, size: 0x4, addend: 0x0, symName: __ZL7file189, symObjAddr: 0x3F0EC, symBinAddr: 0x45D14, symSize: 0x0 }
  - { offsetInCU: 0x2D05, offset: 0x2D9E0, size: 0x4, addend: 0x0, symName: __ZL7file190, symObjAddr: 0x3F1E2, symBinAddr: 0x45E0A, symSize: 0x0 }
  - { offsetInCU: 0x2D28, offset: 0x2DA03, size: 0x4, addend: 0x0, symName: __ZL7file191, symObjAddr: 0x3F2AD, symBinAddr: 0x45ED5, symSize: 0x0 }
  - { offsetInCU: 0x2D4C, offset: 0x2DA27, size: 0x4, addend: 0x0, symName: __ZL7file192, symObjAddr: 0x3F51B, symBinAddr: 0x46143, symSize: 0x0 }
  - { offsetInCU: 0x2D70, offset: 0x2DA4B, size: 0x4, addend: 0x0, symName: __ZL7file193, symObjAddr: 0x3F672, symBinAddr: 0x4629A, symSize: 0x0 }
  - { offsetInCU: 0x2D94, offset: 0x2DA6F, size: 0x4, addend: 0x0, symName: __ZL7file194, symObjAddr: 0x3F7CA, symBinAddr: 0x463F2, symSize: 0x0 }
  - { offsetInCU: 0x2DB8, offset: 0x2DA93, size: 0x4, addend: 0x0, symName: __ZL7file195, symObjAddr: 0x3FA37, symBinAddr: 0x4665F, symSize: 0x0 }
  - { offsetInCU: 0x2DDC, offset: 0x2DAB7, size: 0x4, addend: 0x0, symName: __ZL9patches36, symObjAddr: 0x19F520, symBinAddr: 0x1A60C0, symSize: 0x0 }
  - { offsetInCU: 0x2DF3, offset: 0x2DACE, size: 0x4, addend: 0x0, symName: __ZL10patchBuf61, symObjAddr: 0x3FCC9, symBinAddr: 0x468F1, symSize: 0x0 }
  - { offsetInCU: 0x2E0A, offset: 0x2DAE5, size: 0x4, addend: 0x0, symName: __ZL11platforms37, symObjAddr: 0x36A68, symBinAddr: 0x3D690, symSize: 0x0 }
  - { offsetInCU: 0x2E21, offset: 0x2DAFC, size: 0x4, addend: 0x0, symName: __ZL7file196, symObjAddr: 0x3FCCD, symBinAddr: 0x468F5, symSize: 0x0 }
  - { offsetInCU: 0x2E38, offset: 0x2DB13, size: 0x4, addend: 0x0, symName: __ZL9layouts37, symObjAddr: 0x36A7C, symBinAddr: 0x3D6A4, symSize: 0x0 }
  - { offsetInCU: 0x2E4F, offset: 0x2DB2A, size: 0x4, addend: 0x0, symName: __ZL7file197, symObjAddr: 0x3FE10, symBinAddr: 0x46A38, symSize: 0x0 }
  - { offsetInCU: 0x2E66, offset: 0x2DB41, size: 0x4, addend: 0x0, symName: __ZL9patches37, symObjAddr: 0x19F600, symBinAddr: 0x1A61A0, symSize: 0x0 }
  - { offsetInCU: 0x2E7D, offset: 0x2DB58, size: 0x4, addend: 0x0, symName: __ZL10patchBuf62, symObjAddr: 0x3FF05, symBinAddr: 0x46B2D, symSize: 0x0 }
  - { offsetInCU: 0x2E94, offset: 0x2DB6F, size: 0x4, addend: 0x0, symName: __ZL11platforms38, symObjAddr: 0x36A90, symBinAddr: 0x3D6B8, symSize: 0x0 }
  - { offsetInCU: 0x2EAB, offset: 0x2DB86, size: 0x4, addend: 0x0, symName: __ZL7file198, symObjAddr: 0x3FF09, symBinAddr: 0x46B31, symSize: 0x0 }
  - { offsetInCU: 0x2EC2, offset: 0x2DB9D, size: 0x4, addend: 0x0, symName: __ZL9layouts38, symObjAddr: 0x36AA4, symBinAddr: 0x3D6CC, symSize: 0x0 }
  - { offsetInCU: 0x2ED9, offset: 0x2DBB4, size: 0x4, addend: 0x0, symName: __ZL7file199, symObjAddr: 0x40079, symBinAddr: 0x46CA1, symSize: 0x0 }
  - { offsetInCU: 0x2EFD, offset: 0x2DBD8, size: 0x4, addend: 0x0, symName: __ZL9patches38, symObjAddr: 0x19F6C4, symBinAddr: 0x1A6264, symSize: 0x0 }
  - { offsetInCU: 0x2F14, offset: 0x2DBEF, size: 0x4, addend: 0x0, symName: __ZL10patchBuf63, symObjAddr: 0x40184, symBinAddr: 0x46DAC, symSize: 0x0 }
  - { offsetInCU: 0x2F2B, offset: 0x2DC06, size: 0x4, addend: 0x0, symName: __ZL11platforms39, symObjAddr: 0x36AB8, symBinAddr: 0x3D6E0, symSize: 0x0 }
  - { offsetInCU: 0x2F42, offset: 0x2DC1D, size: 0x4, addend: 0x0, symName: __ZL7file200, symObjAddr: 0x40188, symBinAddr: 0x46DB0, symSize: 0x0 }
  - { offsetInCU: 0x2F59, offset: 0x2DC34, size: 0x4, addend: 0x0, symName: __ZL9layouts39, symObjAddr: 0x36ACC, symBinAddr: 0x3D6F4, symSize: 0x0 }
  - { offsetInCU: 0x2F70, offset: 0x2DC4B, size: 0x4, addend: 0x0, symName: __ZL7file201, symObjAddr: 0x403DD, symBinAddr: 0x47005, symSize: 0x0 }
  - { offsetInCU: 0x2F87, offset: 0x2DC62, size: 0x4, addend: 0x0, symName: __ZL9patches39, symObjAddr: 0x19F7A4, symBinAddr: 0x1A6344, symSize: 0x0 }
  - { offsetInCU: 0x2F9E, offset: 0x2DC79, size: 0x4, addend: 0x0, symName: __ZL10patchBuf64, symObjAddr: 0x40649, symBinAddr: 0x47271, symSize: 0x0 }
  - { offsetInCU: 0x2FB5, offset: 0x2DC90, size: 0x4, addend: 0x0, symName: __ZL11platforms40, symObjAddr: 0x36AE0, symBinAddr: 0x3D708, symSize: 0x0 }
  - { offsetInCU: 0x2FCC, offset: 0x2DCA7, size: 0x4, addend: 0x0, symName: __ZL7file202, symObjAddr: 0x4064D, symBinAddr: 0x47275, symSize: 0x0 }
  - { offsetInCU: 0x2FF0, offset: 0x2DCCB, size: 0x4, addend: 0x0, symName: __ZL7file203, symObjAddr: 0x407AB, symBinAddr: 0x473D3, symSize: 0x0 }
  - { offsetInCU: 0x3014, offset: 0x2DCEF, size: 0x4, addend: 0x0, symName: __ZL9layouts40, symObjAddr: 0x36B08, symBinAddr: 0x3D730, symSize: 0x0 }
  - { offsetInCU: 0x302B, offset: 0x2DD06, size: 0x4, addend: 0x0, symName: __ZL7file204, symObjAddr: 0x408E2, symBinAddr: 0x4750A, symSize: 0x0 }
  - { offsetInCU: 0x304F, offset: 0x2DD2A, size: 0x4, addend: 0x0, symName: __ZL7file205, symObjAddr: 0x41DF3, symBinAddr: 0x48A1B, symSize: 0x0 }
  - { offsetInCU: 0x3073, offset: 0x2DD4E, size: 0x4, addend: 0x0, symName: __ZL9patches40, symObjAddr: 0x19F884, symBinAddr: 0x1A6424, symSize: 0x0 }
  - { offsetInCU: 0x308A, offset: 0x2DD65, size: 0x4, addend: 0x0, symName: __ZL10patchBuf65, symObjAddr: 0x4331A, symBinAddr: 0x49F42, symSize: 0x0 }
  - { offsetInCU: 0x30A1, offset: 0x2DD7C, size: 0x4, addend: 0x0, symName: __ZL11platforms41, symObjAddr: 0x36B30, symBinAddr: 0x3D758, symSize: 0x0 }
  - { offsetInCU: 0x30B8, offset: 0x2DD93, size: 0x4, addend: 0x0, symName: __ZL7file206, symObjAddr: 0x4331E, symBinAddr: 0x49F46, symSize: 0x0 }
  - { offsetInCU: 0x30DC, offset: 0x2DDB7, size: 0x4, addend: 0x0, symName: __ZL7file207, symObjAddr: 0x44406, symBinAddr: 0x4B02E, symSize: 0x0 }
  - { offsetInCU: 0x30F3, offset: 0x2DDCE, size: 0x4, addend: 0x0, symName: __ZL7file208, symObjAddr: 0x4465B, symBinAddr: 0x4B283, symSize: 0x0 }
  - { offsetInCU: 0x310A, offset: 0x2DDE5, size: 0x4, addend: 0x0, symName: __ZL9layouts41, symObjAddr: 0x36B6C, symBinAddr: 0x3D794, symSize: 0x0 }
  - { offsetInCU: 0x3121, offset: 0x2DDFC, size: 0x4, addend: 0x0, symName: __ZL7file209, symObjAddr: 0x44790, symBinAddr: 0x4B3B8, symSize: 0x0 }
  - { offsetInCU: 0x3145, offset: 0x2DE20, size: 0x4, addend: 0x0, symName: __ZL7file210, symObjAddr: 0x449DA, symBinAddr: 0x4B602, symSize: 0x0 }
  - { offsetInCU: 0x315C, offset: 0x2DE37, size: 0x4, addend: 0x0, symName: __ZL7file211, symObjAddr: 0x44C46, symBinAddr: 0x4B86E, symSize: 0x0 }
  - { offsetInCU: 0x3180, offset: 0x2DE5B, size: 0x4, addend: 0x0, symName: __ZL9patches41, symObjAddr: 0x19F92C, symBinAddr: 0x1A64CC, symSize: 0x0 }
  - { offsetInCU: 0x3197, offset: 0x2DE72, size: 0x4, addend: 0x0, symName: __ZL10patchBuf66, symObjAddr: 0x44D8F, symBinAddr: 0x4B9B7, symSize: 0x0 }
  - { offsetInCU: 0x31AE, offset: 0x2DE89, size: 0x4, addend: 0x0, symName: __ZL11platforms42, symObjAddr: 0x36BA8, symBinAddr: 0x3D7D0, symSize: 0x0 }
  - { offsetInCU: 0x31C5, offset: 0x2DEA0, size: 0x4, addend: 0x0, symName: __ZL7file212, symObjAddr: 0x44D93, symBinAddr: 0x4B9BB, symSize: 0x0 }
  - { offsetInCU: 0x31E9, offset: 0x2DEC4, size: 0x4, addend: 0x0, symName: __ZL9layouts42, symObjAddr: 0x36BBC, symBinAddr: 0x3D7E4, symSize: 0x0 }
  - { offsetInCU: 0x3200, offset: 0x2DEDB, size: 0x4, addend: 0x0, symName: __ZL7file213, symObjAddr: 0x44ED1, symBinAddr: 0x4BAF9, symSize: 0x0 }
  - { offsetInCU: 0x3217, offset: 0x2DEF2, size: 0x4, addend: 0x0, symName: __ZL9patches42, symObjAddr: 0x19F9F0, symBinAddr: 0x1A6590, symSize: 0x0 }
  - { offsetInCU: 0x322E, offset: 0x2DF09, size: 0x4, addend: 0x0, symName: __ZL10patchBuf67, symObjAddr: 0x44FC6, symBinAddr: 0x4BBEE, symSize: 0x0 }
  - { offsetInCU: 0x3245, offset: 0x2DF20, size: 0x4, addend: 0x0, symName: __ZL11platforms43, symObjAddr: 0x36BD0, symBinAddr: 0x3D7F8, symSize: 0x0 }
  - { offsetInCU: 0x325C, offset: 0x2DF37, size: 0x4, addend: 0x0, symName: __ZL7file214, symObjAddr: 0x44FCA, symBinAddr: 0x4BBF2, symSize: 0x0 }
  - { offsetInCU: 0x3273, offset: 0x2DF4E, size: 0x4, addend: 0x0, symName: __ZL7file215, symObjAddr: 0x45113, symBinAddr: 0x4BD3B, symSize: 0x0 }
  - { offsetInCU: 0x328A, offset: 0x2DF65, size: 0x4, addend: 0x0, symName: __ZL9layouts43, symObjAddr: 0x36BF8, symBinAddr: 0x3D820, symSize: 0x0 }
  - { offsetInCU: 0x32A1, offset: 0x2DF7C, size: 0x4, addend: 0x0, symName: __ZL7file216, symObjAddr: 0x45255, symBinAddr: 0x4BE7D, symSize: 0x0 }
  - { offsetInCU: 0x32B8, offset: 0x2DF93, size: 0x4, addend: 0x0, symName: __ZL7file217, symObjAddr: 0x45355, symBinAddr: 0x4BF7D, symSize: 0x0 }
  - { offsetInCU: 0x32DB, offset: 0x2DFB6, size: 0x4, addend: 0x0, symName: __ZL9patches43, symObjAddr: 0x19FAB4, symBinAddr: 0x1A6654, symSize: 0x0 }
  - { offsetInCU: 0x32F2, offset: 0x2DFCD, size: 0x4, addend: 0x0, symName: __ZL10patchBuf68, symObjAddr: 0x45449, symBinAddr: 0x4C071, symSize: 0x0 }
  - { offsetInCU: 0x3309, offset: 0x2DFE4, size: 0x4, addend: 0x0, symName: __ZL11platforms44, symObjAddr: 0x36C20, symBinAddr: 0x3D848, symSize: 0x0 }
  - { offsetInCU: 0x3320, offset: 0x2DFFB, size: 0x4, addend: 0x0, symName: __ZL7file218, symObjAddr: 0x4544D, symBinAddr: 0x4C075, symSize: 0x0 }
  - { offsetInCU: 0x3344, offset: 0x2E01F, size: 0x4, addend: 0x0, symName: __ZL9layouts44, symObjAddr: 0x36C34, symBinAddr: 0x3D85C, symSize: 0x0 }
  - { offsetInCU: 0x335B, offset: 0x2E036, size: 0x4, addend: 0x0, symName: __ZL7file219, symObjAddr: 0x4558C, symBinAddr: 0x4C1B4, symSize: 0x0 }
  - { offsetInCU: 0x3372, offset: 0x2E04D, size: 0x4, addend: 0x0, symName: __ZL9patches44, symObjAddr: 0x19FB94, symBinAddr: 0x1A6734, symSize: 0x0 }
  - { offsetInCU: 0x3389, offset: 0x2E064, size: 0x4, addend: 0x0, symName: __ZL10patchBuf69, symObjAddr: 0x46A9D, symBinAddr: 0x4D6C5, symSize: 0x0 }
  - { offsetInCU: 0x33A0, offset: 0x2E07B, size: 0x4, addend: 0x0, symName: __ZL11platforms45, symObjAddr: 0x36C48, symBinAddr: 0x3D870, symSize: 0x0 }
  - { offsetInCU: 0x33B7, offset: 0x2E092, size: 0x4, addend: 0x0, symName: __ZL7file220, symObjAddr: 0x46AA1, symBinAddr: 0x4D6C9, symSize: 0x0 }
  - { offsetInCU: 0x33CE, offset: 0x2E0A9, size: 0x4, addend: 0x0, symName: __ZL9layouts45, symObjAddr: 0x36C5C, symBinAddr: 0x3D884, symSize: 0x0 }
  - { offsetInCU: 0x33E5, offset: 0x2E0C0, size: 0x4, addend: 0x0, symName: __ZL7file221, symObjAddr: 0x46BCD, symBinAddr: 0x4D7F5, symSize: 0x0 }
  - { offsetInCU: 0x33FC, offset: 0x2E0D7, size: 0x4, addend: 0x0, symName: __ZL9patches45, symObjAddr: 0x19FC74, symBinAddr: 0x1A6814, symSize: 0x0 }
  - { offsetInCU: 0x3413, offset: 0x2E0EE, size: 0x4, addend: 0x0, symName: __ZL11platforms46, symObjAddr: 0x36C70, symBinAddr: 0x3D898, symSize: 0x0 }
  - { offsetInCU: 0x342A, offset: 0x2E105, size: 0x4, addend: 0x0, symName: __ZL7file222, symObjAddr: 0x46CC3, symBinAddr: 0x4D8EB, symSize: 0x0 }
  - { offsetInCU: 0x3441, offset: 0x2E11C, size: 0x4, addend: 0x0, symName: __ZL9layouts46, symObjAddr: 0x36C84, symBinAddr: 0x3D8AC, symSize: 0x0 }
  - { offsetInCU: 0x3458, offset: 0x2E133, size: 0x4, addend: 0x0, symName: __ZL7file223, symObjAddr: 0x46E09, symBinAddr: 0x4DA31, symSize: 0x0 }
  - { offsetInCU: 0x346F, offset: 0x2E14A, size: 0x4, addend: 0x0, symName: __ZL9patches46, symObjAddr: 0x19FD54, symBinAddr: 0x1A68F4, symSize: 0x0 }
  - { offsetInCU: 0x3486, offset: 0x2E161, size: 0x4, addend: 0x0, symName: __ZL10patchBuf70, symObjAddr: 0x46EFF, symBinAddr: 0x4DB27, symSize: 0x0 }
  - { offsetInCU: 0x349D, offset: 0x2E178, size: 0x4, addend: 0x0, symName: __ZL11revisions14, symObjAddr: 0x36C98, symBinAddr: 0x3D8C0, symSize: 0x0 }
  - { offsetInCU: 0x34B4, offset: 0x2E18F, size: 0x4, addend: 0x0, symName: __ZL11platforms47, symObjAddr: 0x36C9C, symBinAddr: 0x3D8C4, symSize: 0x0 }
  - { offsetInCU: 0x34CB, offset: 0x2E1A6, size: 0x4, addend: 0x0, symName: __ZL7file224, symObjAddr: 0x46F03, symBinAddr: 0x4DB2B, symSize: 0x0 }
  - { offsetInCU: 0x34E2, offset: 0x2E1BD, size: 0x4, addend: 0x0, symName: __ZL9layouts47, symObjAddr: 0x36CB0, symBinAddr: 0x3D8D8, symSize: 0x0 }
  - { offsetInCU: 0x34F9, offset: 0x2E1D4, size: 0x4, addend: 0x0, symName: __ZL7file225, symObjAddr: 0x47050, symBinAddr: 0x4DC78, symSize: 0x0 }
  - { offsetInCU: 0x351D, offset: 0x2E1F8, size: 0x4, addend: 0x0, symName: __ZL9patches47, symObjAddr: 0x19FE18, symBinAddr: 0x1A69B8, symSize: 0x0 }
  - { offsetInCU: 0x3534, offset: 0x2E20F, size: 0x4, addend: 0x0, symName: __ZL10patchBuf71, symObjAddr: 0x472D5, symBinAddr: 0x4DEFD, symSize: 0x0 }
  - { offsetInCU: 0x354D, offset: 0x2E228, size: 0x4, addend: 0x0, symName: __ZL15codecModRealtek, symObjAddr: 0x19BDF4, symBinAddr: 0x1A2994, symSize: 0x0 }
  - { offsetInCU: 0x3570, offset: 0x2E24B, size: 0x4, addend: 0x0, symName: __ZL11revisions15, symObjAddr: 0x472DC, symBinAddr: 0x4DF04, symSize: 0x0 }
  - { offsetInCU: 0x3587, offset: 0x2E262, size: 0x4, addend: 0x0, symName: __ZL11platforms48, symObjAddr: 0x472E4, symBinAddr: 0x4DF0C, symSize: 0x0 }
  - { offsetInCU: 0x35AA, offset: 0x2E285, size: 0x4, addend: 0x0, symName: __ZL7file226, symObjAddr: 0x4C90C, symBinAddr: 0x53534, symSize: 0x0 }
  - { offsetInCU: 0x35CE, offset: 0x2E2A9, size: 0x4, addend: 0x0, symName: __ZL7file227, symObjAddr: 0x4CB4A, symBinAddr: 0x53772, symSize: 0x0 }
  - { offsetInCU: 0x35F2, offset: 0x2E2CD, size: 0x4, addend: 0x0, symName: __ZL7file228, symObjAddr: 0x4CCDA, symBinAddr: 0x53902, symSize: 0x0 }
  - { offsetInCU: 0x3616, offset: 0x2E2F1, size: 0x4, addend: 0x0, symName: __ZL7file229, symObjAddr: 0x4CE49, symBinAddr: 0x53A71, symSize: 0x0 }
  - { offsetInCU: 0x363A, offset: 0x2E315, size: 0x4, addend: 0x0, symName: __ZL7file230, symObjAddr: 0x4CF85, symBinAddr: 0x53BAD, symSize: 0x0 }
  - { offsetInCU: 0x3651, offset: 0x2E32C, size: 0x4, addend: 0x0, symName: __ZL7file231, symObjAddr: 0x4D0C1, symBinAddr: 0x53CE9, symSize: 0x0 }
  - { offsetInCU: 0x3675, offset: 0x2E350, size: 0x4, addend: 0x0, symName: __ZL7file232, symObjAddr: 0x4D223, symBinAddr: 0x53E4B, symSize: 0x0 }
  - { offsetInCU: 0x3699, offset: 0x2E374, size: 0x4, addend: 0x0, symName: __ZL7file233, symObjAddr: 0x4D37F, symBinAddr: 0x53FA7, symSize: 0x0 }
  - { offsetInCU: 0x36BD, offset: 0x2E398, size: 0x4, addend: 0x0, symName: __ZL7file234, symObjAddr: 0x4D4F7, symBinAddr: 0x5411F, symSize: 0x0 }
  - { offsetInCU: 0x36D4, offset: 0x2E3AF, size: 0x4, addend: 0x0, symName: __ZL7file235, symObjAddr: 0x4D63F, symBinAddr: 0x54267, symSize: 0x0 }
  - { offsetInCU: 0x36F8, offset: 0x2E3D3, size: 0x4, addend: 0x0, symName: __ZL7file236, symObjAddr: 0x4D7BB, symBinAddr: 0x543E3, symSize: 0x0 }
  - { offsetInCU: 0x370F, offset: 0x2E3EA, size: 0x4, addend: 0x0, symName: __ZL7file237, symObjAddr: 0x4D91D, symBinAddr: 0x54545, symSize: 0x0 }
  - { offsetInCU: 0x3733, offset: 0x2E40E, size: 0x4, addend: 0x0, symName: __ZL7file238, symObjAddr: 0x4DA82, symBinAddr: 0x546AA, symSize: 0x0 }
  - { offsetInCU: 0x3757, offset: 0x2E432, size: 0x4, addend: 0x0, symName: __ZL7file239, symObjAddr: 0x4DBE3, symBinAddr: 0x5480B, symSize: 0x0 }
  - { offsetInCU: 0x376E, offset: 0x2E449, size: 0x4, addend: 0x0, symName: __ZL7file240, symObjAddr: 0x4DD1C, symBinAddr: 0x54944, symSize: 0x0 }
  - { offsetInCU: 0x3785, offset: 0x2E460, size: 0x4, addend: 0x0, symName: __ZL7file241, symObjAddr: 0x4DE58, symBinAddr: 0x54A80, symSize: 0x0 }
  - { offsetInCU: 0x37A9, offset: 0x2E484, size: 0x4, addend: 0x0, symName: __ZL7file242, symObjAddr: 0x4DFC3, symBinAddr: 0x54BEB, symSize: 0x0 }
  - { offsetInCU: 0x37C0, offset: 0x2E49B, size: 0x4, addend: 0x0, symName: __ZL7file243, symObjAddr: 0x4E115, symBinAddr: 0x54D3D, symSize: 0x0 }
  - { offsetInCU: 0x37D7, offset: 0x2E4B2, size: 0x4, addend: 0x0, symName: __ZL7file244, symObjAddr: 0x4E256, symBinAddr: 0x54E7E, symSize: 0x0 }
  - { offsetInCU: 0x37EE, offset: 0x2E4C9, size: 0x4, addend: 0x0, symName: __ZL7file245, symObjAddr: 0x4E39D, symBinAddr: 0x54FC5, symSize: 0x0 }
  - { offsetInCU: 0x3805, offset: 0x2E4E0, size: 0x4, addend: 0x0, symName: __ZL7file246, symObjAddr: 0x4E4DB, symBinAddr: 0x55103, symSize: 0x0 }
  - { offsetInCU: 0x3829, offset: 0x2E504, size: 0x4, addend: 0x0, symName: __ZL9layouts48, symObjAddr: 0x474C4, symBinAddr: 0x4E0EC, symSize: 0x0 }
  - { offsetInCU: 0x3840, offset: 0x2E51B, size: 0x4, addend: 0x0, symName: __ZL7file247, symObjAddr: 0x4E660, symBinAddr: 0x55288, symSize: 0x0 }
  - { offsetInCU: 0x3864, offset: 0x2E53F, size: 0x4, addend: 0x0, symName: __ZL7file248, symObjAddr: 0x4E97D, symBinAddr: 0x555A5, symSize: 0x0 }
  - { offsetInCU: 0x3888, offset: 0x2E563, size: 0x4, addend: 0x0, symName: __ZL7file249, symObjAddr: 0x4EC99, symBinAddr: 0x558C1, symSize: 0x0 }
  - { offsetInCU: 0x389F, offset: 0x2E57A, size: 0x4, addend: 0x0, symName: __ZL7file250, symObjAddr: 0x4EFB6, symBinAddr: 0x55BDE, symSize: 0x0 }
  - { offsetInCU: 0x38C3, offset: 0x2E59E, size: 0x4, addend: 0x0, symName: __ZL7file251, symObjAddr: 0x4F2E7, symBinAddr: 0x55F0F, symSize: 0x0 }
  - { offsetInCU: 0x38DA, offset: 0x2E5B5, size: 0x4, addend: 0x0, symName: __ZL7file252, symObjAddr: 0x4F618, symBinAddr: 0x56240, symSize: 0x0 }
  - { offsetInCU: 0x38FE, offset: 0x2E5D9, size: 0x4, addend: 0x0, symName: __ZL7file253, symObjAddr: 0x4F94C, symBinAddr: 0x56574, symSize: 0x0 }
  - { offsetInCU: 0x3922, offset: 0x2E5FD, size: 0x4, addend: 0x0, symName: __ZL7file254, symObjAddr: 0x4FC5A, symBinAddr: 0x56882, symSize: 0x0 }
  - { offsetInCU: 0x3946, offset: 0x2E621, size: 0x4, addend: 0x0, symName: __ZL7file255, symObjAddr: 0x4FF69, symBinAddr: 0x56B91, symSize: 0x0 }
  - { offsetInCU: 0x396A, offset: 0x2E645, size: 0x4, addend: 0x0, symName: __ZL7file256, symObjAddr: 0x502C9, symBinAddr: 0x56EF1, symSize: 0x0 }
  - { offsetInCU: 0x398E, offset: 0x2E669, size: 0x4, addend: 0x0, symName: __ZL7file257, symObjAddr: 0x505F7, symBinAddr: 0x5721F, symSize: 0x0 }
  - { offsetInCU: 0x39B2, offset: 0x2E68D, size: 0x4, addend: 0x0, symName: __ZL7file258, symObjAddr: 0x50973, symBinAddr: 0x5759B, symSize: 0x0 }
  - { offsetInCU: 0x39D6, offset: 0x2E6B1, size: 0x4, addend: 0x0, symName: __ZL7file259, symObjAddr: 0x50CA5, symBinAddr: 0x578CD, symSize: 0x0 }
  - { offsetInCU: 0x39ED, offset: 0x2E6C8, size: 0x4, addend: 0x0, symName: __ZL7file260, symObjAddr: 0x50FD3, symBinAddr: 0x57BFB, symSize: 0x0 }
  - { offsetInCU: 0x3A11, offset: 0x2E6EC, size: 0x4, addend: 0x0, symName: __ZL7file261, symObjAddr: 0x512ED, symBinAddr: 0x57F15, symSize: 0x0 }
  - { offsetInCU: 0x3A28, offset: 0x2E703, size: 0x4, addend: 0x0, symName: __ZL7file262, symObjAddr: 0x51607, symBinAddr: 0x5822F, symSize: 0x0 }
  - { offsetInCU: 0x3A3F, offset: 0x2E71A, size: 0x4, addend: 0x0, symName: __ZL7file263, symObjAddr: 0x51967, symBinAddr: 0x5858F, symSize: 0x0 }
  - { offsetInCU: 0x3A63, offset: 0x2E73E, size: 0x4, addend: 0x0, symName: __ZL7file264, symObjAddr: 0x51C6A, symBinAddr: 0x58892, symSize: 0x0 }
  - { offsetInCU: 0x3A7A, offset: 0x2E755, size: 0x4, addend: 0x0, symName: __ZL7file265, symObjAddr: 0x51F6D, symBinAddr: 0x58B95, symSize: 0x0 }
  - { offsetInCU: 0x3A9E, offset: 0x2E779, size: 0x4, addend: 0x0, symName: __ZL7file266, symObjAddr: 0x522B8, symBinAddr: 0x58EE0, symSize: 0x0 }
  - { offsetInCU: 0x3AC2, offset: 0x2E79D, size: 0x4, addend: 0x0, symName: __ZL7file267, symObjAddr: 0x525D7, symBinAddr: 0x591FF, symSize: 0x0 }
  - { offsetInCU: 0x3AE6, offset: 0x2E7C1, size: 0x4, addend: 0x0, symName: __ZL7file268, symObjAddr: 0x528D9, symBinAddr: 0x59501, symSize: 0x0 }
  - { offsetInCU: 0x3AFD, offset: 0x2E7D8, size: 0x4, addend: 0x0, symName: __ZL7file269, symObjAddr: 0x529B5, symBinAddr: 0x595DD, symSize: 0x0 }
  - { offsetInCU: 0x3B21, offset: 0x2E7FC, size: 0x4, addend: 0x0, symName: __ZL7file270, symObjAddr: 0x52CCD, symBinAddr: 0x598F5, symSize: 0x0 }
  - { offsetInCU: 0x3B38, offset: 0x2E813, size: 0x4, addend: 0x0, symName: __ZL9patches48, symObjAddr: 0x19FEF8, symBinAddr: 0x1A6A98, symSize: 0x0 }
  - { offsetInCU: 0x3B4F, offset: 0x2E82A, size: 0x4, addend: 0x0, symName: __ZL10patchBuf72, symObjAddr: 0x5302F, symBinAddr: 0x59C57, symSize: 0x0 }
  - { offsetInCU: 0x3B66, offset: 0x2E841, size: 0x4, addend: 0x0, symName: __ZL11revisions16, symObjAddr: 0x476A4, symBinAddr: 0x4E2CC, symSize: 0x0 }
  - { offsetInCU: 0x3B7D, offset: 0x2E858, size: 0x4, addend: 0x0, symName: __ZL11platforms49, symObjAddr: 0x476A8, symBinAddr: 0x4E2D0, symSize: 0x0 }
  - { offsetInCU: 0x3B94, offset: 0x2E86F, size: 0x4, addend: 0x0, symName: __ZL7file271, symObjAddr: 0x53033, symBinAddr: 0x59C5B, symSize: 0x0 }
  - { offsetInCU: 0x3BAB, offset: 0x2E886, size: 0x4, addend: 0x0, symName: __ZL7file272, symObjAddr: 0x53173, symBinAddr: 0x59D9B, symSize: 0x0 }
  - { offsetInCU: 0x3BCF, offset: 0x2E8AA, size: 0x4, addend: 0x0, symName: __ZL7file273, symObjAddr: 0x545A2, symBinAddr: 0x5B1CA, symSize: 0x0 }
  - { offsetInCU: 0x3BF3, offset: 0x2E8CE, size: 0x4, addend: 0x0, symName: __ZL7file274, symObjAddr: 0x55B55, symBinAddr: 0x5C77D, symSize: 0x0 }
  - { offsetInCU: 0x3C17, offset: 0x2E8F2, size: 0x4, addend: 0x0, symName: __ZL7file275, symObjAddr: 0x55CBC, symBinAddr: 0x5C8E4, symSize: 0x0 }
  - { offsetInCU: 0x3C2E, offset: 0x2E909, size: 0x4, addend: 0x0, symName: __ZL9layouts49, symObjAddr: 0x4770C, symBinAddr: 0x4E334, symSize: 0x0 }
  - { offsetInCU: 0x3C45, offset: 0x2E920, size: 0x4, addend: 0x0, symName: __ZL7file276, symObjAddr: 0x55DFD, symBinAddr: 0x5CA25, symSize: 0x0 }
  - { offsetInCU: 0x3C69, offset: 0x2E944, size: 0x4, addend: 0x0, symName: __ZL7file277, symObjAddr: 0x56435, symBinAddr: 0x5D05D, symSize: 0x0 }
  - { offsetInCU: 0x3C8D, offset: 0x2E968, size: 0x4, addend: 0x0, symName: __ZL7file278, symObjAddr: 0x571B5, symBinAddr: 0x5DDDD, symSize: 0x0 }
  - { offsetInCU: 0x3CB1, offset: 0x2E98C, size: 0x4, addend: 0x0, symName: __ZL7file279, symObjAddr: 0x577D9, symBinAddr: 0x5E401, symSize: 0x0 }
  - { offsetInCU: 0x3CD5, offset: 0x2E9B0, size: 0x4, addend: 0x0, symName: __ZL7file280, symObjAddr: 0x57DBF, symBinAddr: 0x5E9E7, symSize: 0x0 }
  - { offsetInCU: 0x3CF9, offset: 0x2E9D4, size: 0x4, addend: 0x0, symName: __ZL9patches49, symObjAddr: 0x19FFBC, symBinAddr: 0x1A6B5C, symSize: 0x0 }
  - { offsetInCU: 0x3D10, offset: 0x2E9EB, size: 0x4, addend: 0x0, symName: __ZL10patchBuf73, symObjAddr: 0x58345, symBinAddr: 0x5EF6D, symSize: 0x0 }
  - { offsetInCU: 0x3D27, offset: 0x2EA02, size: 0x4, addend: 0x0, symName: __ZL10patchBuf74, symObjAddr: 0x58349, symBinAddr: 0x5EF71, symSize: 0x0 }
  - { offsetInCU: 0x3D3E, offset: 0x2EA19, size: 0x4, addend: 0x0, symName: __ZL11revisions17, symObjAddr: 0x47770, symBinAddr: 0x4E398, symSize: 0x0 }
  - { offsetInCU: 0x3D55, offset: 0x2EA30, size: 0x4, addend: 0x0, symName: __ZL11platforms50, symObjAddr: 0x47774, symBinAddr: 0x4E39C, symSize: 0x0 }
  - { offsetInCU: 0x3D78, offset: 0x2EA53, size: 0x4, addend: 0x0, symName: __ZL7file281, symObjAddr: 0x5834D, symBinAddr: 0x5EF75, symSize: 0x0 }
  - { offsetInCU: 0x3D8F, offset: 0x2EA6A, size: 0x4, addend: 0x0, symName: __ZL7file282, symObjAddr: 0x58495, symBinAddr: 0x5F0BD, symSize: 0x0 }
  - { offsetInCU: 0x3DA6, offset: 0x2EA81, size: 0x4, addend: 0x0, symName: __ZL7file283, symObjAddr: 0x585D2, symBinAddr: 0x5F1FA, symSize: 0x0 }
  - { offsetInCU: 0x3DBD, offset: 0x2EA98, size: 0x4, addend: 0x0, symName: __ZL7file284, symObjAddr: 0x5871A, symBinAddr: 0x5F342, symSize: 0x0 }
  - { offsetInCU: 0x3DE1, offset: 0x2EABC, size: 0x4, addend: 0x0, symName: __ZL7file285, symObjAddr: 0x591F2, symBinAddr: 0x5FE1A, symSize: 0x0 }
  - { offsetInCU: 0x3DF8, offset: 0x2EAD3, size: 0x4, addend: 0x0, symName: __ZL7file286, symObjAddr: 0x59349, symBinAddr: 0x5FF71, symSize: 0x0 }
  - { offsetInCU: 0x3E0F, offset: 0x2EAEA, size: 0x4, addend: 0x0, symName: __ZL7file287, symObjAddr: 0x59489, symBinAddr: 0x600B1, symSize: 0x0 }
  - { offsetInCU: 0x3E33, offset: 0x2EB0E, size: 0x4, addend: 0x0, symName: __ZL7file288, symObjAddr: 0x595CD, symBinAddr: 0x601F5, symSize: 0x0 }
  - { offsetInCU: 0x3E4A, offset: 0x2EB25, size: 0x4, addend: 0x0, symName: __ZL7file289, symObjAddr: 0x59715, symBinAddr: 0x6033D, symSize: 0x0 }
  - { offsetInCU: 0x3E61, offset: 0x2EB3C, size: 0x4, addend: 0x0, symName: __ZL7file290, symObjAddr: 0x5985D, symBinAddr: 0x60485, symSize: 0x0 }
  - { offsetInCU: 0x3E78, offset: 0x2EB53, size: 0x4, addend: 0x0, symName: __ZL9layouts50, symObjAddr: 0x4783C, symBinAddr: 0x4E464, symSize: 0x0 }
  - { offsetInCU: 0x3E8F, offset: 0x2EB6A, size: 0x4, addend: 0x0, symName: __ZL7file291, symObjAddr: 0x599A1, symBinAddr: 0x605C9, symSize: 0x0 }
  - { offsetInCU: 0x3EB3, offset: 0x2EB8E, size: 0x4, addend: 0x0, symName: __ZL7file292, symObjAddr: 0x5A967, symBinAddr: 0x6158F, symSize: 0x0 }
  - { offsetInCU: 0x3ED7, offset: 0x2EBB2, size: 0x4, addend: 0x0, symName: __ZL7file293, symObjAddr: 0x5AF60, symBinAddr: 0x61B88, symSize: 0x0 }
  - { offsetInCU: 0x3EFB, offset: 0x2EBD6, size: 0x4, addend: 0x0, symName: __ZL7file294, symObjAddr: 0x5B5C5, symBinAddr: 0x621ED, symSize: 0x0 }
  - { offsetInCU: 0x3F1F, offset: 0x2EBFA, size: 0x4, addend: 0x0, symName: __ZL7file295, symObjAddr: 0x5BB93, symBinAddr: 0x627BB, symSize: 0x0 }
  - { offsetInCU: 0x3F43, offset: 0x2EC1E, size: 0x4, addend: 0x0, symName: __ZL7file296, symObjAddr: 0x5CB5C, symBinAddr: 0x63784, symSize: 0x0 }
  - { offsetInCU: 0x3F5A, offset: 0x2EC35, size: 0x4, addend: 0x0, symName: __ZL7file297, symObjAddr: 0x5D12A, symBinAddr: 0x63D52, symSize: 0x0 }
  - { offsetInCU: 0x3F7E, offset: 0x2EC59, size: 0x4, addend: 0x0, symName: __ZL7file298, symObjAddr: 0x5D777, symBinAddr: 0x6439F, symSize: 0x0 }
  - { offsetInCU: 0x3F95, offset: 0x2EC70, size: 0x4, addend: 0x0, symName: __ZL7file299, symObjAddr: 0x5E740, symBinAddr: 0x65368, symSize: 0x0 }
  - { offsetInCU: 0x3FB9, offset: 0x2EC94, size: 0x4, addend: 0x0, symName: __ZL7file300, symObjAddr: 0x5ED76, symBinAddr: 0x6599E, symSize: 0x0 }
  - { offsetInCU: 0x3FD0, offset: 0x2ECAB, size: 0x4, addend: 0x0, symName: __ZL9patches50, symObjAddr: 0x1A00B8, symBinAddr: 0x1A6C58, symSize: 0x0 }
  - { offsetInCU: 0x3FF3, offset: 0x2ECCE, size: 0x4, addend: 0x0, symName: __ZL10patchBuf75, symObjAddr: 0x5F3AC, symBinAddr: 0x65FD4, symSize: 0x0 }
  - { offsetInCU: 0x400A, offset: 0x2ECE5, size: 0x4, addend: 0x0, symName: __ZL11platforms51, symObjAddr: 0x47904, symBinAddr: 0x4E52C, symSize: 0x0 }
  - { offsetInCU: 0x4021, offset: 0x2ECFC, size: 0x4, addend: 0x0, symName: __ZL7file301, symObjAddr: 0x5F3B0, symBinAddr: 0x65FD8, symSize: 0x0 }
  - { offsetInCU: 0x4045, offset: 0x2ED20, size: 0x4, addend: 0x0, symName: __ZL7file302, symObjAddr: 0x5FEFB, symBinAddr: 0x66B23, symSize: 0x0 }
  - { offsetInCU: 0x405C, offset: 0x2ED37, size: 0x4, addend: 0x0, symName: __ZL7file303, symObjAddr: 0x6004C, symBinAddr: 0x66C74, symSize: 0x0 }
  - { offsetInCU: 0x4073, offset: 0x2ED4E, size: 0x4, addend: 0x0, symName: __ZL7file304, symObjAddr: 0x6018B, symBinAddr: 0x66DB3, symSize: 0x0 }
  - { offsetInCU: 0x408A, offset: 0x2ED65, size: 0x4, addend: 0x0, symName: __ZL7file305, symObjAddr: 0x602C3, symBinAddr: 0x66EEB, symSize: 0x0 }
  - { offsetInCU: 0x40A1, offset: 0x2ED7C, size: 0x4, addend: 0x0, symName: __ZL9layouts51, symObjAddr: 0x47968, symBinAddr: 0x4E590, symSize: 0x0 }
  - { offsetInCU: 0x40B8, offset: 0x2ED93, size: 0x4, addend: 0x0, symName: __ZL7file306, symObjAddr: 0x60406, symBinAddr: 0x6702E, symSize: 0x0 }
  - { offsetInCU: 0x40DC, offset: 0x2EDB7, size: 0x4, addend: 0x0, symName: __ZL7file307, symObjAddr: 0x606F1, symBinAddr: 0x67319, symSize: 0x0 }
  - { offsetInCU: 0x4100, offset: 0x2EDDB, size: 0x4, addend: 0x0, symName: __ZL7file308, symObjAddr: 0x60C27, symBinAddr: 0x6784F, symSize: 0x0 }
  - { offsetInCU: 0x4124, offset: 0x2EDFF, size: 0x4, addend: 0x0, symName: __ZL7file309, symObjAddr: 0x61153, symBinAddr: 0x67D7B, symSize: 0x0 }
  - { offsetInCU: 0x4148, offset: 0x2EE23, size: 0x4, addend: 0x0, symName: __ZL7file310, symObjAddr: 0x61680, symBinAddr: 0x682A8, symSize: 0x0 }
  - { offsetInCU: 0x416C, offset: 0x2EE47, size: 0x4, addend: 0x0, symName: __ZL9patches51, symObjAddr: 0x1A01D0, symBinAddr: 0x1A6D70, symSize: 0x0 }
  - { offsetInCU: 0x4183, offset: 0x2EE5E, size: 0x4, addend: 0x0, symName: __ZL10patchBuf76, symObjAddr: 0x61BAB, symBinAddr: 0x687D3, symSize: 0x0 }
  - { offsetInCU: 0x419A, offset: 0x2EE75, size: 0x4, addend: 0x0, symName: __ZL11platforms52, symObjAddr: 0x479CC, symBinAddr: 0x4E5F4, symSize: 0x0 }
  - { offsetInCU: 0x41B1, offset: 0x2EE8C, size: 0x4, addend: 0x0, symName: __ZL7file311, symObjAddr: 0x61BAF, symBinAddr: 0x687D7, symSize: 0x0 }
  - { offsetInCU: 0x41C8, offset: 0x2EEA3, size: 0x4, addend: 0x0, symName: __ZL7file312, symObjAddr: 0x61CDE, symBinAddr: 0x68906, symSize: 0x0 }
  - { offsetInCU: 0x41EC, offset: 0x2EEC7, size: 0x4, addend: 0x0, symName: __ZL9layouts52, symObjAddr: 0x479F4, symBinAddr: 0x4E61C, symSize: 0x0 }
  - { offsetInCU: 0x4203, offset: 0x2EEDE, size: 0x4, addend: 0x0, symName: __ZL7file313, symObjAddr: 0x6280E, symBinAddr: 0x69436, symSize: 0x0 }
  - { offsetInCU: 0x4227, offset: 0x2EF02, size: 0x4, addend: 0x0, symName: __ZL7file314, symObjAddr: 0x62ECB, symBinAddr: 0x69AF3, symSize: 0x0 }
  - { offsetInCU: 0x424B, offset: 0x2EF26, size: 0x4, addend: 0x0, symName: __ZL9patches52, symObjAddr: 0x1A02B0, symBinAddr: 0x1A6E50, symSize: 0x0 }
  - { offsetInCU: 0x4262, offset: 0x2EF3D, size: 0x4, addend: 0x0, symName: __ZL10patchBuf77, symObjAddr: 0x635EA, symBinAddr: 0x6A212, symSize: 0x0 }
  - { offsetInCU: 0x4279, offset: 0x2EF54, size: 0x4, addend: 0x0, symName: __ZL11platforms53, symObjAddr: 0x47A1C, symBinAddr: 0x4E644, symSize: 0x0 }
  - { offsetInCU: 0x429C, offset: 0x2EF77, size: 0x4, addend: 0x0, symName: __ZL7file315, symObjAddr: 0x635EE, symBinAddr: 0x6A216, symSize: 0x0 }
  - { offsetInCU: 0x42B3, offset: 0x2EF8E, size: 0x4, addend: 0x0, symName: __ZL7file316, symObjAddr: 0x6372F, symBinAddr: 0x6A357, symSize: 0x0 }
  - { offsetInCU: 0x42CA, offset: 0x2EFA5, size: 0x4, addend: 0x0, symName: __ZL7file317, symObjAddr: 0x63859, symBinAddr: 0x6A481, symSize: 0x0 }
  - { offsetInCU: 0x42E1, offset: 0x2EFBC, size: 0x4, addend: 0x0, symName: __ZL7file318, symObjAddr: 0x63999, symBinAddr: 0x6A5C1, symSize: 0x0 }
  - { offsetInCU: 0x42F8, offset: 0x2EFD3, size: 0x4, addend: 0x0, symName: __ZL7file319, symObjAddr: 0x63AEB, symBinAddr: 0x6A713, symSize: 0x0 }
  - { offsetInCU: 0x431C, offset: 0x2EFF7, size: 0x4, addend: 0x0, symName: __ZL7file320, symObjAddr: 0x63C4F, symBinAddr: 0x6A877, symSize: 0x0 }
  - { offsetInCU: 0x4333, offset: 0x2F00E, size: 0x4, addend: 0x0, symName: __ZL7file321, symObjAddr: 0x63DAA, symBinAddr: 0x6A9D2, symSize: 0x0 }
  - { offsetInCU: 0x4357, offset: 0x2F032, size: 0x4, addend: 0x0, symName: __ZL7file322, symObjAddr: 0x63F27, symBinAddr: 0x6AB4F, symSize: 0x0 }
  - { offsetInCU: 0x436E, offset: 0x2F049, size: 0x4, addend: 0x0, symName: __ZL7file323, symObjAddr: 0x64080, symBinAddr: 0x6ACA8, symSize: 0x0 }
  - { offsetInCU: 0x4392, offset: 0x2F06D, size: 0x4, addend: 0x0, symName: __ZL7file324, symObjAddr: 0x64204, symBinAddr: 0x6AE2C, symSize: 0x0 }
  - { offsetInCU: 0x43A9, offset: 0x2F084, size: 0x4, addend: 0x0, symName: __ZL7file325, symObjAddr: 0x6432F, symBinAddr: 0x6AF57, symSize: 0x0 }
  - { offsetInCU: 0x43C0, offset: 0x2F09B, size: 0x4, addend: 0x0, symName: __ZL9layouts53, symObjAddr: 0x47AF8, symBinAddr: 0x4E720, symSize: 0x0 }
  - { offsetInCU: 0x43D7, offset: 0x2F0B2, size: 0x4, addend: 0x0, symName: __ZL7file326, symObjAddr: 0x6448F, symBinAddr: 0x6B0B7, symSize: 0x0 }
  - { offsetInCU: 0x43FB, offset: 0x2F0D6, size: 0x4, addend: 0x0, symName: __ZL7file327, symObjAddr: 0x64A4F, symBinAddr: 0x6B677, symSize: 0x0 }
  - { offsetInCU: 0x441F, offset: 0x2F0FA, size: 0x4, addend: 0x0, symName: __ZL7file328, symObjAddr: 0x6505A, symBinAddr: 0x6BC82, symSize: 0x0 }
  - { offsetInCU: 0x4443, offset: 0x2F11E, size: 0x4, addend: 0x0, symName: __ZL7file329, symObjAddr: 0x65669, symBinAddr: 0x6C291, symSize: 0x0 }
  - { offsetInCU: 0x4467, offset: 0x2F142, size: 0x4, addend: 0x0, symName: __ZL7file330, symObjAddr: 0x65C89, symBinAddr: 0x6C8B1, symSize: 0x0 }
  - { offsetInCU: 0x448B, offset: 0x2F166, size: 0x4, addend: 0x0, symName: __ZL7file331, symObjAddr: 0x662AA, symBinAddr: 0x6CED2, symSize: 0x0 }
  - { offsetInCU: 0x44AF, offset: 0x2F18A, size: 0x4, addend: 0x0, symName: __ZL7file332, symObjAddr: 0x668CD, symBinAddr: 0x6D4F5, symSize: 0x0 }
  - { offsetInCU: 0x44D3, offset: 0x2F1AE, size: 0x4, addend: 0x0, symName: __ZL7file333, symObjAddr: 0x66F2F, symBinAddr: 0x6DB57, symSize: 0x0 }
  - { offsetInCU: 0x44F7, offset: 0x2F1D2, size: 0x4, addend: 0x0, symName: __ZL7file334, symObjAddr: 0x67566, symBinAddr: 0x6E18E, symSize: 0x0 }
  - { offsetInCU: 0x450E, offset: 0x2F1E9, size: 0x4, addend: 0x0, symName: __ZL7file335, symObjAddr: 0x67B86, symBinAddr: 0x6E7AE, symSize: 0x0 }
  - { offsetInCU: 0x4532, offset: 0x2F20D, size: 0x4, addend: 0x0, symName: __ZL7file336, symObjAddr: 0x681A4, symBinAddr: 0x6EDCC, symSize: 0x0 }
  - { offsetInCU: 0x4549, offset: 0x2F224, size: 0x4, addend: 0x0, symName: __ZL9patches53, symObjAddr: 0x1A03AC, symBinAddr: 0x1A6F4C, symSize: 0x0 }
  - { offsetInCU: 0x4560, offset: 0x2F23B, size: 0x4, addend: 0x0, symName: __ZL10patchBuf78, symObjAddr: 0x687C5, symBinAddr: 0x6F3ED, symSize: 0x0 }
  - { offsetInCU: 0x4577, offset: 0x2F252, size: 0x4, addend: 0x0, symName: __ZL11revisions18, symObjAddr: 0x47BD4, symBinAddr: 0x4E7FC, symSize: 0x0 }
  - { offsetInCU: 0x458E, offset: 0x2F269, size: 0x4, addend: 0x0, symName: __ZL11platforms54, symObjAddr: 0x47BD8, symBinAddr: 0x4E800, symSize: 0x0 }
  - { offsetInCU: 0x45A5, offset: 0x2F280, size: 0x4, addend: 0x0, symName: __ZL7file337, symObjAddr: 0x687C9, symBinAddr: 0x6F3F1, symSize: 0x0 }
  - { offsetInCU: 0x45BC, offset: 0x2F297, size: 0x4, addend: 0x0, symName: __ZL7file338, symObjAddr: 0x68A07, symBinAddr: 0x6F62F, symSize: 0x0 }
  - { offsetInCU: 0x45E0, offset: 0x2F2BB, size: 0x4, addend: 0x0, symName: __ZL7file339, symObjAddr: 0x68BC6, symBinAddr: 0x6F7EE, symSize: 0x0 }
  - { offsetInCU: 0x4604, offset: 0x2F2DF, size: 0x4, addend: 0x0, symName: __ZL7file340, symObjAddr: 0x68D87, symBinAddr: 0x6F9AF, symSize: 0x0 }
  - { offsetInCU: 0x4628, offset: 0x2F303, size: 0x4, addend: 0x0, symName: __ZL9layouts54, symObjAddr: 0x47C64, symBinAddr: 0x4E88C, symSize: 0x0 }
  - { offsetInCU: 0x463F, offset: 0x2F31A, size: 0x4, addend: 0x0, symName: __ZL7file341, symObjAddr: 0x68F1C, symBinAddr: 0x6FB44, symSize: 0x0 }
  - { offsetInCU: 0x4663, offset: 0x2F33E, size: 0x4, addend: 0x0, symName: __ZL7file342, symObjAddr: 0x6926B, symBinAddr: 0x6FE93, symSize: 0x0 }
  - { offsetInCU: 0x467A, offset: 0x2F355, size: 0x4, addend: 0x0, symName: __ZL7file343, symObjAddr: 0x695BA, symBinAddr: 0x701E2, symSize: 0x0 }
  - { offsetInCU: 0x4691, offset: 0x2F36C, size: 0x4, addend: 0x0, symName: __ZL7file344, symObjAddr: 0x69909, symBinAddr: 0x70531, symSize: 0x0 }
  - { offsetInCU: 0x46B5, offset: 0x2F390, size: 0x4, addend: 0x0, symName: __ZL7file345, symObjAddr: 0x69C38, symBinAddr: 0x70860, symSize: 0x0 }
  - { offsetInCU: 0x46CC, offset: 0x2F3A7, size: 0x4, addend: 0x0, symName: __ZL7file346, symObjAddr: 0x69F67, symBinAddr: 0x70B8F, symSize: 0x0 }
  - { offsetInCU: 0x46F0, offset: 0x2F3CB, size: 0x4, addend: 0x0, symName: __ZL9patches54, symObjAddr: 0x1A048C, symBinAddr: 0x1A702C, symSize: 0x0 }
  - { offsetInCU: 0x4707, offset: 0x2F3E2, size: 0x4, addend: 0x0, symName: __ZL10patchBuf79, symObjAddr: 0x6A2B3, symBinAddr: 0x70EDB, symSize: 0x0 }
  - { offsetInCU: 0x471E, offset: 0x2F3F9, size: 0x4, addend: 0x0, symName: __ZL11revisions19, symObjAddr: 0x47CF0, symBinAddr: 0x4E918, symSize: 0x0 }
  - { offsetInCU: 0x4735, offset: 0x2F410, size: 0x4, addend: 0x0, symName: __ZL11platforms55, symObjAddr: 0x47CF4, symBinAddr: 0x4E91C, symSize: 0x0 }
  - { offsetInCU: 0x4758, offset: 0x2F433, size: 0x4, addend: 0x0, symName: __ZL7file347, symObjAddr: 0x6A2B7, symBinAddr: 0x70EDF, symSize: 0x0 }
  - { offsetInCU: 0x476F, offset: 0x2F44A, size: 0x4, addend: 0x0, symName: __ZL7file348, symObjAddr: 0x6A3F4, symBinAddr: 0x7101C, symSize: 0x0 }
  - { offsetInCU: 0x4786, offset: 0x2F461, size: 0x4, addend: 0x0, symName: __ZL7file349, symObjAddr: 0x6A54E, symBinAddr: 0x71176, symSize: 0x0 }
  - { offsetInCU: 0x47AA, offset: 0x2F485, size: 0x4, addend: 0x0, symName: __ZL7file350, symObjAddr: 0x6A6C7, symBinAddr: 0x712EF, symSize: 0x0 }
  - { offsetInCU: 0x47CE, offset: 0x2F4A9, size: 0x4, addend: 0x0, symName: __ZL7file351, symObjAddr: 0x6A83C, symBinAddr: 0x71464, symSize: 0x0 }
  - { offsetInCU: 0x47E5, offset: 0x2F4C0, size: 0x4, addend: 0x0, symName: __ZL7file352, symObjAddr: 0x6A9B9, symBinAddr: 0x715E1, symSize: 0x0 }
  - { offsetInCU: 0x4809, offset: 0x2F4E4, size: 0x4, addend: 0x0, symName: __ZL7file353, symObjAddr: 0x6AB4D, symBinAddr: 0x71775, symSize: 0x0 }
  - { offsetInCU: 0x4820, offset: 0x2F4FB, size: 0x4, addend: 0x0, symName: __ZL7file354, symObjAddr: 0x6ACDF, symBinAddr: 0x71907, symSize: 0x0 }
  - { offsetInCU: 0x4844, offset: 0x2F51F, size: 0x4, addend: 0x0, symName: __ZL7file355, symObjAddr: 0x6AE52, symBinAddr: 0x71A7A, symSize: 0x0 }
  - { offsetInCU: 0x485B, offset: 0x2F536, size: 0x4, addend: 0x0, symName: __ZL7file356, symObjAddr: 0x6AF85, symBinAddr: 0x71BAD, symSize: 0x0 }
  - { offsetInCU: 0x4872, offset: 0x2F54D, size: 0x4, addend: 0x0, symName: __ZL7file357, symObjAddr: 0x6B0DA, symBinAddr: 0x71D02, symSize: 0x0 }
  - { offsetInCU: 0x4896, offset: 0x2F571, size: 0x4, addend: 0x0, symName: __ZL7file358, symObjAddr: 0x6B202, symBinAddr: 0x71E2A, symSize: 0x0 }
  - { offsetInCU: 0x48AD, offset: 0x2F588, size: 0x4, addend: 0x0, symName: __ZL7file359, symObjAddr: 0x6B33A, symBinAddr: 0x71F62, symSize: 0x0 }
  - { offsetInCU: 0x48C4, offset: 0x2F59F, size: 0x4, addend: 0x0, symName: __ZL7file360, symObjAddr: 0x6B49E, symBinAddr: 0x720C6, symSize: 0x0 }
  - { offsetInCU: 0x48E8, offset: 0x2F5C3, size: 0x4, addend: 0x0, symName: __ZL7file361, symObjAddr: 0x6B5C2, symBinAddr: 0x721EA, symSize: 0x0 }
  - { offsetInCU: 0x48FF, offset: 0x2F5DA, size: 0x4, addend: 0x0, symName: __ZL7file362, symObjAddr: 0x6B747, symBinAddr: 0x7236F, symSize: 0x0 }
  - { offsetInCU: 0x4923, offset: 0x2F5FE, size: 0x4, addend: 0x0, symName: __ZL7file363, symObjAddr: 0x6B8B1, symBinAddr: 0x724D9, symSize: 0x0 }
  - { offsetInCU: 0x493A, offset: 0x2F615, size: 0x4, addend: 0x0, symName: __ZL7file364, symObjAddr: 0x6BA15, symBinAddr: 0x7263D, symSize: 0x0 }
  - { offsetInCU: 0x4951, offset: 0x2F62C, size: 0x4, addend: 0x0, symName: __ZL7file365, symObjAddr: 0x6BB92, symBinAddr: 0x727BA, symSize: 0x0 }
  - { offsetInCU: 0x4975, offset: 0x2F650, size: 0x4, addend: 0x0, symName: __ZL7file366, symObjAddr: 0x6BCDC, symBinAddr: 0x72904, symSize: 0x0 }
  - { offsetInCU: 0x4999, offset: 0x2F674, size: 0x4, addend: 0x0, symName: __ZL7file367, symObjAddr: 0x6BE53, symBinAddr: 0x72A7B, symSize: 0x0 }
  - { offsetInCU: 0x49BD, offset: 0x2F698, size: 0x4, addend: 0x0, symName: __ZL7file368, symObjAddr: 0x6BFF2, symBinAddr: 0x72C1A, symSize: 0x0 }
  - { offsetInCU: 0x49E1, offset: 0x2F6BC, size: 0x4, addend: 0x0, symName: __ZL7file369, symObjAddr: 0x6C180, symBinAddr: 0x72DA8, symSize: 0x0 }
  - { offsetInCU: 0x4A05, offset: 0x2F6E0, size: 0x4, addend: 0x0, symName: __ZL7file370, symObjAddr: 0x6C2E6, symBinAddr: 0x72F0E, symSize: 0x0 }
  - { offsetInCU: 0x4A29, offset: 0x2F704, size: 0x4, addend: 0x0, symName: __ZL7file371, symObjAddr: 0x6C469, symBinAddr: 0x73091, symSize: 0x0 }
  - { offsetInCU: 0x4A4D, offset: 0x2F728, size: 0x4, addend: 0x0, symName: __ZL7file372, symObjAddr: 0x6C5FC, symBinAddr: 0x73224, symSize: 0x0 }
  - { offsetInCU: 0x4A71, offset: 0x2F74C, size: 0x4, addend: 0x0, symName: __ZL7file373, symObjAddr: 0x6C786, symBinAddr: 0x733AE, symSize: 0x0 }
  - { offsetInCU: 0x4A88, offset: 0x2F763, size: 0x4, addend: 0x0, symName: __ZL7file374, symObjAddr: 0x6C914, symBinAddr: 0x7353C, symSize: 0x0 }
  - { offsetInCU: 0x4A9F, offset: 0x2F77A, size: 0x4, addend: 0x0, symName: __ZL7file375, symObjAddr: 0x6CA7E, symBinAddr: 0x736A6, symSize: 0x0 }
  - { offsetInCU: 0x4AB6, offset: 0x2F791, size: 0x4, addend: 0x0, symName: __ZL7file376, symObjAddr: 0x6CBFA, symBinAddr: 0x73822, symSize: 0x0 }
  - { offsetInCU: 0x4ACD, offset: 0x2F7A8, size: 0x4, addend: 0x0, symName: __ZL7file377, symObjAddr: 0x6CD3B, symBinAddr: 0x73963, symSize: 0x0 }
  - { offsetInCU: 0x4AE4, offset: 0x2F7BF, size: 0x4, addend: 0x0, symName: __ZL7file378, symObjAddr: 0x6CE7C, symBinAddr: 0x73AA4, symSize: 0x0 }
  - { offsetInCU: 0x4AFB, offset: 0x2F7D6, size: 0x4, addend: 0x0, symName: __ZL9layouts55, symObjAddr: 0x47F74, symBinAddr: 0x4EB9C, symSize: 0x0 }
  - { offsetInCU: 0x4B12, offset: 0x2F7ED, size: 0x4, addend: 0x0, symName: __ZL7file379, symObjAddr: 0x6CFC5, symBinAddr: 0x73BED, symSize: 0x0 }
  - { offsetInCU: 0x4B36, offset: 0x2F811, size: 0x4, addend: 0x0, symName: __ZL7file380, symObjAddr: 0x6D37E, symBinAddr: 0x73FA6, symSize: 0x0 }
  - { offsetInCU: 0x4B5A, offset: 0x2F835, size: 0x4, addend: 0x0, symName: __ZL7file381, symObjAddr: 0x6DA13, symBinAddr: 0x7463B, symSize: 0x0 }
  - { offsetInCU: 0x4B7E, offset: 0x2F859, size: 0x4, addend: 0x0, symName: __ZL7file382, symObjAddr: 0x6E0A4, symBinAddr: 0x74CCC, symSize: 0x0 }
  - { offsetInCU: 0x4BA2, offset: 0x2F87D, size: 0x4, addend: 0x0, symName: __ZL7file383, symObjAddr: 0x6E738, symBinAddr: 0x75360, symSize: 0x0 }
  - { offsetInCU: 0x4BB9, offset: 0x2F894, size: 0x4, addend: 0x0, symName: __ZL7file384, symObjAddr: 0x6EDCC, symBinAddr: 0x759F4, symSize: 0x0 }
  - { offsetInCU: 0x4BDD, offset: 0x2F8B8, size: 0x4, addend: 0x0, symName: __ZL7file385, symObjAddr: 0x70455, symBinAddr: 0x7707D, symSize: 0x0 }
  - { offsetInCU: 0x4C01, offset: 0x2F8DC, size: 0x4, addend: 0x0, symName: __ZL7file386, symObjAddr: 0x713A2, symBinAddr: 0x77FCA, symSize: 0x0 }
  - { offsetInCU: 0x4C25, offset: 0x2F900, size: 0x4, addend: 0x0, symName: __ZL7file387, symObjAddr: 0x71A38, symBinAddr: 0x78660, symSize: 0x0 }
  - { offsetInCU: 0x4C49, offset: 0x2F924, size: 0x4, addend: 0x0, symName: __ZL7file388, symObjAddr: 0x727B9, symBinAddr: 0x793E1, symSize: 0x0 }
  - { offsetInCU: 0x4C60, offset: 0x2F93B, size: 0x4, addend: 0x0, symName: __ZL7file389, symObjAddr: 0x72E4A, symBinAddr: 0x79A72, symSize: 0x0 }
  - { offsetInCU: 0x4C84, offset: 0x2F95F, size: 0x4, addend: 0x0, symName: __ZL7file390, symObjAddr: 0x73BC8, symBinAddr: 0x7A7F0, symSize: 0x0 }
  - { offsetInCU: 0x4CA8, offset: 0x2F983, size: 0x4, addend: 0x0, symName: __ZL7file391, symObjAddr: 0x74A91, symBinAddr: 0x7B6B9, symSize: 0x0 }
  - { offsetInCU: 0x4CCC, offset: 0x2F9A7, size: 0x4, addend: 0x0, symName: __ZL7file392, symObjAddr: 0x750D5, symBinAddr: 0x7BCFD, symSize: 0x0 }
  - { offsetInCU: 0x4CE3, offset: 0x2F9BE, size: 0x4, addend: 0x0, symName: __ZL7file393, symObjAddr: 0x75E53, symBinAddr: 0x7CA7B, symSize: 0x0 }
  - { offsetInCU: 0x4D07, offset: 0x2F9E2, size: 0x4, addend: 0x0, symName: __ZL7file394, symObjAddr: 0x764EA, symBinAddr: 0x7D112, symSize: 0x0 }
  - { offsetInCU: 0x4D1E, offset: 0x2F9F9, size: 0x4, addend: 0x0, symName: __ZL7file395, symObjAddr: 0x76B7F, symBinAddr: 0x7D7A7, symSize: 0x0 }
  - { offsetInCU: 0x4D35, offset: 0x2FA10, size: 0x4, addend: 0x0, symName: __ZL7file396, symObjAddr: 0x77214, symBinAddr: 0x7DE3C, symSize: 0x0 }
  - { offsetInCU: 0x4D4C, offset: 0x2FA27, size: 0x4, addend: 0x0, symName: __ZL7file397, symObjAddr: 0x778A9, symBinAddr: 0x7E4D1, symSize: 0x0 }
  - { offsetInCU: 0x4D6F, offset: 0x2FA4A, size: 0x4, addend: 0x0, symName: __ZL7file398, symObjAddr: 0x7797E, symBinAddr: 0x7E5A6, symSize: 0x0 }
  - { offsetInCU: 0x4D86, offset: 0x2FA61, size: 0x4, addend: 0x0, symName: __ZL7file399, symObjAddr: 0x78013, symBinAddr: 0x7EC3B, symSize: 0x0 }
  - { offsetInCU: 0x4D9D, offset: 0x2FA78, size: 0x4, addend: 0x0, symName: __ZL7file400, symObjAddr: 0x78F60, symBinAddr: 0x7FB88, symSize: 0x0 }
  - { offsetInCU: 0x4DC1, offset: 0x2FA9C, size: 0x4, addend: 0x0, symName: __ZL7file401, symObjAddr: 0x79EB4, symBinAddr: 0x80ADC, symSize: 0x0 }
  - { offsetInCU: 0x4DD8, offset: 0x2FAB3, size: 0x4, addend: 0x0, symName: __ZL7file402, symObjAddr: 0x7A545, symBinAddr: 0x8116D, symSize: 0x0 }
  - { offsetInCU: 0x4DFC, offset: 0x2FAD7, size: 0x4, addend: 0x0, symName: __ZL7file403, symObjAddr: 0x7ABD7, symBinAddr: 0x817FF, symSize: 0x0 }
  - { offsetInCU: 0x4E13, offset: 0x2FAEE, size: 0x4, addend: 0x0, symName: __ZL7file404, symObjAddr: 0x7B26C, symBinAddr: 0x81E94, symSize: 0x0 }
  - { offsetInCU: 0x4E37, offset: 0x2FB12, size: 0x4, addend: 0x0, symName: __ZL7file405, symObjAddr: 0x7BFF9, symBinAddr: 0x82C21, symSize: 0x0 }
  - { offsetInCU: 0x4E5B, offset: 0x2FB36, size: 0x4, addend: 0x0, symName: __ZL7file406, symObjAddr: 0x7CE40, symBinAddr: 0x83A68, symSize: 0x0 }
  - { offsetInCU: 0x4E72, offset: 0x2FB4D, size: 0x4, addend: 0x0, symName: __ZL7file407, symObjAddr: 0x7D4D5, symBinAddr: 0x840FD, symSize: 0x0 }
  - { offsetInCU: 0x4E96, offset: 0x2FB71, size: 0x4, addend: 0x0, symName: __ZL7file408, symObjAddr: 0x7E419, symBinAddr: 0x85041, symSize: 0x0 }
  - { offsetInCU: 0x4EBA, offset: 0x2FB95, size: 0x4, addend: 0x0, symName: __ZL7file409, symObjAddr: 0x7F002, symBinAddr: 0x85C2A, symSize: 0x0 }
  - { offsetInCU: 0x4EDD, offset: 0x2FBB8, size: 0x4, addend: 0x0, symName: __ZL7file410, symObjAddr: 0x7F0D2, symBinAddr: 0x85CFA, symSize: 0x0 }
  - { offsetInCU: 0x4F01, offset: 0x2FBDC, size: 0x4, addend: 0x0, symName: __ZL9patches55, symObjAddr: 0x1A0550, symBinAddr: 0x1A70F0, symSize: 0x0 }
  - { offsetInCU: 0x4F18, offset: 0x2FBF3, size: 0x4, addend: 0x0, symName: __ZL10patchBuf80, symObjAddr: 0x7FCB8, symBinAddr: 0x868E0, symSize: 0x0 }
  - { offsetInCU: 0x4F2F, offset: 0x2FC0A, size: 0x4, addend: 0x0, symName: __ZL11revisions20, symObjAddr: 0x481F4, symBinAddr: 0x4EE1C, symSize: 0x0 }
  - { offsetInCU: 0x4F52, offset: 0x2FC2D, size: 0x4, addend: 0x0, symName: __ZL11platforms56, symObjAddr: 0x48204, symBinAddr: 0x4EE2C, symSize: 0x0 }
  - { offsetInCU: 0x4F75, offset: 0x2FC50, size: 0x4, addend: 0x0, symName: __ZL7file411, symObjAddr: 0x7FCBC, symBinAddr: 0x868E4, symSize: 0x0 }
  - { offsetInCU: 0x4F8C, offset: 0x2FC67, size: 0x4, addend: 0x0, symName: __ZL7file412, symObjAddr: 0x7FE06, symBinAddr: 0x86A2E, symSize: 0x0 }
  - { offsetInCU: 0x4FA3, offset: 0x2FC7E, size: 0x4, addend: 0x0, symName: __ZL7file413, symObjAddr: 0x7FF6C, symBinAddr: 0x86B94, symSize: 0x0 }
  - { offsetInCU: 0x4FBA, offset: 0x2FC95, size: 0x4, addend: 0x0, symName: __ZL7file414, symObjAddr: 0x800B2, symBinAddr: 0x86CDA, symSize: 0x0 }
  - { offsetInCU: 0x4FD1, offset: 0x2FCAC, size: 0x4, addend: 0x0, symName: __ZL7file415, symObjAddr: 0x801EB, symBinAddr: 0x86E13, symSize: 0x0 }
  - { offsetInCU: 0x4FF5, offset: 0x2FCD0, size: 0x4, addend: 0x0, symName: __ZL7file416, symObjAddr: 0x80C38, symBinAddr: 0x87860, symSize: 0x0 }
  - { offsetInCU: 0x500C, offset: 0x2FCE7, size: 0x4, addend: 0x0, symName: __ZL7file417, symObjAddr: 0x80D7A, symBinAddr: 0x879A2, symSize: 0x0 }
  - { offsetInCU: 0x5023, offset: 0x2FCFE, size: 0x4, addend: 0x0, symName: __ZL7file418, symObjAddr: 0x80EDE, symBinAddr: 0x87B06, symSize: 0x0 }
  - { offsetInCU: 0x503A, offset: 0x2FD15, size: 0x4, addend: 0x0, symName: __ZL7file419, symObjAddr: 0x81020, symBinAddr: 0x87C48, symSize: 0x0 }
  - { offsetInCU: 0x5051, offset: 0x2FD2C, size: 0x4, addend: 0x0, symName: __ZL7file420, symObjAddr: 0x81162, symBinAddr: 0x87D8A, symSize: 0x0 }
  - { offsetInCU: 0x5068, offset: 0x2FD43, size: 0x4, addend: 0x0, symName: __ZL7file421, symObjAddr: 0x812A4, symBinAddr: 0x87ECC, symSize: 0x0 }
  - { offsetInCU: 0x507F, offset: 0x2FD5A, size: 0x4, addend: 0x0, symName: __ZL7file422, symObjAddr: 0x813F1, symBinAddr: 0x88019, symSize: 0x0 }
  - { offsetInCU: 0x50A3, offset: 0x2FD7E, size: 0x4, addend: 0x0, symName: __ZL7file423, symObjAddr: 0x81EFA, symBinAddr: 0x88B22, symSize: 0x0 }
  - { offsetInCU: 0x50BA, offset: 0x2FD95, size: 0x4, addend: 0x0, symName: __ZL7file424, symObjAddr: 0x8202F, symBinAddr: 0x88C57, symSize: 0x0 }
  - { offsetInCU: 0x50D1, offset: 0x2FDAC, size: 0x4, addend: 0x0, symName: __ZL7file425, symObjAddr: 0x82166, symBinAddr: 0x88D8E, symSize: 0x0 }
  - { offsetInCU: 0x50F5, offset: 0x2FDD0, size: 0x4, addend: 0x0, symName: __ZL7file426, symObjAddr: 0x82CB4, symBinAddr: 0x898DC, symSize: 0x0 }
  - { offsetInCU: 0x5119, offset: 0x2FDF4, size: 0x4, addend: 0x0, symName: __ZL7file427, symObjAddr: 0x8380A, symBinAddr: 0x8A432, symSize: 0x0 }
  - { offsetInCU: 0x5130, offset: 0x2FE0B, size: 0x4, addend: 0x0, symName: __ZL7file428, symObjAddr: 0x83941, symBinAddr: 0x8A569, symSize: 0x0 }
  - { offsetInCU: 0x5147, offset: 0x2FE22, size: 0x4, addend: 0x0, symName: __ZL7file429, symObjAddr: 0x83A7A, symBinAddr: 0x8A6A2, symSize: 0x0 }
  - { offsetInCU: 0x516B, offset: 0x2FE46, size: 0x4, addend: 0x0, symName: __ZL7file430, symObjAddr: 0x83B90, symBinAddr: 0x8A7B8, symSize: 0x0 }
  - { offsetInCU: 0x5182, offset: 0x2FE5D, size: 0x4, addend: 0x0, symName: __ZL7file431, symObjAddr: 0x83CCE, symBinAddr: 0x8A8F6, symSize: 0x0 }
  - { offsetInCU: 0x5199, offset: 0x2FE74, size: 0x4, addend: 0x0, symName: __ZL7file432, symObjAddr: 0x83E06, symBinAddr: 0x8AA2E, symSize: 0x0 }
  - { offsetInCU: 0x51BD, offset: 0x2FE98, size: 0x4, addend: 0x0, symName: __ZL7file433, symObjAddr: 0x83F51, symBinAddr: 0x8AB79, symSize: 0x0 }
  - { offsetInCU: 0x51D4, offset: 0x2FEAF, size: 0x4, addend: 0x0, symName: __ZL7file434, symObjAddr: 0x840A2, symBinAddr: 0x8ACCA, symSize: 0x0 }
  - { offsetInCU: 0x51EB, offset: 0x2FEC6, size: 0x4, addend: 0x0, symName: __ZL7file435, symObjAddr: 0x841E3, symBinAddr: 0x8AE0B, symSize: 0x0 }
  - { offsetInCU: 0x5202, offset: 0x2FEDD, size: 0x4, addend: 0x0, symName: __ZL7file436, symObjAddr: 0x8432C, symBinAddr: 0x8AF54, symSize: 0x0 }
  - { offsetInCU: 0x5226, offset: 0x2FF01, size: 0x4, addend: 0x0, symName: __ZL7file437, symObjAddr: 0x8445D, symBinAddr: 0x8B085, symSize: 0x0 }
  - { offsetInCU: 0x524A, offset: 0x2FF25, size: 0x4, addend: 0x0, symName: __ZL7file438, symObjAddr: 0x845AC, symBinAddr: 0x8B1D4, symSize: 0x0 }
  - { offsetInCU: 0x5261, offset: 0x2FF3C, size: 0x4, addend: 0x0, symName: __ZL7file439, symObjAddr: 0x846F6, symBinAddr: 0x8B31E, symSize: 0x0 }
  - { offsetInCU: 0x5278, offset: 0x2FF53, size: 0x4, addend: 0x0, symName: __ZL7file440, symObjAddr: 0x8485E, symBinAddr: 0x8B486, symSize: 0x0 }
  - { offsetInCU: 0x528F, offset: 0x2FF6A, size: 0x4, addend: 0x0, symName: __ZL7file441, symObjAddr: 0x849AB, symBinAddr: 0x8B5D3, symSize: 0x0 }
  - { offsetInCU: 0x52A6, offset: 0x2FF81, size: 0x4, addend: 0x0, symName: __ZL7file442, symObjAddr: 0x84B14, symBinAddr: 0x8B73C, symSize: 0x0 }
  - { offsetInCU: 0x52BD, offset: 0x2FF98, size: 0x4, addend: 0x0, symName: __ZL7file443, symObjAddr: 0x84C7C, symBinAddr: 0x8B8A4, symSize: 0x0 }
  - { offsetInCU: 0x52D4, offset: 0x2FFAF, size: 0x4, addend: 0x0, symName: __ZL7file444, symObjAddr: 0x84DBD, symBinAddr: 0x8B9E5, symSize: 0x0 }
  - { offsetInCU: 0x52EB, offset: 0x2FFC6, size: 0x4, addend: 0x0, symName: __ZL7file445, symObjAddr: 0x84F01, symBinAddr: 0x8BB29, symSize: 0x0 }
  - { offsetInCU: 0x5302, offset: 0x2FFDD, size: 0x4, addend: 0x0, symName: __ZL7file446, symObjAddr: 0x85070, symBinAddr: 0x8BC98, symSize: 0x0 }
  - { offsetInCU: 0x5319, offset: 0x2FFF4, size: 0x4, addend: 0x0, symName: __ZL7file447, symObjAddr: 0x851BA, symBinAddr: 0x8BDE2, symSize: 0x0 }
  - { offsetInCU: 0x5330, offset: 0x3000B, size: 0x4, addend: 0x0, symName: __ZL7file448, symObjAddr: 0x852FD, symBinAddr: 0x8BF25, symSize: 0x0 }
  - { offsetInCU: 0x5347, offset: 0x30022, size: 0x4, addend: 0x0, symName: __ZL7file449, symObjAddr: 0x85434, symBinAddr: 0x8C05C, symSize: 0x0 }
  - { offsetInCU: 0x535E, offset: 0x30039, size: 0x4, addend: 0x0, symName: __ZL7file450, symObjAddr: 0x85581, symBinAddr: 0x8C1A9, symSize: 0x0 }
  - { offsetInCU: 0x5382, offset: 0x3005D, size: 0x4, addend: 0x0, symName: __ZL7file451, symObjAddr: 0x860AD, symBinAddr: 0x8CCD5, symSize: 0x0 }
  - { offsetInCU: 0x5399, offset: 0x30074, size: 0x4, addend: 0x0, symName: __ZL7file452, symObjAddr: 0x861EB, symBinAddr: 0x8CE13, symSize: 0x0 }
  - { offsetInCU: 0x53B0, offset: 0x3008B, size: 0x4, addend: 0x0, symName: __ZL7file453, symObjAddr: 0x8632D, symBinAddr: 0x8CF55, symSize: 0x0 }
  - { offsetInCU: 0x53D4, offset: 0x300AF, size: 0x4, addend: 0x0, symName: __ZL7file454, symObjAddr: 0x86E60, symBinAddr: 0x8DA88, symSize: 0x0 }
  - { offsetInCU: 0x53EB, offset: 0x300C6, size: 0x4, addend: 0x0, symName: __ZL7file455, symObjAddr: 0x86FA1, symBinAddr: 0x8DBC9, symSize: 0x0 }
  - { offsetInCU: 0x540F, offset: 0x300EA, size: 0x4, addend: 0x0, symName: __ZL7file456, symObjAddr: 0x879A8, symBinAddr: 0x8E5D0, symSize: 0x0 }
  - { offsetInCU: 0x5426, offset: 0x30101, size: 0x4, addend: 0x0, symName: __ZL7file457, symObjAddr: 0x87AEB, symBinAddr: 0x8E713, symSize: 0x0 }
  - { offsetInCU: 0x543D, offset: 0x30118, size: 0x4, addend: 0x0, symName: __ZL7file458, symObjAddr: 0x87C2B, symBinAddr: 0x8E853, symSize: 0x0 }
  - { offsetInCU: 0x5454, offset: 0x3012F, size: 0x4, addend: 0x0, symName: __ZL7file459, symObjAddr: 0x87D4B, symBinAddr: 0x8E973, symSize: 0x0 }
  - { offsetInCU: 0x546B, offset: 0x30146, size: 0x4, addend: 0x0, symName: __ZL7file460, symObjAddr: 0x87E8F, symBinAddr: 0x8EAB7, symSize: 0x0 }
  - { offsetInCU: 0x5482, offset: 0x3015D, size: 0x4, addend: 0x0, symName: __ZL7file461, symObjAddr: 0x87FD0, symBinAddr: 0x8EBF8, symSize: 0x0 }
  - { offsetInCU: 0x54A6, offset: 0x30181, size: 0x4, addend: 0x0, symName: __ZL7file462, symObjAddr: 0x88AB0, symBinAddr: 0x8F6D8, symSize: 0x0 }
  - { offsetInCU: 0x54BD, offset: 0x30198, size: 0x4, addend: 0x0, symName: __ZL7file463, symObjAddr: 0x88BF1, symBinAddr: 0x8F819, symSize: 0x0 }
  - { offsetInCU: 0x54D4, offset: 0x301AF, size: 0x4, addend: 0x0, symName: __ZL7file464, symObjAddr: 0x88D32, symBinAddr: 0x8F95A, symSize: 0x0 }
  - { offsetInCU: 0x54EB, offset: 0x301C6, size: 0x4, addend: 0x0, symName: __ZL7file465, symObjAddr: 0x88E4D, symBinAddr: 0x8FA75, symSize: 0x0 }
  - { offsetInCU: 0x550F, offset: 0x301EA, size: 0x4, addend: 0x0, symName: __ZL7file466, symObjAddr: 0x89946, symBinAddr: 0x9056E, symSize: 0x0 }
  - { offsetInCU: 0x5526, offset: 0x30201, size: 0x4, addend: 0x0, symName: __ZL7file467, symObjAddr: 0x89A86, symBinAddr: 0x906AE, symSize: 0x0 }
  - { offsetInCU: 0x553D, offset: 0x30218, size: 0x4, addend: 0x0, symName: __ZL7file468, symObjAddr: 0x89BC4, symBinAddr: 0x907EC, symSize: 0x0 }
  - { offsetInCU: 0x5554, offset: 0x3022F, size: 0x4, addend: 0x0, symName: __ZL9layouts56, symObjAddr: 0x486B4, symBinAddr: 0x4F2DC, symSize: 0x0 }
  - { offsetInCU: 0x556B, offset: 0x30246, size: 0x4, addend: 0x0, symName: __ZL7file469, symObjAddr: 0x89CF5, symBinAddr: 0x9091D, symSize: 0x0 }
  - { offsetInCU: 0x5582, offset: 0x3025D, size: 0x4, addend: 0x0, symName: __ZL7file470, symObjAddr: 0x8AAB5, symBinAddr: 0x916DD, symSize: 0x0 }
  - { offsetInCU: 0x5599, offset: 0x30274, size: 0x4, addend: 0x0, symName: __ZL7file471, symObjAddr: 0x8B595, symBinAddr: 0x921BD, symSize: 0x0 }
  - { offsetInCU: 0x55BD, offset: 0x30298, size: 0x4, addend: 0x0, symName: __ZL7file472, symObjAddr: 0x8BB04, symBinAddr: 0x9272C, symSize: 0x0 }
  - { offsetInCU: 0x55E1, offset: 0x302BC, size: 0x4, addend: 0x0, symName: __ZL7file473, symObjAddr: 0x8D764, symBinAddr: 0x9438C, symSize: 0x0 }
  - { offsetInCU: 0x5605, offset: 0x302E0, size: 0x4, addend: 0x0, symName: __ZL7file474, symObjAddr: 0x8DDAA, symBinAddr: 0x949D2, symSize: 0x0 }
  - { offsetInCU: 0x5629, offset: 0x30304, size: 0x4, addend: 0x0, symName: __ZL7file475, symObjAddr: 0x8EB6D, symBinAddr: 0x95795, symSize: 0x0 }
  - { offsetInCU: 0x5640, offset: 0x3031B, size: 0x4, addend: 0x0, symName: __ZL7file476, symObjAddr: 0x8F1B3, symBinAddr: 0x95DDB, symSize: 0x0 }
  - { offsetInCU: 0x5657, offset: 0x30332, size: 0x4, addend: 0x0, symName: __ZL7file477, symObjAddr: 0x8FF76, symBinAddr: 0x96B9E, symSize: 0x0 }
  - { offsetInCU: 0x566E, offset: 0x30349, size: 0x4, addend: 0x0, symName: __ZL7file478, symObjAddr: 0x905BC, symBinAddr: 0x971E4, symSize: 0x0 }
  - { offsetInCU: 0x5685, offset: 0x30360, size: 0x4, addend: 0x0, symName: __ZL7file479, symObjAddr: 0x90C02, symBinAddr: 0x9782A, symSize: 0x0 }
  - { offsetInCU: 0x569C, offset: 0x30377, size: 0x4, addend: 0x0, symName: __ZL7file480, symObjAddr: 0x91248, symBinAddr: 0x97E70, symSize: 0x0 }
  - { offsetInCU: 0x56B3, offset: 0x3038E, size: 0x4, addend: 0x0, symName: __ZL7file481, symObjAddr: 0x9200B, symBinAddr: 0x98C33, symSize: 0x0 }
  - { offsetInCU: 0x56D7, offset: 0x303B2, size: 0x4, addend: 0x0, symName: __ZL7file482, symObjAddr: 0x923F5, symBinAddr: 0x9901D, symSize: 0x0 }
  - { offsetInCU: 0x56FB, offset: 0x303D6, size: 0x4, addend: 0x0, symName: __ZL7file483, symObjAddr: 0x92A9E, symBinAddr: 0x996C6, symSize: 0x0 }
  - { offsetInCU: 0x571F, offset: 0x303FA, size: 0x4, addend: 0x0, symName: __ZL7file484, symObjAddr: 0x9385D, symBinAddr: 0x9A485, symSize: 0x0 }
  - { offsetInCU: 0x5743, offset: 0x3041E, size: 0x4, addend: 0x0, symName: __ZL7file485, symObjAddr: 0x93F04, symBinAddr: 0x9AB2C, symSize: 0x0 }
  - { offsetInCU: 0x5767, offset: 0x30442, size: 0x4, addend: 0x0, symName: __ZL7file486, symObjAddr: 0x95587, symBinAddr: 0x9C1AF, symSize: 0x0 }
  - { offsetInCU: 0x577E, offset: 0x30459, size: 0x4, addend: 0x0, symName: __ZL7file487, symObjAddr: 0x95C30, symBinAddr: 0x9C858, symSize: 0x0 }
  - { offsetInCU: 0x57A2, offset: 0x3047D, size: 0x4, addend: 0x0, symName: __ZL7file488, symObjAddr: 0x962E6, symBinAddr: 0x9CF0E, symSize: 0x0 }
  - { offsetInCU: 0x57C6, offset: 0x304A1, size: 0x4, addend: 0x0, symName: __ZL7file489, symObjAddr: 0x965C6, symBinAddr: 0x9D1EE, symSize: 0x0 }
  - { offsetInCU: 0x57EA, offset: 0x304C5, size: 0x4, addend: 0x0, symName: __ZL7file490, symObjAddr: 0x96C0B, symBinAddr: 0x9D833, symSize: 0x0 }
  - { offsetInCU: 0x5801, offset: 0x304DC, size: 0x4, addend: 0x0, symName: __ZL7file491, symObjAddr: 0x979CB, symBinAddr: 0x9E5F3, symSize: 0x0 }
  - { offsetInCU: 0x5824, offset: 0x304FF, size: 0x4, addend: 0x0, symName: __ZL7file492, symObjAddr: 0x97A82, symBinAddr: 0x9E6AA, symSize: 0x0 }
  - { offsetInCU: 0x5848, offset: 0x30523, size: 0x4, addend: 0x0, symName: __ZL7file493, symObjAddr: 0x980D5, symBinAddr: 0x9ECFD, symSize: 0x0 }
  - { offsetInCU: 0x585F, offset: 0x3053A, size: 0x4, addend: 0x0, symName: __ZL7file494, symObjAddr: 0x9871A, symBinAddr: 0x9F342, symSize: 0x0 }
  - { offsetInCU: 0x5876, offset: 0x30551, size: 0x4, addend: 0x0, symName: __ZL7file495, symObjAddr: 0x994D9, symBinAddr: 0xA0101, symSize: 0x0 }
  - { offsetInCU: 0x589A, offset: 0x30575, size: 0x4, addend: 0x0, symName: __ZL7file496, symObjAddr: 0x99AA2, symBinAddr: 0xA06CA, symSize: 0x0 }
  - { offsetInCU: 0x58BE, offset: 0x30599, size: 0x4, addend: 0x0, symName: __ZL7file497, symObjAddr: 0x9A010, symBinAddr: 0xA0C38, symSize: 0x0 }
  - { offsetInCU: 0x58E2, offset: 0x305BD, size: 0x4, addend: 0x0, symName: __ZL7file498, symObjAddr: 0x9AAEE, symBinAddr: 0xA1716, symSize: 0x0 }
  - { offsetInCU: 0x5906, offset: 0x305E1, size: 0x4, addend: 0x0, symName: __ZL7file499, symObjAddr: 0x9B143, symBinAddr: 0xA1D6B, symSize: 0x0 }
  - { offsetInCU: 0x591D, offset: 0x305F8, size: 0x4, addend: 0x0, symName: __ZL7file500, symObjAddr: 0x9BC21, symBinAddr: 0xA2849, symSize: 0x0 }
  - { offsetInCU: 0x5941, offset: 0x3061C, size: 0x4, addend: 0x0, symName: __ZL7file501, symObjAddr: 0x9C191, symBinAddr: 0xA2DB9, symSize: 0x0 }
  - { offsetInCU: 0x5965, offset: 0x30640, size: 0x4, addend: 0x0, symName: __ZL7file502, symObjAddr: 0x9CC70, symBinAddr: 0xA3898, symSize: 0x0 }
  - { offsetInCU: 0x5989, offset: 0x30664, size: 0x4, addend: 0x0, symName: __ZL7file503, symObjAddr: 0x9D292, symBinAddr: 0xA3EBA, symSize: 0x0 }
  - { offsetInCU: 0x59A0, offset: 0x3067B, size: 0x4, addend: 0x0, symName: __ZL7file504, symObjAddr: 0x9D8B4, symBinAddr: 0xA44DC, symSize: 0x0 }
  - { offsetInCU: 0x59B7, offset: 0x30692, size: 0x4, addend: 0x0, symName: __ZL7file505, symObjAddr: 0x9E674, symBinAddr: 0xA529C, symSize: 0x0 }
  - { offsetInCU: 0x59DB, offset: 0x306B6, size: 0x4, addend: 0x0, symName: __ZL7file506, symObjAddr: 0x9F436, symBinAddr: 0xA605E, symSize: 0x0 }
  - { offsetInCU: 0x59FF, offset: 0x306DA, size: 0x4, addend: 0x0, symName: __ZL7file507, symObjAddr: 0xA01FB, symBinAddr: 0xA6E23, symSize: 0x0 }
  - { offsetInCU: 0x5A16, offset: 0x306F1, size: 0x4, addend: 0x0, symName: __ZL7file508, symObjAddr: 0xA0840, symBinAddr: 0xA7468, symSize: 0x0 }
  - { offsetInCU: 0x5A2D, offset: 0x30708, size: 0x4, addend: 0x0, symName: __ZL7file509, symObjAddr: 0xA0E93, symBinAddr: 0xA7ABB, symSize: 0x0 }
  - { offsetInCU: 0x5A44, offset: 0x3071F, size: 0x4, addend: 0x0, symName: __ZL7file510, symObjAddr: 0xA14D7, symBinAddr: 0xA80FF, symSize: 0x0 }
  - { offsetInCU: 0x5A5B, offset: 0x30736, size: 0x4, addend: 0x0, symName: __ZL7file511, symObjAddr: 0xA229A, symBinAddr: 0xA8EC2, symSize: 0x0 }
  - { offsetInCU: 0x5A72, offset: 0x3074D, size: 0x4, addend: 0x0, symName: __ZL7file512, symObjAddr: 0xA28DF, symBinAddr: 0xA9507, symSize: 0x0 }
  - { offsetInCU: 0x5A96, offset: 0x30771, size: 0x4, addend: 0x0, symName: __ZL7file513, symObjAddr: 0xA4195, symBinAddr: 0xAADBD, symSize: 0x0 }
  - { offsetInCU: 0x5ABA, offset: 0x30795, size: 0x4, addend: 0x0, symName: __ZL7file514, symObjAddr: 0xA4F59, symBinAddr: 0xABB81, symSize: 0x0 }
  - { offsetInCU: 0x5AD1, offset: 0x307AC, size: 0x4, addend: 0x0, symName: __ZL7file515, symObjAddr: 0xA5D1D, symBinAddr: 0xAC945, symSize: 0x0 }
  - { offsetInCU: 0x5AE8, offset: 0x307C3, size: 0x4, addend: 0x0, symName: __ZL7file516, symObjAddr: 0xA6AE0, symBinAddr: 0xAD708, symSize: 0x0 }
  - { offsetInCU: 0x5AFF, offset: 0x307DA, size: 0x4, addend: 0x0, symName: __ZL7file517, symObjAddr: 0xA78A4, symBinAddr: 0xAE4CC, symSize: 0x0 }
  - { offsetInCU: 0x5B23, offset: 0x307FE, size: 0x4, addend: 0x0, symName: __ZL7file518, symObjAddr: 0xA7E62, symBinAddr: 0xAEA8A, symSize: 0x0 }
  - { offsetInCU: 0x5B47, offset: 0x30822, size: 0x4, addend: 0x0, symName: __ZL7file519, symObjAddr: 0xA842D, symBinAddr: 0xAF055, symSize: 0x0 }
  - { offsetInCU: 0x5B5E, offset: 0x30839, size: 0x4, addend: 0x0, symName: __ZL7file520, symObjAddr: 0xA91F2, symBinAddr: 0xAFE1A, symSize: 0x0 }
  - { offsetInCU: 0x5B82, offset: 0x3085D, size: 0x4, addend: 0x0, symName: __ZL7file521, symObjAddr: 0xAA1BF, symBinAddr: 0xB0DE7, symSize: 0x0 }
  - { offsetInCU: 0x5BA6, offset: 0x30881, size: 0x4, addend: 0x0, symName: __ZL7file522, symObjAddr: 0xAAF85, symBinAddr: 0xB1BAD, symSize: 0x0 }
  - { offsetInCU: 0x5BCA, offset: 0x308A5, size: 0x4, addend: 0x0, symName: __ZL7file523, symObjAddr: 0xABD46, symBinAddr: 0xB296E, symSize: 0x0 }
  - { offsetInCU: 0x5BEE, offset: 0x308C9, size: 0x4, addend: 0x0, symName: __ZL7file524, symObjAddr: 0xAC259, symBinAddr: 0xB2E81, symSize: 0x0 }
  - { offsetInCU: 0x5C12, offset: 0x308ED, size: 0x4, addend: 0x0, symName: __ZL7file525, symObjAddr: 0xAD22F, symBinAddr: 0xB3E57, symSize: 0x0 }
  - { offsetInCU: 0x5C36, offset: 0x30911, size: 0x4, addend: 0x0, symName: __ZL7file526, symObjAddr: 0xAD87E, symBinAddr: 0xB44A6, symSize: 0x0 }
  - { offsetInCU: 0x5C4D, offset: 0x30928, size: 0x4, addend: 0x0, symName: __ZL7file527, symObjAddr: 0xAE641, symBinAddr: 0xB5269, symSize: 0x0 }
  - { offsetInCU: 0x5C64, offset: 0x3093F, size: 0x4, addend: 0x0, symName: __ZL9patches56, symObjAddr: 0x1A064C, symBinAddr: 0x1A71EC, symSize: 0x0 }
  - { offsetInCU: 0x5C7B, offset: 0x30956, size: 0x4, addend: 0x0, symName: __ZL10patchBuf81, symObjAddr: 0xAF400, symBinAddr: 0xB6028, symSize: 0x0 }
  - { offsetInCU: 0x5C92, offset: 0x3096D, size: 0x4, addend: 0x0, symName: __ZL11platforms57, symObjAddr: 0x48B64, symBinAddr: 0x4F78C, symSize: 0x0 }
  - { offsetInCU: 0x5CB5, offset: 0x30990, size: 0x4, addend: 0x0, symName: __ZL7file528, symObjAddr: 0xAF404, symBinAddr: 0xB602C, symSize: 0x0 }
  - { offsetInCU: 0x5CCC, offset: 0x309A7, size: 0x4, addend: 0x0, symName: __ZL7file529, symObjAddr: 0xAF550, symBinAddr: 0xB6178, symSize: 0x0 }
  - { offsetInCU: 0x5CE3, offset: 0x309BE, size: 0x4, addend: 0x0, symName: __ZL7file530, symObjAddr: 0xAF6B1, symBinAddr: 0xB62D9, symSize: 0x0 }
  - { offsetInCU: 0x5CFA, offset: 0x309D5, size: 0x4, addend: 0x0, symName: __ZL7file531, symObjAddr: 0xAF7FB, symBinAddr: 0xB6423, symSize: 0x0 }
  - { offsetInCU: 0x5D11, offset: 0x309EC, size: 0x4, addend: 0x0, symName: __ZL7file532, symObjAddr: 0xAF945, symBinAddr: 0xB656D, symSize: 0x0 }
  - { offsetInCU: 0x5D28, offset: 0x30A03, size: 0x4, addend: 0x0, symName: __ZL7file533, symObjAddr: 0xAFA88, symBinAddr: 0xB66B0, symSize: 0x0 }
  - { offsetInCU: 0x5D4C, offset: 0x30A27, size: 0x4, addend: 0x0, symName: __ZL7file534, symObjAddr: 0xB05BD, symBinAddr: 0xB71E5, symSize: 0x0 }
  - { offsetInCU: 0x5D70, offset: 0x30A4B, size: 0x4, addend: 0x0, symName: __ZL7file535, symObjAddr: 0xB0702, symBinAddr: 0xB732A, symSize: 0x0 }
  - { offsetInCU: 0x5D94, offset: 0x30A6F, size: 0x4, addend: 0x0, symName: __ZL7file536, symObjAddr: 0xB1236, symBinAddr: 0xB7E5E, symSize: 0x0 }
  - { offsetInCU: 0x5DAB, offset: 0x30A86, size: 0x4, addend: 0x0, symName: __ZL7file537, symObjAddr: 0xB1395, symBinAddr: 0xB7FBD, symSize: 0x0 }
  - { offsetInCU: 0x5DCF, offset: 0x30AAA, size: 0x4, addend: 0x0, symName: __ZL7file538, symObjAddr: 0xB153D, symBinAddr: 0xB8165, symSize: 0x0 }
  - { offsetInCU: 0x5DE6, offset: 0x30AC1, size: 0x4, addend: 0x0, symName: __ZL7file539, symObjAddr: 0xB169C, symBinAddr: 0xB82C4, symSize: 0x0 }
  - { offsetInCU: 0x5DFD, offset: 0x30AD8, size: 0x4, addend: 0x0, symName: __ZL7file540, symObjAddr: 0xB17E5, symBinAddr: 0xB840D, symSize: 0x0 }
  - { offsetInCU: 0x5E14, offset: 0x30AEF, size: 0x4, addend: 0x0, symName: __ZL7file541, symObjAddr: 0xB2319, symBinAddr: 0xB8F41, symSize: 0x0 }
  - { offsetInCU: 0x5E2B, offset: 0x30B06, size: 0x4, addend: 0x0, symName: __ZL7file542, symObjAddr: 0xB2DF9, symBinAddr: 0xB9A21, symSize: 0x0 }
  - { offsetInCU: 0x5E42, offset: 0x30B1D, size: 0x4, addend: 0x0, symName: __ZL7file543, symObjAddr: 0xB2F40, symBinAddr: 0xB9B68, symSize: 0x0 }
  - { offsetInCU: 0x5E59, offset: 0x30B34, size: 0x4, addend: 0x0, symName: __ZL7file544, symObjAddr: 0xB308C, symBinAddr: 0xB9CB4, symSize: 0x0 }
  - { offsetInCU: 0x5E70, offset: 0x30B4B, size: 0x4, addend: 0x0, symName: __ZL7file545, symObjAddr: 0xB31DD, symBinAddr: 0xB9E05, symSize: 0x0 }
  - { offsetInCU: 0x5E87, offset: 0x30B62, size: 0x4, addend: 0x0, symName: __ZL7file546, symObjAddr: 0xB330E, symBinAddr: 0xB9F36, symSize: 0x0 }
  - { offsetInCU: 0x5E9E, offset: 0x30B79, size: 0x4, addend: 0x0, symName: __ZL7file547, symObjAddr: 0xB3463, symBinAddr: 0xBA08B, symSize: 0x0 }
  - { offsetInCU: 0x5EB5, offset: 0x30B90, size: 0x4, addend: 0x0, symName: __ZL7file548, symObjAddr: 0xB3558, symBinAddr: 0xBA180, symSize: 0x0 }
  - { offsetInCU: 0x5ECC, offset: 0x30BA7, size: 0x4, addend: 0x0, symName: __ZL7file549, symObjAddr: 0xB36B8, symBinAddr: 0xBA2E0, symSize: 0x0 }
  - { offsetInCU: 0x5EE3, offset: 0x30BBE, size: 0x4, addend: 0x0, symName: __ZL9layouts57, symObjAddr: 0x48D1C, symBinAddr: 0x4F944, symSize: 0x0 }
  - { offsetInCU: 0x5EFA, offset: 0x30BD5, size: 0x4, addend: 0x0, symName: __ZL7file550, symObjAddr: 0xB3820, symBinAddr: 0xBA448, symSize: 0x0 }
  - { offsetInCU: 0x5F11, offset: 0x30BEC, size: 0x4, addend: 0x0, symName: __ZL7file551, symObjAddr: 0xB47E9, symBinAddr: 0xBB411, symSize: 0x0 }
  - { offsetInCU: 0x5F35, offset: 0x30C10, size: 0x4, addend: 0x0, symName: __ZL7file552, symObjAddr: 0xB4E1A, symBinAddr: 0xBBA42, symSize: 0x0 }
  - { offsetInCU: 0x5F59, offset: 0x30C34, size: 0x4, addend: 0x0, symName: __ZL7file553, symObjAddr: 0xB5DF5, symBinAddr: 0xBCA1D, symSize: 0x0 }
  - { offsetInCU: 0x5F7D, offset: 0x30C58, size: 0x4, addend: 0x0, symName: __ZL7file554, symObjAddr: 0xB6DCE, symBinAddr: 0xBD9F6, symSize: 0x0 }
  - { offsetInCU: 0x5FA1, offset: 0x30C7C, size: 0x4, addend: 0x0, symName: __ZL7file555, symObjAddr: 0xB7D98, symBinAddr: 0xBE9C0, symSize: 0x0 }
  - { offsetInCU: 0x5FC5, offset: 0x30CA0, size: 0x4, addend: 0x0, symName: __ZL7file556, symObjAddr: 0xB83CA, symBinAddr: 0xBEFF2, symSize: 0x0 }
  - { offsetInCU: 0x5FE9, offset: 0x30CC4, size: 0x4, addend: 0x0, symName: __ZL7file557, symObjAddr: 0xB9530, symBinAddr: 0xC0158, symSize: 0x0 }
  - { offsetInCU: 0x6000, offset: 0x30CDB, size: 0x4, addend: 0x0, symName: __ZL7file558, symObjAddr: 0xB9B66, symBinAddr: 0xC078E, symSize: 0x0 }
  - { offsetInCU: 0x6024, offset: 0x30CFF, size: 0x4, addend: 0x0, symName: __ZL7file559, symObjAddr: 0xBA199, symBinAddr: 0xC0DC1, symSize: 0x0 }
  - { offsetInCU: 0x6048, offset: 0x30D23, size: 0x4, addend: 0x0, symName: __ZL7file560, symObjAddr: 0xBA703, symBinAddr: 0xC132B, symSize: 0x0 }
  - { offsetInCU: 0x606C, offset: 0x30D47, size: 0x4, addend: 0x0, symName: __ZL7file561, symObjAddr: 0xBAD37, symBinAddr: 0xC195F, symSize: 0x0 }
  - { offsetInCU: 0x6090, offset: 0x30D6B, size: 0x4, addend: 0x0, symName: __ZL7file562, symObjAddr: 0xBBF9A, symBinAddr: 0xC2BC2, symSize: 0x0 }
  - { offsetInCU: 0x60A7, offset: 0x30D82, size: 0x4, addend: 0x0, symName: __ZL7file563, symObjAddr: 0xBC5D0, symBinAddr: 0xC31F8, symSize: 0x0 }
  - { offsetInCU: 0x60BE, offset: 0x30D99, size: 0x4, addend: 0x0, symName: __ZL7file564, symObjAddr: 0xBD38F, symBinAddr: 0xC3FB7, symSize: 0x0 }
  - { offsetInCU: 0x60D5, offset: 0x30DB0, size: 0x4, addend: 0x0, symName: __ZL7file565, symObjAddr: 0xBE14F, symBinAddr: 0xC4D77, symSize: 0x0 }
  - { offsetInCU: 0x60EC, offset: 0x30DC7, size: 0x4, addend: 0x0, symName: __ZL7file566, symObjAddr: 0xBEF11, symBinAddr: 0xC5B39, symSize: 0x0 }
  - { offsetInCU: 0x6110, offset: 0x30DEB, size: 0x4, addend: 0x0, symName: __ZL7file567, symObjAddr: 0xBF60D, symBinAddr: 0xC6235, symSize: 0x0 }
  - { offsetInCU: 0x6133, offset: 0x30E0E, size: 0x4, addend: 0x0, symName: __ZL7file568, symObjAddr: 0xBF6C5, symBinAddr: 0xC62ED, symSize: 0x0 }
  - { offsetInCU: 0x614A, offset: 0x30E25, size: 0x4, addend: 0x0, symName: __ZL7file569, symObjAddr: 0xBFDC1, symBinAddr: 0xC69E9, symSize: 0x0 }
  - { offsetInCU: 0x6161, offset: 0x30E3C, size: 0x4, addend: 0x0, symName: __ZL7file570, symObjAddr: 0xC03F2, symBinAddr: 0xC701A, symSize: 0x0 }
  - { offsetInCU: 0x6185, offset: 0x30E60, size: 0x4, addend: 0x0, symName: __ZL7file571, symObjAddr: 0xC0A27, symBinAddr: 0xC764F, symSize: 0x0 }
  - { offsetInCU: 0x61A9, offset: 0x30E84, size: 0x4, addend: 0x0, symName: __ZL9patches57, symObjAddr: 0x1A072C, symBinAddr: 0x1A72CC, symSize: 0x0 }
  - { offsetInCU: 0x61C0, offset: 0x30E9B, size: 0x4, addend: 0x0, symName: __ZL10patchBuf82, symObjAddr: 0xC10C4, symBinAddr: 0xC7CEC, symSize: 0x0 }
  - { offsetInCU: 0x61D7, offset: 0x30EB2, size: 0x4, addend: 0x0, symName: __ZL11revisions21, symObjAddr: 0x48ED4, symBinAddr: 0x4FAFC, symSize: 0x0 }
  - { offsetInCU: 0x61EE, offset: 0x30EC9, size: 0x4, addend: 0x0, symName: __ZL11platforms58, symObjAddr: 0x48ED8, symBinAddr: 0x4FB00, symSize: 0x0 }
  - { offsetInCU: 0x6211, offset: 0x30EEC, size: 0x4, addend: 0x0, symName: __ZL7file572, symObjAddr: 0xC10C8, symBinAddr: 0xC7CF0, symSize: 0x0 }
  - { offsetInCU: 0x6228, offset: 0x30F03, size: 0x4, addend: 0x0, symName: __ZL7file573, symObjAddr: 0xC121E, symBinAddr: 0xC7E46, symSize: 0x0 }
  - { offsetInCU: 0x623F, offset: 0x30F1A, size: 0x4, addend: 0x0, symName: __ZL7file574, symObjAddr: 0xC1360, symBinAddr: 0xC7F88, symSize: 0x0 }
  - { offsetInCU: 0x6256, offset: 0x30F31, size: 0x4, addend: 0x0, symName: __ZL7file575, symObjAddr: 0xC14A6, symBinAddr: 0xC80CE, symSize: 0x0 }
  - { offsetInCU: 0x626D, offset: 0x30F48, size: 0x4, addend: 0x0, symName: __ZL7file576, symObjAddr: 0xC1604, symBinAddr: 0xC822C, symSize: 0x0 }
  - { offsetInCU: 0x6284, offset: 0x30F5F, size: 0x4, addend: 0x0, symName: __ZL7file577, symObjAddr: 0xC1762, symBinAddr: 0xC838A, symSize: 0x0 }
  - { offsetInCU: 0x629B, offset: 0x30F76, size: 0x4, addend: 0x0, symName: __ZL7file578, symObjAddr: 0xC18A5, symBinAddr: 0xC84CD, symSize: 0x0 }
  - { offsetInCU: 0x62B2, offset: 0x30F8D, size: 0x4, addend: 0x0, symName: __ZL7file579, symObjAddr: 0xC19E1, symBinAddr: 0xC8609, symSize: 0x0 }
  - { offsetInCU: 0x62C9, offset: 0x30FA4, size: 0x4, addend: 0x0, symName: __ZL9layouts58, symObjAddr: 0x48F78, symBinAddr: 0x4FBA0, symSize: 0x0 }
  - { offsetInCU: 0x62E0, offset: 0x30FBB, size: 0x4, addend: 0x0, symName: __ZL7file580, symObjAddr: 0xC1B3D, symBinAddr: 0xC8765, symSize: 0x0 }
  - { offsetInCU: 0x6304, offset: 0x30FDF, size: 0x4, addend: 0x0, symName: __ZL7file581, symObjAddr: 0xC2184, symBinAddr: 0xC8DAC, symSize: 0x0 }
  - { offsetInCU: 0x6328, offset: 0x31003, size: 0x4, addend: 0x0, symName: __ZL7file582, symObjAddr: 0xC27CC, symBinAddr: 0xC93F4, symSize: 0x0 }
  - { offsetInCU: 0x634B, offset: 0x31026, size: 0x4, addend: 0x0, symName: __ZL7file583, symObjAddr: 0xC2885, symBinAddr: 0xC94AD, symSize: 0x0 }
  - { offsetInCU: 0x636F, offset: 0x3104A, size: 0x4, addend: 0x0, symName: __ZL7file584, symObjAddr: 0xC2F04, symBinAddr: 0xC9B2C, symSize: 0x0 }
  - { offsetInCU: 0x6393, offset: 0x3106E, size: 0x4, addend: 0x0, symName: __ZL7file585, symObjAddr: 0xC3584, symBinAddr: 0xCA1AC, symSize: 0x0 }
  - { offsetInCU: 0x63AA, offset: 0x31085, size: 0x4, addend: 0x0, symName: __ZL7file586, symObjAddr: 0xC3BCC, symBinAddr: 0xCA7F4, symSize: 0x0 }
  - { offsetInCU: 0x63C1, offset: 0x3109C, size: 0x4, addend: 0x0, symName: __ZL7file587, symObjAddr: 0xC4213, symBinAddr: 0xCAE3B, symSize: 0x0 }
  - { offsetInCU: 0x63D8, offset: 0x310B3, size: 0x4, addend: 0x0, symName: __ZL9patches58, symObjAddr: 0x1A0844, symBinAddr: 0x1A73E4, symSize: 0x0 }
  - { offsetInCU: 0x63EF, offset: 0x310CA, size: 0x4, addend: 0x0, symName: __ZL10patchBuf83, symObjAddr: 0xC485B, symBinAddr: 0xCB483, symSize: 0x0 }
  - { offsetInCU: 0x6406, offset: 0x310E1, size: 0x4, addend: 0x0, symName: __ZL11revisions22, symObjAddr: 0x49018, symBinAddr: 0x4FC40, symSize: 0x0 }
  - { offsetInCU: 0x641D, offset: 0x310F8, size: 0x4, addend: 0x0, symName: __ZL11platforms59, symObjAddr: 0x4901C, symBinAddr: 0x4FC44, symSize: 0x0 }
  - { offsetInCU: 0x6434, offset: 0x3110F, size: 0x4, addend: 0x0, symName: __ZL7file588, symObjAddr: 0xC485F, symBinAddr: 0xCB487, symSize: 0x0 }
  - { offsetInCU: 0x644B, offset: 0x31126, size: 0x4, addend: 0x0, symName: __ZL7file589, symObjAddr: 0xC4A9D, symBinAddr: 0xCB6C5, symSize: 0x0 }
  - { offsetInCU: 0x6462, offset: 0x3113D, size: 0x4, addend: 0x0, symName: __ZL7file590, symObjAddr: 0xC4C05, symBinAddr: 0xCB82D, symSize: 0x0 }
  - { offsetInCU: 0x6486, offset: 0x31161, size: 0x4, addend: 0x0, symName: __ZL7file591, symObjAddr: 0xC4DC5, symBinAddr: 0xCB9ED, symSize: 0x0 }
  - { offsetInCU: 0x649D, offset: 0x31178, size: 0x4, addend: 0x0, symName: __ZL7file592, symObjAddr: 0xC4F2D, symBinAddr: 0xCBB55, symSize: 0x0 }
  - { offsetInCU: 0x64B4, offset: 0x3118F, size: 0x4, addend: 0x0, symName: __ZL7file593, symObjAddr: 0xC5082, symBinAddr: 0xCBCAA, symSize: 0x0 }
  - { offsetInCU: 0x64CB, offset: 0x311A6, size: 0x4, addend: 0x0, symName: __ZL7file594, symObjAddr: 0xC51ED, symBinAddr: 0xCBE15, symSize: 0x0 }
  - { offsetInCU: 0x64E2, offset: 0x311BD, size: 0x4, addend: 0x0, symName: __ZL7file595, symObjAddr: 0xC5339, symBinAddr: 0xCBF61, symSize: 0x0 }
  - { offsetInCU: 0x64F9, offset: 0x311D4, size: 0x4, addend: 0x0, symName: __ZL7file596, symObjAddr: 0xC54A3, symBinAddr: 0xCC0CB, symSize: 0x0 }
  - { offsetInCU: 0x6510, offset: 0x311EB, size: 0x4, addend: 0x0, symName: __ZL7file597, symObjAddr: 0xC560D, symBinAddr: 0xCC235, symSize: 0x0 }
  - { offsetInCU: 0x6527, offset: 0x31202, size: 0x4, addend: 0x0, symName: __ZL7file598, symObjAddr: 0xC574F, symBinAddr: 0xCC377, symSize: 0x0 }
  - { offsetInCU: 0x653E, offset: 0x31219, size: 0x4, addend: 0x0, symName: __ZL7file599, symObjAddr: 0xC58BA, symBinAddr: 0xCC4E2, symSize: 0x0 }
  - { offsetInCU: 0x6555, offset: 0x31230, size: 0x4, addend: 0x0, symName: __ZL7file600, symObjAddr: 0xC5A1A, symBinAddr: 0xCC642, symSize: 0x0 }
  - { offsetInCU: 0x656C, offset: 0x31247, size: 0x4, addend: 0x0, symName: __ZL7file601, symObjAddr: 0xC5B6D, symBinAddr: 0xCC795, symSize: 0x0 }
  - { offsetInCU: 0x6583, offset: 0x3125E, size: 0x4, addend: 0x0, symName: __ZL7file602, symObjAddr: 0xC5CB8, symBinAddr: 0xCC8E0, symSize: 0x0 }
  - { offsetInCU: 0x659A, offset: 0x31275, size: 0x4, addend: 0x0, symName: __ZL7file603, symObjAddr: 0xC5E0B, symBinAddr: 0xCCA33, symSize: 0x0 }
  - { offsetInCU: 0x65BE, offset: 0x31299, size: 0x4, addend: 0x0, symName: __ZL7file604, symObjAddr: 0xC5F92, symBinAddr: 0xCCBBA, symSize: 0x0 }
  - { offsetInCU: 0x65D5, offset: 0x312B0, size: 0x4, addend: 0x0, symName: __ZL7file605, symObjAddr: 0xC60F1, symBinAddr: 0xCCD19, symSize: 0x0 }
  - { offsetInCU: 0x65EC, offset: 0x312C7, size: 0x4, addend: 0x0, symName: __ZL7file606, symObjAddr: 0xC6241, symBinAddr: 0xCCE69, symSize: 0x0 }
  - { offsetInCU: 0x6603, offset: 0x312DE, size: 0x4, addend: 0x0, symName: __ZL7file607, symObjAddr: 0xC6391, symBinAddr: 0xCCFB9, symSize: 0x0 }
  - { offsetInCU: 0x661A, offset: 0x312F5, size: 0x4, addend: 0x0, symName: __ZL7file608, symObjAddr: 0xC650A, symBinAddr: 0xCD132, symSize: 0x0 }
  - { offsetInCU: 0x663E, offset: 0x31319, size: 0x4, addend: 0x0, symName: __ZL7file609, symObjAddr: 0xC6A70, symBinAddr: 0xCD698, symSize: 0x0 }
  - { offsetInCU: 0x6655, offset: 0x31330, size: 0x4, addend: 0x0, symName: __ZL7file610, symObjAddr: 0xC6BD9, symBinAddr: 0xCD801, symSize: 0x0 }
  - { offsetInCU: 0x666C, offset: 0x31347, size: 0x4, addend: 0x0, symName: __ZL9layouts59, symObjAddr: 0x49224, symBinAddr: 0x4FE4C, symSize: 0x0 }
  - { offsetInCU: 0x6683, offset: 0x3135E, size: 0x4, addend: 0x0, symName: __ZL7file611, symObjAddr: 0xC6D4E, symBinAddr: 0xCD976, symSize: 0x0 }
  - { offsetInCU: 0x66A7, offset: 0x31382, size: 0x4, addend: 0x0, symName: __ZL7file612, symObjAddr: 0xC7148, symBinAddr: 0xCDD70, symSize: 0x0 }
  - { offsetInCU: 0x66BE, offset: 0x31399, size: 0x4, addend: 0x0, symName: __ZL7file613, symObjAddr: 0xC7542, symBinAddr: 0xCE16A, symSize: 0x0 }
  - { offsetInCU: 0x66E2, offset: 0x313BD, size: 0x4, addend: 0x0, symName: __ZL7file614, symObjAddr: 0xC793D, symBinAddr: 0xCE565, symSize: 0x0 }
  - { offsetInCU: 0x6706, offset: 0x313E1, size: 0x4, addend: 0x0, symName: __ZL7file615, symObjAddr: 0xC7D39, symBinAddr: 0xCE961, symSize: 0x0 }
  - { offsetInCU: 0x671D, offset: 0x313F8, size: 0x4, addend: 0x0, symName: __ZL7file616, symObjAddr: 0xC8055, symBinAddr: 0xCEC7D, symSize: 0x0 }
  - { offsetInCU: 0x6734, offset: 0x3140F, size: 0x4, addend: 0x0, symName: __ZL7file617, symObjAddr: 0xC8371, symBinAddr: 0xCEF99, symSize: 0x0 }
  - { offsetInCU: 0x6758, offset: 0x31433, size: 0x4, addend: 0x0, symName: __ZL7file618, symObjAddr: 0xC87F6, symBinAddr: 0xCF41E, symSize: 0x0 }
  - { offsetInCU: 0x677C, offset: 0x31457, size: 0x4, addend: 0x0, symName: __ZL7file619, symObjAddr: 0xC8BB2, symBinAddr: 0xCF7DA, symSize: 0x0 }
  - { offsetInCU: 0x67A0, offset: 0x3147B, size: 0x4, addend: 0x0, symName: __ZL7file620, symObjAddr: 0xC9006, symBinAddr: 0xCFC2E, symSize: 0x0 }
  - { offsetInCU: 0x67C4, offset: 0x3149F, size: 0x4, addend: 0x0, symName: __ZL7file621, symObjAddr: 0xC9457, symBinAddr: 0xD007F, symSize: 0x0 }
  - { offsetInCU: 0x67E8, offset: 0x314C3, size: 0x4, addend: 0x0, symName: __ZL7file622, symObjAddr: 0xC98B8, symBinAddr: 0xD04E0, symSize: 0x0 }
  - { offsetInCU: 0x680C, offset: 0x314E7, size: 0x4, addend: 0x0, symName: __ZL7file623, symObjAddr: 0xC9D1A, symBinAddr: 0xD0942, symSize: 0x0 }
  - { offsetInCU: 0x6830, offset: 0x3150B, size: 0x4, addend: 0x0, symName: __ZL7file624, symObjAddr: 0xCA160, symBinAddr: 0xD0D88, symSize: 0x0 }
  - { offsetInCU: 0x6854, offset: 0x3152F, size: 0x4, addend: 0x0, symName: __ZL7file625, symObjAddr: 0xCA56E, symBinAddr: 0xD1196, symSize: 0x0 }
  - { offsetInCU: 0x6878, offset: 0x31553, size: 0x4, addend: 0x0, symName: __ZL7file626, symObjAddr: 0xCA95F, symBinAddr: 0xD1587, symSize: 0x0 }
  - { offsetInCU: 0x689C, offset: 0x31577, size: 0x4, addend: 0x0, symName: __ZL7file627, symObjAddr: 0xCAD51, symBinAddr: 0xD1979, symSize: 0x0 }
  - { offsetInCU: 0x68B3, offset: 0x3158E, size: 0x4, addend: 0x0, symName: __ZL7file628, symObjAddr: 0xCB1D6, symBinAddr: 0xD1DFE, symSize: 0x0 }
  - { offsetInCU: 0x68CA, offset: 0x315A5, size: 0x4, addend: 0x0, symName: __ZL7file629, symObjAddr: 0xCB5C8, symBinAddr: 0xD21F0, symSize: 0x0 }
  - { offsetInCU: 0x68EE, offset: 0x315C9, size: 0x4, addend: 0x0, symName: __ZL7file630, symObjAddr: 0xCBA38, symBinAddr: 0xD2660, symSize: 0x0 }
  - { offsetInCU: 0x6912, offset: 0x315ED, size: 0x4, addend: 0x0, symName: __ZL7file631, symObjAddr: 0xCBE2C, symBinAddr: 0xD2A54, symSize: 0x0 }
  - { offsetInCU: 0x6929, offset: 0x31604, size: 0x4, addend: 0x0, symName: __ZL7file632, symObjAddr: 0xCC149, symBinAddr: 0xD2D71, symSize: 0x0 }
  - { offsetInCU: 0x694D, offset: 0x31628, size: 0x4, addend: 0x0, symName: __ZL7file633, symObjAddr: 0xCC5A9, symBinAddr: 0xD31D1, symSize: 0x0 }
  - { offsetInCU: 0x6964, offset: 0x3163F, size: 0x4, addend: 0x0, symName: __ZL7file634, symObjAddr: 0xCCBE1, symBinAddr: 0xD3809, symSize: 0x0 }
  - { offsetInCU: 0x697B, offset: 0x31656, size: 0x4, addend: 0x0, symName: __ZL7file635, symObjAddr: 0xCD043, symBinAddr: 0xD3C6B, symSize: 0x0 }
  - { offsetInCU: 0x699F, offset: 0x3167A, size: 0x4, addend: 0x0, symName: __ZL9patches59, symObjAddr: 0x1A0978, symBinAddr: 0x1A7518, symSize: 0x0 }
  - { offsetInCU: 0x69B6, offset: 0x31691, size: 0x4, addend: 0x0, symName: __ZL10patchBuf84, symObjAddr: 0xCD4A7, symBinAddr: 0xD40CF, symSize: 0x0 }
  - { offsetInCU: 0x69CD, offset: 0x316A8, size: 0x4, addend: 0x0, symName: __ZL11platforms60, symObjAddr: 0x4942C, symBinAddr: 0x50054, symSize: 0x0 }
  - { offsetInCU: 0x69E4, offset: 0x316BF, size: 0x4, addend: 0x0, symName: __ZL7file636, symObjAddr: 0xCD4AB, symBinAddr: 0xD40D3, symSize: 0x0 }
  - { offsetInCU: 0x69FB, offset: 0x316D6, size: 0x4, addend: 0x0, symName: __ZL7file637, symObjAddr: 0xCD5F1, symBinAddr: 0xD4219, symSize: 0x0 }
  - { offsetInCU: 0x6A12, offset: 0x316ED, size: 0x4, addend: 0x0, symName: __ZL9layouts60, symObjAddr: 0x49454, symBinAddr: 0x5007C, symSize: 0x0 }
  - { offsetInCU: 0x6A29, offset: 0x31704, size: 0x4, addend: 0x0, symName: __ZL7file638, symObjAddr: 0xCD735, symBinAddr: 0xD435D, symSize: 0x0 }
  - { offsetInCU: 0x6A4D, offset: 0x31728, size: 0x4, addend: 0x0, symName: __ZL7file639, symObjAddr: 0xCDA88, symBinAddr: 0xD46B0, symSize: 0x0 }
  - { offsetInCU: 0x6A71, offset: 0x3174C, size: 0x4, addend: 0x0, symName: __ZL9patches60, symObjAddr: 0x1A0A58, symBinAddr: 0x1A75F8, symSize: 0x0 }
  - { offsetInCU: 0x6A88, offset: 0x31763, size: 0x4, addend: 0x0, symName: __ZL10patchBuf85, symObjAddr: 0xCDDD8, symBinAddr: 0xD4A00, symSize: 0x0 }
  - { offsetInCU: 0x6A9F, offset: 0x3177A, size: 0x4, addend: 0x0, symName: __ZL11platforms61, symObjAddr: 0x4947C, symBinAddr: 0x500A4, symSize: 0x0 }
  - { offsetInCU: 0x6AC2, offset: 0x3179D, size: 0x4, addend: 0x0, symName: __ZL7file640, symObjAddr: 0xCDDDC, symBinAddr: 0xD4A04, symSize: 0x0 }
  - { offsetInCU: 0x6AE6, offset: 0x317C1, size: 0x4, addend: 0x0, symName: __ZL7file641, symObjAddr: 0xCDF52, symBinAddr: 0xD4B7A, symSize: 0x0 }
  - { offsetInCU: 0x6AFD, offset: 0x317D8, size: 0x4, addend: 0x0, symName: __ZL7file642, symObjAddr: 0xCE08A, symBinAddr: 0xD4CB2, symSize: 0x0 }
  - { offsetInCU: 0x6B14, offset: 0x317EF, size: 0x4, addend: 0x0, symName: __ZL7file643, symObjAddr: 0xCE1F4, symBinAddr: 0xD4E1C, symSize: 0x0 }
  - { offsetInCU: 0x6B38, offset: 0x31813, size: 0x4, addend: 0x0, symName: __ZL7file644, symObjAddr: 0xCE3C9, symBinAddr: 0xD4FF1, symSize: 0x0 }
  - { offsetInCU: 0x6B4F, offset: 0x3182A, size: 0x4, addend: 0x0, symName: __ZL7file645, symObjAddr: 0xCE501, symBinAddr: 0xD5129, symSize: 0x0 }
  - { offsetInCU: 0x6B73, offset: 0x3184E, size: 0x4, addend: 0x0, symName: __ZL7file646, symObjAddr: 0xCEFD4, symBinAddr: 0xD5BFC, symSize: 0x0 }
  - { offsetInCU: 0x6B97, offset: 0x31872, size: 0x4, addend: 0x0, symName: __ZL7file647, symObjAddr: 0xCF16B, symBinAddr: 0xD5D93, symSize: 0x0 }
  - { offsetInCU: 0x6BAE, offset: 0x31889, size: 0x4, addend: 0x0, symName: __ZL7file648, symObjAddr: 0xCF2FE, symBinAddr: 0xD5F26, symSize: 0x0 }
  - { offsetInCU: 0x6BC5, offset: 0x318A0, size: 0x4, addend: 0x0, symName: __ZL7file649, symObjAddr: 0xCF472, symBinAddr: 0xD609A, symSize: 0x0 }
  - { offsetInCU: 0x6BE9, offset: 0x318C4, size: 0x4, addend: 0x0, symName: __ZL7file650, symObjAddr: 0xCFF48, symBinAddr: 0xD6B70, symSize: 0x0 }
  - { offsetInCU: 0x6C00, offset: 0x318DB, size: 0x4, addend: 0x0, symName: __ZL7file651, symObjAddr: 0xD00BC, symBinAddr: 0xD6CE4, symSize: 0x0 }
  - { offsetInCU: 0x6C17, offset: 0x318F2, size: 0x4, addend: 0x0, symName: __ZL9layouts61, symObjAddr: 0x495A8, symBinAddr: 0x501D0, symSize: 0x0 }
  - { offsetInCU: 0x6C2E, offset: 0x31909, size: 0x4, addend: 0x0, symName: __ZL7file652, symObjAddr: 0xD0236, symBinAddr: 0xD6E5E, symSize: 0x0 }
  - { offsetInCU: 0x6C45, offset: 0x31920, size: 0x4, addend: 0x0, symName: __ZL7file653, symObjAddr: 0xD0FF7, symBinAddr: 0xD7C1F, symSize: 0x0 }
  - { offsetInCU: 0x6C69, offset: 0x31944, size: 0x4, addend: 0x0, symName: __ZL7file654, symObjAddr: 0xD1F92, symBinAddr: 0xD8BBA, symSize: 0x0 }
  - { offsetInCU: 0x6C80, offset: 0x3195B, size: 0x4, addend: 0x0, symName: __ZL7file655, symObjAddr: 0xD25B3, symBinAddr: 0xD91DB, symSize: 0x0 }
  - { offsetInCU: 0x6C97, offset: 0x31972, size: 0x4, addend: 0x0, symName: __ZL7file656, symObjAddr: 0xD3372, symBinAddr: 0xD9F9A, symSize: 0x0 }
  - { offsetInCU: 0x6CBB, offset: 0x31996, size: 0x4, addend: 0x0, symName: __ZL7file657, symObjAddr: 0xD3989, symBinAddr: 0xDA5B1, symSize: 0x0 }
  - { offsetInCU: 0x6CD2, offset: 0x319AD, size: 0x4, addend: 0x0, symName: __ZL7file658, symObjAddr: 0xD3FAB, symBinAddr: 0xDABD3, symSize: 0x0 }
  - { offsetInCU: 0x6CF6, offset: 0x319D1, size: 0x4, addend: 0x0, symName: __ZL7file659, symObjAddr: 0xD4F4B, symBinAddr: 0xDBB73, symSize: 0x0 }
  - { offsetInCU: 0x6D0D, offset: 0x319E8, size: 0x4, addend: 0x0, symName: __ZL7file660, symObjAddr: 0xD5D0E, symBinAddr: 0xDC936, symSize: 0x0 }
  - { offsetInCU: 0x6D31, offset: 0x31A0C, size: 0x4, addend: 0x0, symName: __ZL7file661, symObjAddr: 0xD6C71, symBinAddr: 0xDD899, symSize: 0x0 }
  - { offsetInCU: 0x6D55, offset: 0x31A30, size: 0x4, addend: 0x0, symName: __ZL7file662, symObjAddr: 0xD7BD5, symBinAddr: 0xDE7FD, symSize: 0x0 }
  - { offsetInCU: 0x6D79, offset: 0x31A54, size: 0x4, addend: 0x0, symName: __ZL7file663, symObjAddr: 0xD8993, symBinAddr: 0xDF5BB, symSize: 0x0 }
  - { offsetInCU: 0x6D90, offset: 0x31A6B, size: 0x4, addend: 0x0, symName: __ZL7file664, symObjAddr: 0xD9752, symBinAddr: 0xE037A, symSize: 0x0 }
  - { offsetInCU: 0x6DA7, offset: 0x31A82, size: 0x4, addend: 0x0, symName: __ZL7file665, symObjAddr: 0xDA512, symBinAddr: 0xE113A, symSize: 0x0 }
  - { offsetInCU: 0x6DCB, offset: 0x31AA6, size: 0x4, addend: 0x0, symName: __ZL7file666, symObjAddr: 0xDB478, symBinAddr: 0xE20A0, symSize: 0x0 }
  - { offsetInCU: 0x6DE2, offset: 0x31ABD, size: 0x4, addend: 0x0, symName: __ZL9patches61, symObjAddr: 0x1A0B38, symBinAddr: 0x1A76D8, symSize: 0x0 }
  - { offsetInCU: 0x6DF9, offset: 0x31AD4, size: 0x4, addend: 0x0, symName: __ZL10patchBuf86, symObjAddr: 0xDC238, symBinAddr: 0xE2E60, symSize: 0x0 }
  - { offsetInCU: 0x6E10, offset: 0x31AEB, size: 0x4, addend: 0x0, symName: __ZL11platforms62, symObjAddr: 0x496D4, symBinAddr: 0x502FC, symSize: 0x0 }
  - { offsetInCU: 0x6E27, offset: 0x31B02, size: 0x4, addend: 0x0, symName: __ZL7file667, symObjAddr: 0xDC23C, symBinAddr: 0xE2E64, symSize: 0x0 }
  - { offsetInCU: 0x6E4B, offset: 0x31B26, size: 0x4, addend: 0x0, symName: __ZL7file668, symObjAddr: 0xDCAF0, symBinAddr: 0xE3718, symSize: 0x0 }
  - { offsetInCU: 0x6E62, offset: 0x31B3D, size: 0x4, addend: 0x0, symName: __ZL7file669, symObjAddr: 0xDCC2A, symBinAddr: 0xE3852, symSize: 0x0 }
  - { offsetInCU: 0x6E79, offset: 0x31B54, size: 0x4, addend: 0x0, symName: __ZL7file670, symObjAddr: 0xDCD75, symBinAddr: 0xE399D, symSize: 0x0 }
  - { offsetInCU: 0x6E90, offset: 0x31B6B, size: 0x4, addend: 0x0, symName: __ZL7file671, symObjAddr: 0xDCEC0, symBinAddr: 0xE3AE8, symSize: 0x0 }
  - { offsetInCU: 0x6EA7, offset: 0x31B82, size: 0x4, addend: 0x0, symName: __ZL7file672, symObjAddr: 0xDD00B, symBinAddr: 0xE3C33, symSize: 0x0 }
  - { offsetInCU: 0x6EBE, offset: 0x31B99, size: 0x4, addend: 0x0, symName: __ZL7file673, symObjAddr: 0xDD16A, symBinAddr: 0xE3D92, symSize: 0x0 }
  - { offsetInCU: 0x6ED5, offset: 0x31BB0, size: 0x4, addend: 0x0, symName: __ZL9layouts62, symObjAddr: 0x49760, symBinAddr: 0x50388, symSize: 0x0 }
  - { offsetInCU: 0x6EEC, offset: 0x31BC7, size: 0x4, addend: 0x0, symName: __ZL7file674, symObjAddr: 0xDD2C5, symBinAddr: 0xE3EED, symSize: 0x0 }
  - { offsetInCU: 0x6F03, offset: 0x31BDE, size: 0x4, addend: 0x0, symName: __ZL7file675, symObjAddr: 0xDD883, symBinAddr: 0xE44AB, symSize: 0x0 }
  - { offsetInCU: 0x6F27, offset: 0x31C02, size: 0x4, addend: 0x0, symName: __ZL7file676, symObjAddr: 0xDDB68, symBinAddr: 0xE4790, symSize: 0x0 }
  - { offsetInCU: 0x6F4B, offset: 0x31C26, size: 0x4, addend: 0x0, symName: __ZL7file677, symObjAddr: 0xDE8F3, symBinAddr: 0xE551B, symSize: 0x0 }
  - { offsetInCU: 0x6F62, offset: 0x31C3D, size: 0x4, addend: 0x0, symName: __ZL7file678, symObjAddr: 0xDF6B4, symBinAddr: 0xE62DC, symSize: 0x0 }
  - { offsetInCU: 0x6F79, offset: 0x31C54, size: 0x4, addend: 0x0, symName: __ZL7file679, symObjAddr: 0xE0475, symBinAddr: 0xE709D, symSize: 0x0 }
  - { offsetInCU: 0x6F9D, offset: 0x31C78, size: 0x4, addend: 0x0, symName: __ZL7file680, symObjAddr: 0xE0BEE, symBinAddr: 0xE7816, symSize: 0x0 }
  - { offsetInCU: 0x6FC1, offset: 0x31C9C, size: 0x4, addend: 0x0, symName: __ZL9patches62, symObjAddr: 0x1A0C18, symBinAddr: 0x1A77B8, symSize: 0x0 }
  - { offsetInCU: 0x6FD8, offset: 0x31CB3, size: 0x4, addend: 0x0, symName: __ZL10patchBuf87, symObjAddr: 0xE19B9, symBinAddr: 0xE85E1, symSize: 0x0 }
  - { offsetInCU: 0x6FEF, offset: 0x31CCA, size: 0x4, addend: 0x0, symName: __ZL11platforms63, symObjAddr: 0x497EC, symBinAddr: 0x50414, symSize: 0x0 }
  - { offsetInCU: 0x7006, offset: 0x31CE1, size: 0x4, addend: 0x0, symName: __ZL7file681, symObjAddr: 0xE19BD, symBinAddr: 0xE85E5, symSize: 0x0 }
  - { offsetInCU: 0x701D, offset: 0x31CF8, size: 0x4, addend: 0x0, symName: __ZL7file682, symObjAddr: 0xE1B02, symBinAddr: 0xE872A, symSize: 0x0 }
  - { offsetInCU: 0x7034, offset: 0x31D0F, size: 0x4, addend: 0x0, symName: __ZL7file683, symObjAddr: 0xE1C43, symBinAddr: 0xE886B, symSize: 0x0 }
  - { offsetInCU: 0x704B, offset: 0x31D26, size: 0x4, addend: 0x0, symName: __ZL9layouts63, symObjAddr: 0x49828, symBinAddr: 0x50450, symSize: 0x0 }
  - { offsetInCU: 0x7062, offset: 0x31D3D, size: 0x4, addend: 0x0, symName: __ZL7file684, symObjAddr: 0xE1D88, symBinAddr: 0xE89B0, symSize: 0x0 }
  - { offsetInCU: 0x7079, offset: 0x31D54, size: 0x4, addend: 0x0, symName: __ZL7file685, symObjAddr: 0xE2351, symBinAddr: 0xE8F79, symSize: 0x0 }
  - { offsetInCU: 0x709D, offset: 0x31D78, size: 0x4, addend: 0x0, symName: __ZL7file686, symObjAddr: 0xE29B9, symBinAddr: 0xE95E1, symSize: 0x0 }
  - { offsetInCU: 0x70C1, offset: 0x31D9C, size: 0x4, addend: 0x0, symName: __ZL9patches63, symObjAddr: 0x1A0CF8, symBinAddr: 0x1A7898, symSize: 0x0 }
  - { offsetInCU: 0x70D8, offset: 0x31DB3, size: 0x4, addend: 0x0, symName: __ZL10patchBuf88, symObjAddr: 0xE2F89, symBinAddr: 0xE9BB1, symSize: 0x0 }
  - { offsetInCU: 0x70EF, offset: 0x31DCA, size: 0x4, addend: 0x0, symName: __ZL11revisions23, symObjAddr: 0x49864, symBinAddr: 0x5048C, symSize: 0x0 }
  - { offsetInCU: 0x7106, offset: 0x31DE1, size: 0x4, addend: 0x0, symName: __ZL11platforms64, symObjAddr: 0x4986C, symBinAddr: 0x50494, symSize: 0x0 }
  - { offsetInCU: 0x711D, offset: 0x31DF8, size: 0x4, addend: 0x0, symName: __ZL7file687, symObjAddr: 0xE2F8D, symBinAddr: 0xE9BB5, symSize: 0x0 }
  - { offsetInCU: 0x7141, offset: 0x31E1C, size: 0x4, addend: 0x0, symName: __ZL7file688, symObjAddr: 0xE39D2, symBinAddr: 0xEA5FA, symSize: 0x0 }
  - { offsetInCU: 0x7158, offset: 0x31E33, size: 0x4, addend: 0x0, symName: __ZL7file689, symObjAddr: 0xE44DB, symBinAddr: 0xEB103, symSize: 0x0 }
  - { offsetInCU: 0x716F, offset: 0x31E4A, size: 0x4, addend: 0x0, symName: __ZL9layouts64, symObjAddr: 0x498BC, symBinAddr: 0x504E4, symSize: 0x0 }
  - { offsetInCU: 0x7186, offset: 0x31E61, size: 0x4, addend: 0x0, symName: __ZL7file690, symObjAddr: 0xE4625, symBinAddr: 0xEB24D, symSize: 0x0 }
  - { offsetInCU: 0x71AA, offset: 0x31E85, size: 0x4, addend: 0x0, symName: __ZL7file691, symObjAddr: 0xE4D5F, symBinAddr: 0xEB987, symSize: 0x0 }
  - { offsetInCU: 0x71CE, offset: 0x31EA9, size: 0x4, addend: 0x0, symName: __ZL7file692, symObjAddr: 0xE5461, symBinAddr: 0xEC089, symSize: 0x0 }
  - { offsetInCU: 0x71F2, offset: 0x31ECD, size: 0x4, addend: 0x0, symName: __ZL9patches64, symObjAddr: 0x1A0DBC, symBinAddr: 0x1A795C, symSize: 0x0 }
  - { offsetInCU: 0x7209, offset: 0x31EE4, size: 0x4, addend: 0x0, symName: __ZL10patchBuf89, symObjAddr: 0xE5B99, symBinAddr: 0xEC7C1, symSize: 0x0 }
  - { offsetInCU: 0x7220, offset: 0x31EFB, size: 0x4, addend: 0x0, symName: __ZL11revisions24, symObjAddr: 0x4990C, symBinAddr: 0x50534, symSize: 0x0 }
  - { offsetInCU: 0x7237, offset: 0x31F12, size: 0x4, addend: 0x0, symName: __ZL11platforms65, symObjAddr: 0x49914, symBinAddr: 0x5053C, symSize: 0x0 }
  - { offsetInCU: 0x724E, offset: 0x31F29, size: 0x4, addend: 0x0, symName: __ZL7file693, symObjAddr: 0xE5B9D, symBinAddr: 0xEC7C5, symSize: 0x0 }
  - { offsetInCU: 0x7265, offset: 0x31F40, size: 0x4, addend: 0x0, symName: __ZL7file694, symObjAddr: 0xE5CE2, symBinAddr: 0xEC90A, symSize: 0x0 }
  - { offsetInCU: 0x727C, offset: 0x31F57, size: 0x4, addend: 0x0, symName: __ZL7file695, symObjAddr: 0xE5E20, symBinAddr: 0xECA48, symSize: 0x0 }
  - { offsetInCU: 0x72A0, offset: 0x31F7B, size: 0x4, addend: 0x0, symName: __ZL7file696, symObjAddr: 0xE6F19, symBinAddr: 0xEDB41, symSize: 0x0 }
  - { offsetInCU: 0x72C4, offset: 0x31F9F, size: 0x4, addend: 0x0, symName: __ZL7file697, symObjAddr: 0xE7A50, symBinAddr: 0xEE678, symSize: 0x0 }
  - { offsetInCU: 0x72DB, offset: 0x31FB6, size: 0x4, addend: 0x0, symName: __ZL9layouts65, symObjAddr: 0x49978, symBinAddr: 0x505A0, symSize: 0x0 }
  - { offsetInCU: 0x72F2, offset: 0x31FCD, size: 0x4, addend: 0x0, symName: __ZL7file698, symObjAddr: 0xE8593, symBinAddr: 0xEF1BB, symSize: 0x0 }
  - { offsetInCU: 0x7309, offset: 0x31FE4, size: 0x4, addend: 0x0, symName: __ZL7file699, symObjAddr: 0xE88E3, symBinAddr: 0xEF50B, symSize: 0x0 }
  - { offsetInCU: 0x732D, offset: 0x32008, size: 0x4, addend: 0x0, symName: __ZL7file700, symObjAddr: 0xE8C34, symBinAddr: 0xEF85C, symSize: 0x0 }
  - { offsetInCU: 0x7350, offset: 0x3202B, size: 0x4, addend: 0x0, symName: __ZL7file701, symObjAddr: 0xE8D24, symBinAddr: 0xEF94C, symSize: 0x0 }
  - { offsetInCU: 0x7373, offset: 0x3204E, size: 0x4, addend: 0x0, symName: __ZL9patches65, symObjAddr: 0x1A0E80, symBinAddr: 0x1A7A20, symSize: 0x0 }
  - { offsetInCU: 0x738A, offset: 0x32065, size: 0x4, addend: 0x0, symName: __ZL10patchBuf90, symObjAddr: 0xE8E0D, symBinAddr: 0xEFA35, symSize: 0x0 }
  - { offsetInCU: 0x73A1, offset: 0x3207C, size: 0x4, addend: 0x0, symName: __ZL11revisions25, symObjAddr: 0x499DC, symBinAddr: 0x50604, symSize: 0x0 }
  - { offsetInCU: 0x73B8, offset: 0x32093, size: 0x4, addend: 0x0, symName: __ZL11platforms66, symObjAddr: 0x499EC, symBinAddr: 0x50614, symSize: 0x0 }
  - { offsetInCU: 0x73CF, offset: 0x320AA, size: 0x4, addend: 0x0, symName: __ZL7file702, symObjAddr: 0xE8E11, symBinAddr: 0xEFA39, symSize: 0x0 }
  - { offsetInCU: 0x73F3, offset: 0x320CE, size: 0x4, addend: 0x0, symName: __ZL7file703, symObjAddr: 0xE9219, symBinAddr: 0xEFE41, symSize: 0x0 }
  - { offsetInCU: 0x740A, offset: 0x320E5, size: 0x4, addend: 0x0, symName: __ZL7file704, symObjAddr: 0xE937F, symBinAddr: 0xEFFA7, symSize: 0x0 }
  - { offsetInCU: 0x7421, offset: 0x320FC, size: 0x4, addend: 0x0, symName: __ZL7file705, symObjAddr: 0xE953E, symBinAddr: 0xF0166, symSize: 0x0 }
  - { offsetInCU: 0x7438, offset: 0x32113, size: 0x4, addend: 0x0, symName: __ZL7file706, symObjAddr: 0xE967D, symBinAddr: 0xF02A5, symSize: 0x0 }
  - { offsetInCU: 0x745C, offset: 0x32137, size: 0x4, addend: 0x0, symName: __ZL7file707, symObjAddr: 0xE97B3, symBinAddr: 0xF03DB, symSize: 0x0 }
  - { offsetInCU: 0x7473, offset: 0x3214E, size: 0x4, addend: 0x0, symName: __ZL7file708, symObjAddr: 0xE98FD, symBinAddr: 0xF0525, symSize: 0x0 }
  - { offsetInCU: 0x748A, offset: 0x32165, size: 0x4, addend: 0x0, symName: __ZL9layouts66, symObjAddr: 0x49AB4, symBinAddr: 0x506DC, symSize: 0x0 }
  - { offsetInCU: 0x74A1, offset: 0x3217C, size: 0x4, addend: 0x0, symName: __ZL7file709, symObjAddr: 0xE9A3E, symBinAddr: 0xF0666, symSize: 0x0 }
  - { offsetInCU: 0x74B8, offset: 0x32193, size: 0x4, addend: 0x0, symName: __ZL7file710, symObjAddr: 0xE9D58, symBinAddr: 0xF0980, symSize: 0x0 }
  - { offsetInCU: 0x74DC, offset: 0x321B7, size: 0x4, addend: 0x0, symName: __ZL7file711, symObjAddr: 0xEA071, symBinAddr: 0xF0C99, symSize: 0x0 }
  - { offsetInCU: 0x74F3, offset: 0x321CE, size: 0x4, addend: 0x0, symName: __ZL7file712, symObjAddr: 0xEA38B, symBinAddr: 0xF0FB3, symSize: 0x0 }
  - { offsetInCU: 0x7517, offset: 0x321F2, size: 0x4, addend: 0x0, symName: __ZL7file713, symObjAddr: 0xEA79B, symBinAddr: 0xF13C3, symSize: 0x0 }
  - { offsetInCU: 0x753B, offset: 0x32216, size: 0x4, addend: 0x0, symName: __ZL7file714, symObjAddr: 0xEAAB6, symBinAddr: 0xF16DE, symSize: 0x0 }
  - { offsetInCU: 0x7552, offset: 0x3222D, size: 0x4, addend: 0x0, symName: __ZL7file715, symObjAddr: 0xEADD1, symBinAddr: 0xF19F9, symSize: 0x0 }
  - { offsetInCU: 0x7576, offset: 0x32251, size: 0x4, addend: 0x0, symName: __ZL7file716, symObjAddr: 0xEB117, symBinAddr: 0xF1D3F, symSize: 0x0 }
  - { offsetInCU: 0x759A, offset: 0x32275, size: 0x4, addend: 0x0, symName: __ZL7file717, symObjAddr: 0xEB423, symBinAddr: 0xF204B, symSize: 0x0 }
  - { offsetInCU: 0x75B1, offset: 0x3228C, size: 0x4, addend: 0x0, symName: __ZL7file718, symObjAddr: 0xEB73B, symBinAddr: 0xF2363, symSize: 0x0 }
  - { offsetInCU: 0x75C8, offset: 0x322A3, size: 0x4, addend: 0x0, symName: __ZL9patches66, symObjAddr: 0x1A0F60, symBinAddr: 0x1A7B00, symSize: 0x0 }
  - { offsetInCU: 0x75DF, offset: 0x322BA, size: 0x4, addend: 0x0, symName: __ZL10patchBuf91, symObjAddr: 0xEBA54, symBinAddr: 0xF267C, symSize: 0x0 }
  - { offsetInCU: 0x75F6, offset: 0x322D1, size: 0x4, addend: 0x0, symName: __ZL11revisions26, symObjAddr: 0x49B7C, symBinAddr: 0x507A4, symSize: 0x0 }
  - { offsetInCU: 0x760D, offset: 0x322E8, size: 0x4, addend: 0x0, symName: __ZL11platforms67, symObjAddr: 0x49B84, symBinAddr: 0x507AC, symSize: 0x0 }
  - { offsetInCU: 0x7624, offset: 0x322FF, size: 0x4, addend: 0x0, symName: __ZL7file719, symObjAddr: 0xEBA58, symBinAddr: 0xF2680, symSize: 0x0 }
  - { offsetInCU: 0x763B, offset: 0x32316, size: 0x4, addend: 0x0, symName: __ZL7file720, symObjAddr: 0xEBB9D, symBinAddr: 0xF27C5, symSize: 0x0 }
  - { offsetInCU: 0x7652, offset: 0x3232D, size: 0x4, addend: 0x0, symName: __ZL7file721, symObjAddr: 0xEBCE2, symBinAddr: 0xF290A, symSize: 0x0 }
  - { offsetInCU: 0x7669, offset: 0x32344, size: 0x4, addend: 0x0, symName: __ZL9layouts67, symObjAddr: 0x49BC0, symBinAddr: 0x507E8, symSize: 0x0 }
  - { offsetInCU: 0x7680, offset: 0x3235B, size: 0x4, addend: 0x0, symName: __ZL7file722, symObjAddr: 0xEBE27, symBinAddr: 0xF2A4F, symSize: 0x0 }
  - { offsetInCU: 0x76A4, offset: 0x3237F, size: 0x4, addend: 0x0, symName: __ZL7file723, symObjAddr: 0xEC3FC, symBinAddr: 0xF3024, symSize: 0x0 }
  - { offsetInCU: 0x76C8, offset: 0x323A3, size: 0x4, addend: 0x0, symName: __ZL7file724, symObjAddr: 0xEC9D4, symBinAddr: 0xF35FC, symSize: 0x0 }
  - { offsetInCU: 0x76EC, offset: 0x323C7, size: 0x4, addend: 0x0, symName: __ZL9patches67, symObjAddr: 0x1A1024, symBinAddr: 0x1A7BC4, symSize: 0x0 }
  - { offsetInCU: 0x7703, offset: 0x323DE, size: 0x4, addend: 0x0, symName: __ZL10patchBuf92, symObjAddr: 0xECFAA, symBinAddr: 0xF3BD2, symSize: 0x0 }
  - { offsetInCU: 0x771A, offset: 0x323F5, size: 0x4, addend: 0x0, symName: __ZL11revisions27, symObjAddr: 0x49BFC, symBinAddr: 0x50824, symSize: 0x0 }
  - { offsetInCU: 0x7731, offset: 0x3240C, size: 0x4, addend: 0x0, symName: __ZL11platforms68, symObjAddr: 0x49C04, symBinAddr: 0x5082C, symSize: 0x0 }
  - { offsetInCU: 0x7748, offset: 0x32423, size: 0x4, addend: 0x0, symName: __ZL7file725, symObjAddr: 0xECFAE, symBinAddr: 0xF3BD6, symSize: 0x0 }
  - { offsetInCU: 0x775F, offset: 0x3243A, size: 0x4, addend: 0x0, symName: __ZL7file726, symObjAddr: 0xED0E9, symBinAddr: 0xF3D11, symSize: 0x0 }
  - { offsetInCU: 0x7776, offset: 0x32451, size: 0x4, addend: 0x0, symName: __ZL7file727, symObjAddr: 0xED222, symBinAddr: 0xF3E4A, symSize: 0x0 }
  - { offsetInCU: 0x778D, offset: 0x32468, size: 0x4, addend: 0x0, symName: __ZL9layouts68, symObjAddr: 0x49C40, symBinAddr: 0x50868, symSize: 0x0 }
  - { offsetInCU: 0x77A4, offset: 0x3247F, size: 0x4, addend: 0x0, symName: __ZL7file728, symObjAddr: 0xED362, symBinAddr: 0xF3F8A, symSize: 0x0 }
  - { offsetInCU: 0x77C8, offset: 0x324A3, size: 0x4, addend: 0x0, symName: __ZL7file729, symObjAddr: 0xED8A8, symBinAddr: 0xF44D0, symSize: 0x0 }
  - { offsetInCU: 0x77EC, offset: 0x324C7, size: 0x4, addend: 0x0, symName: __ZL7file730, symObjAddr: 0xEDDCD, symBinAddr: 0xF49F5, symSize: 0x0 }
  - { offsetInCU: 0x780F, offset: 0x324EA, size: 0x4, addend: 0x0, symName: __ZL9patches68, symObjAddr: 0x1A1104, symBinAddr: 0x1A7CA4, symSize: 0x0 }
  - { offsetInCU: 0x7826, offset: 0x32501, size: 0x4, addend: 0x0, symName: __ZL10patchBuf93, symObjAddr: 0xEDEAD, symBinAddr: 0xF4AD5, symSize: 0x0 }
  - { offsetInCU: 0x783D, offset: 0x32518, size: 0x4, addend: 0x0, symName: __ZL11revisions28, symObjAddr: 0x49C7C, symBinAddr: 0x508A4, symSize: 0x0 }
  - { offsetInCU: 0x7854, offset: 0x3252F, size: 0x4, addend: 0x0, symName: __ZL11platforms69, symObjAddr: 0x49C80, symBinAddr: 0x508A8, symSize: 0x0 }
  - { offsetInCU: 0x7877, offset: 0x32552, size: 0x4, addend: 0x0, symName: __ZL7file731, symObjAddr: 0xEDEB1, symBinAddr: 0xF4AD9, symSize: 0x0 }
  - { offsetInCU: 0x788E, offset: 0x32569, size: 0x4, addend: 0x0, symName: __ZL7file732, symObjAddr: 0xEE0EF, symBinAddr: 0xF4D17, symSize: 0x0 }
  - { offsetInCU: 0x78B2, offset: 0x3258D, size: 0x4, addend: 0x0, symName: __ZL7file733, symObjAddr: 0xEE28B, symBinAddr: 0xF4EB3, symSize: 0x0 }
  - { offsetInCU: 0x78D6, offset: 0x325B1, size: 0x4, addend: 0x0, symName: __ZL7file734, symObjAddr: 0xEE3F8, symBinAddr: 0xF5020, symSize: 0x0 }
  - { offsetInCU: 0x78FA, offset: 0x325D5, size: 0x4, addend: 0x0, symName: __ZL7file735, symObjAddr: 0xEE596, symBinAddr: 0xF51BE, symSize: 0x0 }
  - { offsetInCU: 0x7911, offset: 0x325EC, size: 0x4, addend: 0x0, symName: __ZL7file736, symObjAddr: 0xEE72D, symBinAddr: 0xF5355, symSize: 0x0 }
  - { offsetInCU: 0x7935, offset: 0x32610, size: 0x4, addend: 0x0, symName: __ZL7file737, symObjAddr: 0xEEC82, symBinAddr: 0xF58AA, symSize: 0x0 }
  - { offsetInCU: 0x7959, offset: 0x32634, size: 0x4, addend: 0x0, symName: __ZL7file738, symObjAddr: 0xEF1E7, symBinAddr: 0xF5E0F, symSize: 0x0 }
  - { offsetInCU: 0x797D, offset: 0x32658, size: 0x4, addend: 0x0, symName: __ZL7file739, symObjAddr: 0xEF366, symBinAddr: 0xF5F8E, symSize: 0x0 }
  - { offsetInCU: 0x79A1, offset: 0x3267C, size: 0x4, addend: 0x0, symName: __ZL9layouts69, symObjAddr: 0x49D70, symBinAddr: 0x50998, symSize: 0x0 }
  - { offsetInCU: 0x79B8, offset: 0x32693, size: 0x4, addend: 0x0, symName: __ZL7file740, symObjAddr: 0xEF500, symBinAddr: 0xF6128, symSize: 0x0 }
  - { offsetInCU: 0x79CF, offset: 0x326AA, size: 0x4, addend: 0x0, symName: __ZL7file741, symObjAddr: 0xEF851, symBinAddr: 0xF6479, symSize: 0x0 }
  - { offsetInCU: 0x79E6, offset: 0x326C1, size: 0x4, addend: 0x0, symName: __ZL7file742, symObjAddr: 0xEFBA2, symBinAddr: 0xF67CA, symSize: 0x0 }
  - { offsetInCU: 0x79FD, offset: 0x326D8, size: 0x4, addend: 0x0, symName: __ZL7file743, symObjAddr: 0xEFEF3, symBinAddr: 0xF6B1B, symSize: 0x0 }
  - { offsetInCU: 0x7A14, offset: 0x326EF, size: 0x4, addend: 0x0, symName: __ZL7file744, symObjAddr: 0xF0244, symBinAddr: 0xF6E6C, symSize: 0x0 }
  - { offsetInCU: 0x7A38, offset: 0x32713, size: 0x4, addend: 0x0, symName: __ZL7file745, symObjAddr: 0xF0592, symBinAddr: 0xF71BA, symSize: 0x0 }
  - { offsetInCU: 0x7A5C, offset: 0x32737, size: 0x4, addend: 0x0, symName: __ZL7file746, symObjAddr: 0xF08DB, symBinAddr: 0xF7503, symSize: 0x0 }
  - { offsetInCU: 0x7A73, offset: 0x3274E, size: 0x4, addend: 0x0, symName: __ZL7file747, symObjAddr: 0xF0C24, symBinAddr: 0xF784C, symSize: 0x0 }
  - { offsetInCU: 0x7A8A, offset: 0x32765, size: 0x4, addend: 0x0, symName: __ZL7file748, symObjAddr: 0xF0F77, symBinAddr: 0xF7B9F, symSize: 0x0 }
  - { offsetInCU: 0x7AAE, offset: 0x32789, size: 0x4, addend: 0x0, symName: __ZL7file749, symObjAddr: 0xF1686, symBinAddr: 0xF82AE, symSize: 0x0 }
  - { offsetInCU: 0x7AD2, offset: 0x327AD, size: 0x4, addend: 0x0, symName: __ZL7file750, symObjAddr: 0xF1DB9, symBinAddr: 0xF89E1, symSize: 0x0 }
  - { offsetInCU: 0x7AF6, offset: 0x327D1, size: 0x4, addend: 0x0, symName: __ZL7file751, symObjAddr: 0xF23E5, symBinAddr: 0xF900D, symSize: 0x0 }
  - { offsetInCU: 0x7B0D, offset: 0x327E8, size: 0x4, addend: 0x0, symName: __ZL9patches69, symObjAddr: 0x1A121C, symBinAddr: 0x1A7DBC, symSize: 0x0 }
  - { offsetInCU: 0x7B24, offset: 0x327FF, size: 0x4, addend: 0x0, symName: __ZL10patchBuf94, symObjAddr: 0xF2736, symBinAddr: 0xF935E, symSize: 0x0 }
  - { offsetInCU: 0x7B3B, offset: 0x32816, size: 0x4, addend: 0x0, symName: __ZL11platforms70, symObjAddr: 0x49E60, symBinAddr: 0x50A88, symSize: 0x0 }
  - { offsetInCU: 0x7B52, offset: 0x3282D, size: 0x4, addend: 0x0, symName: __ZL7file752, symObjAddr: 0xF273A, symBinAddr: 0xF9362, symSize: 0x0 }
  - { offsetInCU: 0x7B69, offset: 0x32844, size: 0x4, addend: 0x0, symName: __ZL7file753, symObjAddr: 0xF2871, symBinAddr: 0xF9499, symSize: 0x0 }
  - { offsetInCU: 0x7B80, offset: 0x3285B, size: 0x4, addend: 0x0, symName: __ZL7file754, symObjAddr: 0xF29A9, symBinAddr: 0xF95D1, symSize: 0x0 }
  - { offsetInCU: 0x7B97, offset: 0x32872, size: 0x4, addend: 0x0, symName: __ZL9layouts70, symObjAddr: 0x49E9C, symBinAddr: 0x50AC4, symSize: 0x0 }
  - { offsetInCU: 0x7BAE, offset: 0x32889, size: 0x4, addend: 0x0, symName: __ZL7file755, symObjAddr: 0xF2AE1, symBinAddr: 0xF9709, symSize: 0x0 }
  - { offsetInCU: 0x7BD2, offset: 0x328AD, size: 0x4, addend: 0x0, symName: __ZL7file756, symObjAddr: 0xF31A0, symBinAddr: 0xF9DC8, symSize: 0x0 }
  - { offsetInCU: 0x7BE9, offset: 0x328C4, size: 0x4, addend: 0x0, symName: __ZL7file757, symObjAddr: 0xF37D3, symBinAddr: 0xFA3FB, symSize: 0x0 }
  - { offsetInCU: 0x7C00, offset: 0x328DB, size: 0x4, addend: 0x0, symName: __ZL9patches70, symObjAddr: 0x1A12E0, symBinAddr: 0x1A7E80, symSize: 0x0 }
  - { offsetInCU: 0x7C17, offset: 0x328F2, size: 0x4, addend: 0x0, symName: __ZL10patchBuf95, symObjAddr: 0xF3E07, symBinAddr: 0xFAA2F, symSize: 0x0 }
  - { offsetInCU: 0x7C2E, offset: 0x32909, size: 0x4, addend: 0x0, symName: __ZL11revisions29, symObjAddr: 0x49ED8, symBinAddr: 0x50B00, symSize: 0x0 }
  - { offsetInCU: 0x7C45, offset: 0x32920, size: 0x4, addend: 0x0, symName: __ZL11platforms71, symObjAddr: 0x49EDC, symBinAddr: 0x50B04, symSize: 0x0 }
  - { offsetInCU: 0x7C5C, offset: 0x32937, size: 0x4, addend: 0x0, symName: __ZL7file758, symObjAddr: 0xF3E0B, symBinAddr: 0xFAA33, symSize: 0x0 }
  - { offsetInCU: 0x7C73, offset: 0x3294E, size: 0x4, addend: 0x0, symName: __ZL7file759, symObjAddr: 0xF4213, symBinAddr: 0xFAE3B, symSize: 0x0 }
  - { offsetInCU: 0x7C8A, offset: 0x32965, size: 0x4, addend: 0x0, symName: __ZL7file760, symObjAddr: 0xF4350, symBinAddr: 0xFAF78, symSize: 0x0 }
  - { offsetInCU: 0x7CA1, offset: 0x3297C, size: 0x4, addend: 0x0, symName: __ZL9layouts71, symObjAddr: 0x49F40, symBinAddr: 0x50B68, symSize: 0x0 }
  - { offsetInCU: 0x7CB8, offset: 0x32993, size: 0x4, addend: 0x0, symName: __ZL7file761, symObjAddr: 0xF44AB, symBinAddr: 0xFB0D3, symSize: 0x0 }
  - { offsetInCU: 0x7CCF, offset: 0x329AA, size: 0x4, addend: 0x0, symName: __ZL7file762, symObjAddr: 0xF47C7, symBinAddr: 0xFB3EF, symSize: 0x0 }
  - { offsetInCU: 0x7CE6, offset: 0x329C1, size: 0x4, addend: 0x0, symName: __ZL7file763, symObjAddr: 0xF4AE2, symBinAddr: 0xFB70A, symSize: 0x0 }
  - { offsetInCU: 0x7CFD, offset: 0x329D8, size: 0x4, addend: 0x0, symName: __ZL7file764, symObjAddr: 0xF4DFD, symBinAddr: 0xFBA25, symSize: 0x0 }
  - { offsetInCU: 0x7D14, offset: 0x329EF, size: 0x4, addend: 0x0, symName: __ZL7file765, symObjAddr: 0xF5118, symBinAddr: 0xFBD40, symSize: 0x0 }
  - { offsetInCU: 0x7D2B, offset: 0x32A06, size: 0x4, addend: 0x0, symName: __ZL9patches71, symObjAddr: 0x1A13F8, symBinAddr: 0x1A7F98, symSize: 0x0 }
  - { offsetInCU: 0x7D42, offset: 0x32A1D, size: 0x4, addend: 0x0, symName: __ZL10patchBuf96, symObjAddr: 0xF5287, symBinAddr: 0xFBEAF, symSize: 0x0 }
  - { offsetInCU: 0x7D59, offset: 0x32A34, size: 0x4, addend: 0x0, symName: __ZL11revisions30, symObjAddr: 0x49FA4, symBinAddr: 0x50BCC, symSize: 0x0 }
  - { offsetInCU: 0x7D70, offset: 0x32A4B, size: 0x4, addend: 0x0, symName: __ZL11platforms72, symObjAddr: 0x49FA8, symBinAddr: 0x50BD0, symSize: 0x0 }
  - { offsetInCU: 0x7D87, offset: 0x32A62, size: 0x4, addend: 0x0, symName: __ZL7file766, symObjAddr: 0xF528B, symBinAddr: 0xFBEB3, symSize: 0x0 }
  - { offsetInCU: 0x7D9E, offset: 0x32A79, size: 0x4, addend: 0x0, symName: __ZL7file767, symObjAddr: 0xF53E6, symBinAddr: 0xFC00E, symSize: 0x0 }
  - { offsetInCU: 0x7DB5, offset: 0x32A90, size: 0x4, addend: 0x0, symName: __ZL7file768, symObjAddr: 0xF552F, symBinAddr: 0xFC157, symSize: 0x0 }
  - { offsetInCU: 0x7DCC, offset: 0x32AA7, size: 0x4, addend: 0x0, symName: __ZL7file769, symObjAddr: 0xF567A, symBinAddr: 0xFC2A2, symSize: 0x0 }
  - { offsetInCU: 0x7DE3, offset: 0x32ABE, size: 0x4, addend: 0x0, symName: __ZL7file770, symObjAddr: 0xF57C4, symBinAddr: 0xFC3EC, symSize: 0x0 }
  - { offsetInCU: 0x7DFA, offset: 0x32AD5, size: 0x4, addend: 0x0, symName: __ZL9layouts72, symObjAddr: 0x4A00C, symBinAddr: 0x50C34, symSize: 0x0 }
  - { offsetInCU: 0x7E11, offset: 0x32AEC, size: 0x4, addend: 0x0, symName: __ZL7file771, symObjAddr: 0xF58FE, symBinAddr: 0xFC526, symSize: 0x0 }
  - { offsetInCU: 0x7E35, offset: 0x32B10, size: 0x4, addend: 0x0, symName: __ZL7file772, symObjAddr: 0xF5F2D, symBinAddr: 0xFCB55, symSize: 0x0 }
  - { offsetInCU: 0x7E4C, offset: 0x32B27, size: 0x4, addend: 0x0, symName: __ZL7file773, symObjAddr: 0xF6459, symBinAddr: 0xFD081, symSize: 0x0 }
  - { offsetInCU: 0x7E63, offset: 0x32B3E, size: 0x4, addend: 0x0, symName: __ZL7file774, symObjAddr: 0xF6986, symBinAddr: 0xFD5AE, symSize: 0x0 }
  - { offsetInCU: 0x7E87, offset: 0x32B62, size: 0x4, addend: 0x0, symName: __ZL7file775, symObjAddr: 0xF6EB5, symBinAddr: 0xFDADD, symSize: 0x0 }
  - { offsetInCU: 0x7E9E, offset: 0x32B79, size: 0x4, addend: 0x0, symName: __ZL9patches72, symObjAddr: 0x1A14BC, symBinAddr: 0x1A805C, symSize: 0x0 }
  - { offsetInCU: 0x7EB5, offset: 0x32B90, size: 0x4, addend: 0x0, symName: __ZL10patchBuf97, symObjAddr: 0xF73E4, symBinAddr: 0xFE00C, symSize: 0x0 }
  - { offsetInCU: 0x7ECC, offset: 0x32BA7, size: 0x4, addend: 0x0, symName: __ZL11platforms73, symObjAddr: 0x4A070, symBinAddr: 0x50C98, symSize: 0x0 }
  - { offsetInCU: 0x7EEF, offset: 0x32BCA, size: 0x4, addend: 0x0, symName: __ZL7file776, symObjAddr: 0xF73E8, symBinAddr: 0xFE010, symSize: 0x0 }
  - { offsetInCU: 0x7F06, offset: 0x32BE1, size: 0x4, addend: 0x0, symName: __ZL7file777, symObjAddr: 0xF752C, symBinAddr: 0xFE154, symSize: 0x0 }
  - { offsetInCU: 0x7F2A, offset: 0x32C05, size: 0x4, addend: 0x0, symName: __ZL7file778, symObjAddr: 0xF809C, symBinAddr: 0xFECC4, symSize: 0x0 }
  - { offsetInCU: 0x7F4D, offset: 0x32C28, size: 0x4, addend: 0x0, symName: __ZL7file779, symObjAddr: 0xF8143, symBinAddr: 0xFED6B, symSize: 0x0 }
  - { offsetInCU: 0x7F64, offset: 0x32C3F, size: 0x4, addend: 0x0, symName: __ZL7file780, symObjAddr: 0xF8280, symBinAddr: 0xFEEA8, symSize: 0x0 }
  - { offsetInCU: 0x7F7B, offset: 0x32C56, size: 0x4, addend: 0x0, symName: __ZL7file781, symObjAddr: 0xF83C1, symBinAddr: 0xFEFE9, symSize: 0x0 }
  - { offsetInCU: 0x7F92, offset: 0x32C6D, size: 0x4, addend: 0x0, symName: __ZL7file782, symObjAddr: 0xF8502, symBinAddr: 0xFF12A, symSize: 0x0 }
  - { offsetInCU: 0x7FA9, offset: 0x32C84, size: 0x4, addend: 0x0, symName: __ZL7file783, symObjAddr: 0xF864E, symBinAddr: 0xFF276, symSize: 0x0 }
  - { offsetInCU: 0x7FC0, offset: 0x32C9B, size: 0x4, addend: 0x0, symName: __ZL7file784, symObjAddr: 0xF8791, symBinAddr: 0xFF3B9, symSize: 0x0 }
  - { offsetInCU: 0x7FD7, offset: 0x32CB2, size: 0x4, addend: 0x0, symName: __ZL9layouts73, symObjAddr: 0x4A124, symBinAddr: 0x50D4C, symSize: 0x0 }
  - { offsetInCU: 0x7FEE, offset: 0x32CC9, size: 0x4, addend: 0x0, symName: __ZL7file785, symObjAddr: 0xF88D2, symBinAddr: 0xFF4FA, symSize: 0x0 }
  - { offsetInCU: 0x8005, offset: 0x32CE0, size: 0x4, addend: 0x0, symName: __ZL7file786, symObjAddr: 0xF8E92, symBinAddr: 0xFFABA, symSize: 0x0 }
  - { offsetInCU: 0x801C, offset: 0x32CF7, size: 0x4, addend: 0x0, symName: __ZL7file787, symObjAddr: 0xF9462, symBinAddr: 0x10008A, symSize: 0x0 }
  - { offsetInCU: 0x803F, offset: 0x32D1A, size: 0x4, addend: 0x0, symName: __ZL7file788, symObjAddr: 0xF94FD, symBinAddr: 0x100125, symSize: 0x0 }
  - { offsetInCU: 0x8063, offset: 0x32D3E, size: 0x4, addend: 0x0, symName: __ZL7file789, symObjAddr: 0xF9B10, symBinAddr: 0x100738, symSize: 0x0 }
  - { offsetInCU: 0x8087, offset: 0x32D62, size: 0x4, addend: 0x0, symName: __ZL7file790, symObjAddr: 0xFA125, symBinAddr: 0x100D4D, symSize: 0x0 }
  - { offsetInCU: 0x80AB, offset: 0x32D86, size: 0x4, addend: 0x0, symName: __ZL7file791, symObjAddr: 0xFA739, symBinAddr: 0x101361, symSize: 0x0 }
  - { offsetInCU: 0x80C2, offset: 0x32D9D, size: 0x4, addend: 0x0, symName: __ZL7file792, symObjAddr: 0xFAD5B, symBinAddr: 0x101983, symSize: 0x0 }
  - { offsetInCU: 0x80E6, offset: 0x32DC1, size: 0x4, addend: 0x0, symName: __ZL7file793, symObjAddr: 0xFB31A, symBinAddr: 0x101F42, symSize: 0x0 }
  - { offsetInCU: 0x810A, offset: 0x32DE5, size: 0x4, addend: 0x0, symName: __ZL9patches73, symObjAddr: 0x1A1580, symBinAddr: 0x1A8120, symSize: 0x0 }
  - { offsetInCU: 0x8121, offset: 0x32DFC, size: 0x4, addend: 0x0, symName: __ZL10patchBuf98, symObjAddr: 0xFB8E0, symBinAddr: 0x102508, symSize: 0x0 }
  - { offsetInCU: 0x8138, offset: 0x32E13, size: 0x4, addend: 0x0, symName: __ZL11platforms74, symObjAddr: 0x4A1D8, symBinAddr: 0x50E00, symSize: 0x0 }
  - { offsetInCU: 0x814F, offset: 0x32E2A, size: 0x4, addend: 0x0, symName: __ZL7file794, symObjAddr: 0xFB8E4, symBinAddr: 0x10250C, symSize: 0x0 }
  - { offsetInCU: 0x8166, offset: 0x32E41, size: 0x4, addend: 0x0, symName: __ZL7file795, symObjAddr: 0xFBA23, symBinAddr: 0x10264B, symSize: 0x0 }
  - { offsetInCU: 0x817D, offset: 0x32E58, size: 0x4, addend: 0x0, symName: __ZL7file796, symObjAddr: 0xFBB8D, symBinAddr: 0x1027B5, symSize: 0x0 }
  - { offsetInCU: 0x81A1, offset: 0x32E7C, size: 0x4, addend: 0x0, symName: __ZL7file797, symObjAddr: 0xFBD0E, symBinAddr: 0x102936, symSize: 0x0 }
  - { offsetInCU: 0x81B8, offset: 0x32E93, size: 0x4, addend: 0x0, symName: __ZL7file798, symObjAddr: 0xFBE49, symBinAddr: 0x102A71, symSize: 0x0 }
  - { offsetInCU: 0x81CF, offset: 0x32EAA, size: 0x4, addend: 0x0, symName: __ZL7file799, symObjAddr: 0xFBF85, symBinAddr: 0x102BAD, symSize: 0x0 }
  - { offsetInCU: 0x81E6, offset: 0x32EC1, size: 0x4, addend: 0x0, symName: __ZL7file800, symObjAddr: 0xFC0FF, symBinAddr: 0x102D27, symSize: 0x0 }
  - { offsetInCU: 0x820A, offset: 0x32EE5, size: 0x4, addend: 0x0, symName: __ZL7file801, symObjAddr: 0xFC25C, symBinAddr: 0x102E84, symSize: 0x0 }
  - { offsetInCU: 0x8221, offset: 0x32EFC, size: 0x4, addend: 0x0, symName: __ZL7file802, symObjAddr: 0xFC3C5, symBinAddr: 0x102FED, symSize: 0x0 }
  - { offsetInCU: 0x8238, offset: 0x32F13, size: 0x4, addend: 0x0, symName: __ZL7file803, symObjAddr: 0xFC506, symBinAddr: 0x10312E, symSize: 0x0 }
  - { offsetInCU: 0x824F, offset: 0x32F2A, size: 0x4, addend: 0x0, symName: __ZL7file804, symObjAddr: 0xFC64E, symBinAddr: 0x103276, symSize: 0x0 }
  - { offsetInCU: 0x8266, offset: 0x32F41, size: 0x4, addend: 0x0, symName: __ZL9layouts74, symObjAddr: 0x4A2B4, symBinAddr: 0x50EDC, symSize: 0x0 }
  - { offsetInCU: 0x827D, offset: 0x32F58, size: 0x4, addend: 0x0, symName: __ZL7file805, symObjAddr: 0xFC794, symBinAddr: 0x1033BC, symSize: 0x0 }
  - { offsetInCU: 0x8294, offset: 0x32F6F, size: 0x4, addend: 0x0, symName: __ZL7file806, symObjAddr: 0xFCDB8, symBinAddr: 0x1039E0, symSize: 0x0 }
  - { offsetInCU: 0x82AB, offset: 0x32F86, size: 0x4, addend: 0x0, symName: __ZL7file807, symObjAddr: 0xFD3D8, symBinAddr: 0x104000, symSize: 0x0 }
  - { offsetInCU: 0x82CF, offset: 0x32FAA, size: 0x4, addend: 0x0, symName: __ZL7file808, symObjAddr: 0xFDA2A, symBinAddr: 0x104652, symSize: 0x0 }
  - { offsetInCU: 0x82E6, offset: 0x32FC1, size: 0x4, addend: 0x0, symName: __ZL7file809, symObjAddr: 0xFE04C, symBinAddr: 0x104C74, symSize: 0x0 }
  - { offsetInCU: 0x82FD, offset: 0x32FD8, size: 0x4, addend: 0x0, symName: __ZL7file810, symObjAddr: 0xFE66E, symBinAddr: 0x105296, symSize: 0x0 }
  - { offsetInCU: 0x8321, offset: 0x32FFC, size: 0x4, addend: 0x0, symName: __ZL7file811, symObjAddr: 0xFF5A6, symBinAddr: 0x1061CE, symSize: 0x0 }
  - { offsetInCU: 0x8338, offset: 0x33013, size: 0x4, addend: 0x0, symName: __ZL7file812, symObjAddr: 0xFFBC8, symBinAddr: 0x1067F0, symSize: 0x0 }
  - { offsetInCU: 0x834F, offset: 0x3302A, size: 0x4, addend: 0x0, symName: __ZL7file813, symObjAddr: 0x1001E8, symBinAddr: 0x106E10, symSize: 0x0 }
  - { offsetInCU: 0x8366, offset: 0x33041, size: 0x4, addend: 0x0, symName: __ZL7file814, symObjAddr: 0x10080B, symBinAddr: 0x107433, symSize: 0x0 }
  - { offsetInCU: 0x838A, offset: 0x33065, size: 0x4, addend: 0x0, symName: __ZL7file815, symObjAddr: 0x100DC8, symBinAddr: 0x1079F0, symSize: 0x0 }
  - { offsetInCU: 0x83AE, offset: 0x33089, size: 0x4, addend: 0x0, symName: __ZL9patches74, symObjAddr: 0x1A1660, symBinAddr: 0x1A8200, symSize: 0x0 }
  - { offsetInCU: 0x83C5, offset: 0x330A0, size: 0x4, addend: 0x0, symName: __ZL10patchBuf99, symObjAddr: 0x1013ED, symBinAddr: 0x108015, symSize: 0x0 }
  - { offsetInCU: 0x83DC, offset: 0x330B7, size: 0x4, addend: 0x0, symName: __ZL11revisions31, symObjAddr: 0x4A390, symBinAddr: 0x50FB8, symSize: 0x0 }
  - { offsetInCU: 0x83F3, offset: 0x330CE, size: 0x4, addend: 0x0, symName: __ZL11platforms75, symObjAddr: 0x4A398, symBinAddr: 0x50FC0, symSize: 0x0 }
  - { offsetInCU: 0x8416, offset: 0x330F1, size: 0x4, addend: 0x0, symName: __ZL7file816, symObjAddr: 0x1013F1, symBinAddr: 0x108019, symSize: 0x0 }
  - { offsetInCU: 0x842D, offset: 0x33108, size: 0x4, addend: 0x0, symName: __ZL7file817, symObjAddr: 0x10162F, symBinAddr: 0x108257, symSize: 0x0 }
  - { offsetInCU: 0x8451, offset: 0x3312C, size: 0x4, addend: 0x0, symName: __ZL7file818, symObjAddr: 0x101806, symBinAddr: 0x10842E, symSize: 0x0 }
  - { offsetInCU: 0x8475, offset: 0x33150, size: 0x4, addend: 0x0, symName: __ZL7file819, symObjAddr: 0x102428, symBinAddr: 0x109050, symSize: 0x0 }
  - { offsetInCU: 0x848C, offset: 0x33167, size: 0x4, addend: 0x0, symName: __ZL7file820, symObjAddr: 0x10259C, symBinAddr: 0x1091C4, symSize: 0x0 }
  - { offsetInCU: 0x84A3, offset: 0x3317E, size: 0x4, addend: 0x0, symName: __ZL7file821, symObjAddr: 0x10271D, symBinAddr: 0x109345, symSize: 0x0 }
  - { offsetInCU: 0x84BA, offset: 0x33195, size: 0x4, addend: 0x0, symName: __ZL7file822, symObjAddr: 0x10284E, symBinAddr: 0x109476, symSize: 0x0 }
  - { offsetInCU: 0x84D1, offset: 0x331AC, size: 0x4, addend: 0x0, symName: __ZL7file823, symObjAddr: 0x1029A7, symBinAddr: 0x1095CF, symSize: 0x0 }
  - { offsetInCU: 0x84E8, offset: 0x331C3, size: 0x4, addend: 0x0, symName: __ZL7file824, symObjAddr: 0x102AFC, symBinAddr: 0x109724, symSize: 0x0 }
  - { offsetInCU: 0x850C, offset: 0x331E7, size: 0x4, addend: 0x0, symName: __ZL7file825, symObjAddr: 0x102CAF, symBinAddr: 0x1098D7, symSize: 0x0 }
  - { offsetInCU: 0x8523, offset: 0x331FE, size: 0x4, addend: 0x0, symName: __ZL7file826, symObjAddr: 0x102E1E, symBinAddr: 0x109A46, symSize: 0x0 }
  - { offsetInCU: 0x853A, offset: 0x33215, size: 0x4, addend: 0x0, symName: __ZL9layouts75, symObjAddr: 0x4A500, symBinAddr: 0x51128, symSize: 0x0 }
  - { offsetInCU: 0x8551, offset: 0x3322C, size: 0x4, addend: 0x0, symName: __ZL7file827, symObjAddr: 0x102F9F, symBinAddr: 0x109BC7, symSize: 0x0 }
  - { offsetInCU: 0x8568, offset: 0x33243, size: 0x4, addend: 0x0, symName: __ZL7file828, symObjAddr: 0x103399, symBinAddr: 0x109FC1, symSize: 0x0 }
  - { offsetInCU: 0x857F, offset: 0x3325A, size: 0x4, addend: 0x0, symName: __ZL7file829, symObjAddr: 0x103793, symBinAddr: 0x10A3BB, symSize: 0x0 }
  - { offsetInCU: 0x8596, offset: 0x33271, size: 0x4, addend: 0x0, symName: __ZL7file830, symObjAddr: 0x103B8E, symBinAddr: 0x10A7B6, symSize: 0x0 }
  - { offsetInCU: 0x85BA, offset: 0x33295, size: 0x4, addend: 0x0, symName: __ZL7file831, symObjAddr: 0x103EC3, symBinAddr: 0x10AAEB, symSize: 0x0 }
  - { offsetInCU: 0x85DE, offset: 0x332B9, size: 0x4, addend: 0x0, symName: __ZL7file832, symObjAddr: 0x1041F9, symBinAddr: 0x10AE21, symSize: 0x0 }
  - { offsetInCU: 0x85F5, offset: 0x332D0, size: 0x4, addend: 0x0, symName: __ZL7file833, symObjAddr: 0x10458A, symBinAddr: 0x10B1B2, symSize: 0x0 }
  - { offsetInCU: 0x860C, offset: 0x332E7, size: 0x4, addend: 0x0, symName: __ZL7file834, symObjAddr: 0x10497E, symBinAddr: 0x10B5A6, symSize: 0x0 }
  - { offsetInCU: 0x8630, offset: 0x3330B, size: 0x4, addend: 0x0, symName: __ZL7file835, symObjAddr: 0x104D5A, symBinAddr: 0x10B982, symSize: 0x0 }
  - { offsetInCU: 0x8647, offset: 0x33322, size: 0x4, addend: 0x0, symName: __ZL7file836, symObjAddr: 0x105076, symBinAddr: 0x10BC9E, symSize: 0x0 }
  - { offsetInCU: 0x866B, offset: 0x33346, size: 0x4, addend: 0x0, symName: __ZL7file837, symObjAddr: 0x1053DB, symBinAddr: 0x10C003, symSize: 0x0 }
  - { offsetInCU: 0x868F, offset: 0x3336A, size: 0x4, addend: 0x0, symName: __ZL7file838, symObjAddr: 0x1056E2, symBinAddr: 0x10C30A, symSize: 0x0 }
  - { offsetInCU: 0x86A6, offset: 0x33381, size: 0x4, addend: 0x0, symName: __ZL7file839, symObjAddr: 0x1057B1, symBinAddr: 0x10C3D9, symSize: 0x0 }
  - { offsetInCU: 0x86CA, offset: 0x333A5, size: 0x4, addend: 0x0, symName: __ZL7file840, symObjAddr: 0x105AAD, symBinAddr: 0x10C6D5, symSize: 0x0 }
  - { offsetInCU: 0x86EE, offset: 0x333C9, size: 0x4, addend: 0x0, symName: __ZL7file841, symObjAddr: 0x105E03, symBinAddr: 0x10CA2B, symSize: 0x0 }
  - { offsetInCU: 0x8712, offset: 0x333ED, size: 0x4, addend: 0x0, symName: __ZL7file842, symObjAddr: 0x10628B, symBinAddr: 0x10CEB3, symSize: 0x0 }
  - { offsetInCU: 0x8736, offset: 0x33411, size: 0x4, addend: 0x0, symName: __ZL7file843, symObjAddr: 0x106714, symBinAddr: 0x10D33C, symSize: 0x0 }
  - { offsetInCU: 0x874D, offset: 0x33428, size: 0x4, addend: 0x0, symName: __ZL7file844, symObjAddr: 0x106A48, symBinAddr: 0x10D670, symSize: 0x0 }
  - { offsetInCU: 0x8771, offset: 0x3344C, size: 0x4, addend: 0x0, symName: __ZL9patches75, symObjAddr: 0x1A1724, symBinAddr: 0x1A82C4, symSize: 0x0 }
  - { offsetInCU: 0x8788, offset: 0x33463, size: 0x4, addend: 0x0, symName: __ZL11patchBuf100, symObjAddr: 0x106E48, symBinAddr: 0x10DA70, symSize: 0x0 }
  - { offsetInCU: 0x879F, offset: 0x3347A, size: 0x4, addend: 0x0, symName: __ZL11platforms76, symObjAddr: 0x4A668, symBinAddr: 0x51290, symSize: 0x0 }
  - { offsetInCU: 0x87B6, offset: 0x33491, size: 0x4, addend: 0x0, symName: __ZL7file845, symObjAddr: 0x106E4C, symBinAddr: 0x10DA74, symSize: 0x0 }
  - { offsetInCU: 0x87CD, offset: 0x334A8, size: 0x4, addend: 0x0, symName: __ZL7file846, symObjAddr: 0x106F83, symBinAddr: 0x10DBAB, symSize: 0x0 }
  - { offsetInCU: 0x87E4, offset: 0x334BF, size: 0x4, addend: 0x0, symName: __ZL7file847, symObjAddr: 0x1070BF, symBinAddr: 0x10DCE7, symSize: 0x0 }
  - { offsetInCU: 0x87FB, offset: 0x334D6, size: 0x4, addend: 0x0, symName: __ZL9layouts76, symObjAddr: 0x4A6A4, symBinAddr: 0x512CC, symSize: 0x0 }
  - { offsetInCU: 0x8812, offset: 0x334ED, size: 0x4, addend: 0x0, symName: __ZL7file848, symObjAddr: 0x1071FA, symBinAddr: 0x10DE22, symSize: 0x0 }
  - { offsetInCU: 0x8836, offset: 0x33511, size: 0x4, addend: 0x0, symName: __ZL7file849, symObjAddr: 0x1078BD, symBinAddr: 0x10E4E5, symSize: 0x0 }
  - { offsetInCU: 0x885A, offset: 0x33535, size: 0x4, addend: 0x0, symName: __ZL7file850, symObjAddr: 0x108899, symBinAddr: 0x10F4C1, symSize: 0x0 }
  - { offsetInCU: 0x887E, offset: 0x33559, size: 0x4, addend: 0x0, symName: __ZL9patches76, symObjAddr: 0x1A17E8, symBinAddr: 0x1A8388, symSize: 0x0 }
  - { offsetInCU: 0x8895, offset: 0x33570, size: 0x4, addend: 0x0, symName: __ZL11patchBuf101, symObjAddr: 0x109873, symBinAddr: 0x11049B, symSize: 0x0 }
  - { offsetInCU: 0x88AC, offset: 0x33587, size: 0x4, addend: 0x0, symName: __ZL11platforms77, symObjAddr: 0x4A6E0, symBinAddr: 0x51308, symSize: 0x0 }
  - { offsetInCU: 0x88C3, offset: 0x3359E, size: 0x4, addend: 0x0, symName: __ZL7file851, symObjAddr: 0x109877, symBinAddr: 0x11049F, symSize: 0x0 }
  - { offsetInCU: 0x88DA, offset: 0x335B5, size: 0x4, addend: 0x0, symName: __ZL7file852, symObjAddr: 0x1099D7, symBinAddr: 0x1105FF, symSize: 0x0 }
  - { offsetInCU: 0x88F1, offset: 0x335CC, size: 0x4, addend: 0x0, symName: __ZL9layouts77, symObjAddr: 0x4A708, symBinAddr: 0x51330, symSize: 0x0 }
  - { offsetInCU: 0x8908, offset: 0x335E3, size: 0x4, addend: 0x0, symName: __ZL7file853, symObjAddr: 0x109B36, symBinAddr: 0x11075E, symSize: 0x0 }
  - { offsetInCU: 0x892C, offset: 0x33607, size: 0x4, addend: 0x0, symName: __ZL7file854, symObjAddr: 0x10A0CB, symBinAddr: 0x110CF3, symSize: 0x0 }
  - { offsetInCU: 0x8950, offset: 0x3362B, size: 0x4, addend: 0x0, symName: __ZL9patches77, symObjAddr: 0x1A1900, symBinAddr: 0x1A84A0, symSize: 0x0 }
  - { offsetInCU: 0x8967, offset: 0x33642, size: 0x4, addend: 0x0, symName: __ZL11patchBuf102, symObjAddr: 0x10A71B, symBinAddr: 0x111343, symSize: 0x0 }
  - { offsetInCU: 0x897E, offset: 0x33659, size: 0x4, addend: 0x0, symName: __ZL11platforms78, symObjAddr: 0x4A730, symBinAddr: 0x51358, symSize: 0x0 }
  - { offsetInCU: 0x8995, offset: 0x33670, size: 0x4, addend: 0x0, symName: __ZL7file855, symObjAddr: 0x10A71F, symBinAddr: 0x111347, symSize: 0x0 }
  - { offsetInCU: 0x89AC, offset: 0x33687, size: 0x4, addend: 0x0, symName: __ZL7file856, symObjAddr: 0x10A857, symBinAddr: 0x11147F, symSize: 0x0 }
  - { offsetInCU: 0x89C3, offset: 0x3369E, size: 0x4, addend: 0x0, symName: __ZL7file857, symObjAddr: 0x10A9A0, symBinAddr: 0x1115C8, symSize: 0x0 }
  - { offsetInCU: 0x89DA, offset: 0x336B5, size: 0x4, addend: 0x0, symName: __ZL7file858, symObjAddr: 0x10AB14, symBinAddr: 0x11173C, symSize: 0x0 }
  - { offsetInCU: 0x89F1, offset: 0x336CC, size: 0x4, addend: 0x0, symName: __ZL7file859, symObjAddr: 0x10AC4C, symBinAddr: 0x111874, symSize: 0x0 }
  - { offsetInCU: 0x8A08, offset: 0x336E3, size: 0x4, addend: 0x0, symName: __ZL9layouts78, symObjAddr: 0x4A794, symBinAddr: 0x513BC, symSize: 0x0 }
  - { offsetInCU: 0x8A1F, offset: 0x336FA, size: 0x4, addend: 0x0, symName: __ZL7file860, symObjAddr: 0x10AD84, symBinAddr: 0x1119AC, symSize: 0x0 }
  - { offsetInCU: 0x8A36, offset: 0x33711, size: 0x4, addend: 0x0, symName: __ZL7file861, symObjAddr: 0x10B3CA, symBinAddr: 0x111FF2, symSize: 0x0 }
  - { offsetInCU: 0x8A5A, offset: 0x33735, size: 0x4, addend: 0x0, symName: __ZL7file862, symObjAddr: 0x10BAEB, symBinAddr: 0x112713, symSize: 0x0 }
  - { offsetInCU: 0x8A7E, offset: 0x33759, size: 0x4, addend: 0x0, symName: __ZL7file863, symObjAddr: 0x10C3F7, symBinAddr: 0x11301F, symSize: 0x0 }
  - { offsetInCU: 0x8AA2, offset: 0x3377D, size: 0x4, addend: 0x0, symName: __ZL7file864, symObjAddr: 0x10CB19, symBinAddr: 0x113741, symSize: 0x0 }
  - { offsetInCU: 0x8AB9, offset: 0x33794, size: 0x4, addend: 0x0, symName: __ZL9patches78, symObjAddr: 0x1A19E0, symBinAddr: 0x1A8580, symSize: 0x0 }
  - { offsetInCU: 0x8AD0, offset: 0x337AB, size: 0x4, addend: 0x0, symName: __ZL11patchBuf103, symObjAddr: 0x10D15D, symBinAddr: 0x113D85, symSize: 0x0 }
  - { offsetInCU: 0x8AE7, offset: 0x337C2, size: 0x4, addend: 0x0, symName: __ZL11revisions32, symObjAddr: 0x4A7F8, symBinAddr: 0x51420, symSize: 0x0 }
  - { offsetInCU: 0x8AFE, offset: 0x337D9, size: 0x4, addend: 0x0, symName: __ZL11platforms79, symObjAddr: 0x4A800, symBinAddr: 0x51428, symSize: 0x0 }
  - { offsetInCU: 0x8B15, offset: 0x337F0, size: 0x4, addend: 0x0, symName: __ZL7file865, symObjAddr: 0x10D161, symBinAddr: 0x113D89, symSize: 0x0 }
  - { offsetInCU: 0x8B39, offset: 0x33814, size: 0x4, addend: 0x0, symName: __ZL7file866, symObjAddr: 0x10D6BD, symBinAddr: 0x1142E5, symSize: 0x0 }
  - { offsetInCU: 0x8B50, offset: 0x3382B, size: 0x4, addend: 0x0, symName: __ZL7file867, symObjAddr: 0x10D7FA, symBinAddr: 0x114422, symSize: 0x0 }
  - { offsetInCU: 0x8B74, offset: 0x3384F, size: 0x4, addend: 0x0, symName: __ZL7file868, symObjAddr: 0x10D914, symBinAddr: 0x11453C, symSize: 0x0 }
  - { offsetInCU: 0x8B8B, offset: 0x33866, size: 0x4, addend: 0x0, symName: __ZL7file869, symObjAddr: 0x10DA45, symBinAddr: 0x11466D, symSize: 0x0 }
  - { offsetInCU: 0x8BA2, offset: 0x3387D, size: 0x4, addend: 0x0, symName: __ZL7file870, symObjAddr: 0x10DB93, symBinAddr: 0x1147BB, symSize: 0x0 }
  - { offsetInCU: 0x8BB9, offset: 0x33894, size: 0x4, addend: 0x0, symName: __ZL7file871, symObjAddr: 0x10DCD2, symBinAddr: 0x1148FA, symSize: 0x0 }
  - { offsetInCU: 0x8BDD, offset: 0x338B8, size: 0x4, addend: 0x0, symName: __ZL7file872, symObjAddr: 0x10DE50, symBinAddr: 0x114A78, symSize: 0x0 }
  - { offsetInCU: 0x8BF4, offset: 0x338CF, size: 0x4, addend: 0x0, symName: __ZL7file873, symObjAddr: 0x10DFB0, symBinAddr: 0x114BD8, symSize: 0x0 }
  - { offsetInCU: 0x8C0B, offset: 0x338E6, size: 0x4, addend: 0x0, symName: __ZL7file874, symObjAddr: 0x10E0F1, symBinAddr: 0x114D19, symSize: 0x0 }
  - { offsetInCU: 0x8C22, offset: 0x338FD, size: 0x4, addend: 0x0, symName: __ZL7file875, symObjAddr: 0x10E23E, symBinAddr: 0x114E66, symSize: 0x0 }
  - { offsetInCU: 0x8C39, offset: 0x33914, size: 0x4, addend: 0x0, symName: __ZL9layouts79, symObjAddr: 0x4A8DC, symBinAddr: 0x51504, symSize: 0x0 }
  - { offsetInCU: 0x8C50, offset: 0x3392B, size: 0x4, addend: 0x0, symName: __ZL7file876, symObjAddr: 0x10E393, symBinAddr: 0x114FBB, symSize: 0x0 }
  - { offsetInCU: 0x8C74, offset: 0x3394F, size: 0x4, addend: 0x0, symName: __ZL7file877, symObjAddr: 0x10EA36, symBinAddr: 0x11565E, symSize: 0x0 }
  - { offsetInCU: 0x8C97, offset: 0x33972, size: 0x4, addend: 0x0, symName: __ZL7file878, symObjAddr: 0x10EB1D, symBinAddr: 0x115745, symSize: 0x0 }
  - { offsetInCU: 0x8CAE, offset: 0x33989, size: 0x4, addend: 0x0, symName: __ZL7file879, symObjAddr: 0x10EC06, symBinAddr: 0x11582E, symSize: 0x0 }
  - { offsetInCU: 0x8CC5, offset: 0x339A0, size: 0x4, addend: 0x0, symName: __ZL7file880, symObjAddr: 0x10ECED, symBinAddr: 0x115915, symSize: 0x0 }
  - { offsetInCU: 0x8CE8, offset: 0x339C3, size: 0x4, addend: 0x0, symName: __ZL7file881, symObjAddr: 0x10EDD8, symBinAddr: 0x115A00, symSize: 0x0 }
  - { offsetInCU: 0x8D0B, offset: 0x339E6, size: 0x4, addend: 0x0, symName: __ZL7file882, symObjAddr: 0x10EEAA, symBinAddr: 0x115AD2, symSize: 0x0 }
  - { offsetInCU: 0x8D22, offset: 0x339FD, size: 0x4, addend: 0x0, symName: __ZL7file883, symObjAddr: 0x10F013, symBinAddr: 0x115C3B, symSize: 0x0 }
  - { offsetInCU: 0x8D39, offset: 0x33A14, size: 0x4, addend: 0x0, symName: __ZL7file884, symObjAddr: 0x10F17B, symBinAddr: 0x115DA3, symSize: 0x0 }
  - { offsetInCU: 0x8D5C, offset: 0x33A37, size: 0x4, addend: 0x0, symName: __ZL7file885, symObjAddr: 0x10F25F, symBinAddr: 0x115E87, symSize: 0x0 }
  - { offsetInCU: 0x8D80, offset: 0x33A5B, size: 0x4, addend: 0x0, symName: __ZL7file886, symObjAddr: 0x10F8F8, symBinAddr: 0x116520, symSize: 0x0 }
  - { offsetInCU: 0x8D97, offset: 0x33A72, size: 0x4, addend: 0x0, symName: __ZL9patches79, symObjAddr: 0x1A1AA4, symBinAddr: 0x1A8644, symSize: 0x0 }
  - { offsetInCU: 0x8DAE, offset: 0x33A89, size: 0x4, addend: 0x0, symName: __ZL11patchBuf104, symObjAddr: 0x10FA63, symBinAddr: 0x11668B, symSize: 0x0 }
  - { offsetInCU: 0x8DC5, offset: 0x33AA0, size: 0x4, addend: 0x0, symName: __ZL11revisions33, symObjAddr: 0x4A9B8, symBinAddr: 0x515E0, symSize: 0x0 }
  - { offsetInCU: 0x8DDC, offset: 0x33AB7, size: 0x4, addend: 0x0, symName: __ZL11platforms80, symObjAddr: 0x4A9BC, symBinAddr: 0x515E4, symSize: 0x0 }
  - { offsetInCU: 0x8DF3, offset: 0x33ACE, size: 0x4, addend: 0x0, symName: __ZL7file887, symObjAddr: 0x10FA67, symBinAddr: 0x11668F, symSize: 0x0 }
  - { offsetInCU: 0x8E0A, offset: 0x33AE5, size: 0x4, addend: 0x0, symName: __ZL7file888, symObjAddr: 0x10FBB2, symBinAddr: 0x1167DA, symSize: 0x0 }
  - { offsetInCU: 0x8E21, offset: 0x33AFC, size: 0x4, addend: 0x0, symName: __ZL9layouts80, symObjAddr: 0x4A9E4, symBinAddr: 0x5160C, symSize: 0x0 }
  - { offsetInCU: 0x8E38, offset: 0x33B13, size: 0x4, addend: 0x0, symName: __ZL7file889, symObjAddr: 0x10FCFD, symBinAddr: 0x116925, symSize: 0x0 }
  - { offsetInCU: 0x8E4F, offset: 0x33B2A, size: 0x4, addend: 0x0, symName: __ZL7file890, symObjAddr: 0x11031F, symBinAddr: 0x116F47, symSize: 0x0 }
  - { offsetInCU: 0x8E66, offset: 0x33B41, size: 0x4, addend: 0x0, symName: __ZL9patches80, symObjAddr: 0x1A1B68, symBinAddr: 0x1A8708, symSize: 0x0 }
  - { offsetInCU: 0x8E7D, offset: 0x33B58, size: 0x4, addend: 0x0, symName: __ZL11patchBuf105, symObjAddr: 0x110942, symBinAddr: 0x11756A, symSize: 0x0 }
  - { offsetInCU: 0x8E94, offset: 0x33B6F, size: 0x4, addend: 0x0, symName: __ZL11platforms81, symObjAddr: 0x4AA0C, symBinAddr: 0x51634, symSize: 0x0 }
  - { offsetInCU: 0x8EB7, offset: 0x33B92, size: 0x4, addend: 0x0, symName: __ZL7file891, symObjAddr: 0x110946, symBinAddr: 0x11756E, symSize: 0x0 }
  - { offsetInCU: 0x8ECE, offset: 0x33BA9, size: 0x4, addend: 0x0, symName: __ZL7file892, symObjAddr: 0x110A7F, symBinAddr: 0x1176A7, symSize: 0x0 }
  - { offsetInCU: 0x8EE5, offset: 0x33BC0, size: 0x4, addend: 0x0, symName: __ZL7file893, symObjAddr: 0x110BDC, symBinAddr: 0x117804, symSize: 0x0 }
  - { offsetInCU: 0x8EFC, offset: 0x33BD7, size: 0x4, addend: 0x0, symName: __ZL7file894, symObjAddr: 0x110D51, symBinAddr: 0x117979, symSize: 0x0 }
  - { offsetInCU: 0x8F13, offset: 0x33BEE, size: 0x4, addend: 0x0, symName: __ZL7file895, symObjAddr: 0x110EA1, symBinAddr: 0x117AC9, symSize: 0x0 }
  - { offsetInCU: 0x8F2A, offset: 0x33C05, size: 0x4, addend: 0x0, symName: __ZL7file896, symObjAddr: 0x11101E, symBinAddr: 0x117C46, symSize: 0x0 }
  - { offsetInCU: 0x8F41, offset: 0x33C1C, size: 0x4, addend: 0x0, symName: __ZL7file897, symObjAddr: 0x11119B, symBinAddr: 0x117DC3, symSize: 0x0 }
  - { offsetInCU: 0x8F58, offset: 0x33C33, size: 0x4, addend: 0x0, symName: __ZL7file898, symObjAddr: 0x111304, symBinAddr: 0x117F2C, symSize: 0x0 }
  - { offsetInCU: 0x8F6F, offset: 0x33C4A, size: 0x4, addend: 0x0, symName: __ZL7file899, symObjAddr: 0x111496, symBinAddr: 0x1180BE, symSize: 0x0 }
  - { offsetInCU: 0x8F86, offset: 0x33C61, size: 0x4, addend: 0x0, symName: __ZL7file900, symObjAddr: 0x11161A, symBinAddr: 0x118242, symSize: 0x0 }
  - { offsetInCU: 0x8F9D, offset: 0x33C78, size: 0x4, addend: 0x0, symName: __ZL7file901, symObjAddr: 0x11176F, symBinAddr: 0x118397, symSize: 0x0 }
  - { offsetInCU: 0x8FC1, offset: 0x33C9C, size: 0x4, addend: 0x0, symName: __ZL7file902, symObjAddr: 0x1118FA, symBinAddr: 0x118522, symSize: 0x0 }
  - { offsetInCU: 0x8FD8, offset: 0x33CB3, size: 0x4, addend: 0x0, symName: __ZL7file903, symObjAddr: 0x111A49, symBinAddr: 0x118671, symSize: 0x0 }
  - { offsetInCU: 0x8FEF, offset: 0x33CCA, size: 0x4, addend: 0x0, symName: __ZL7file904, symObjAddr: 0x111BAE, symBinAddr: 0x1187D6, symSize: 0x0 }
  - { offsetInCU: 0x9013, offset: 0x33CEE, size: 0x4, addend: 0x0, symName: __ZL7file905, symObjAddr: 0x112FD8, symBinAddr: 0x119C00, symSize: 0x0 }
  - { offsetInCU: 0x902A, offset: 0x33D05, size: 0x4, addend: 0x0, symName: __ZL7file906, symObjAddr: 0x113147, symBinAddr: 0x119D6F, symSize: 0x0 }
  - { offsetInCU: 0x9041, offset: 0x33D1C, size: 0x4, addend: 0x0, symName: __ZL7file907, symObjAddr: 0x1132B7, symBinAddr: 0x119EDF, symSize: 0x0 }
  - { offsetInCU: 0x9058, offset: 0x33D33, size: 0x4, addend: 0x0, symName: __ZL7file908, symObjAddr: 0x113424, symBinAddr: 0x11A04C, symSize: 0x0 }
  - { offsetInCU: 0x906F, offset: 0x33D4A, size: 0x4, addend: 0x0, symName: __ZL7file909, symObjAddr: 0x113598, symBinAddr: 0x11A1C0, symSize: 0x0 }
  - { offsetInCU: 0x9086, offset: 0x33D61, size: 0x4, addend: 0x0, symName: __ZL7file910, symObjAddr: 0x113703, symBinAddr: 0x11A32B, symSize: 0x0 }
  - { offsetInCU: 0x909D, offset: 0x33D78, size: 0x4, addend: 0x0, symName: __ZL7file911, symObjAddr: 0x113875, symBinAddr: 0x11A49D, symSize: 0x0 }
  - { offsetInCU: 0x90B4, offset: 0x33D8F, size: 0x4, addend: 0x0, symName: __ZL7file912, symObjAddr: 0x1139FA, symBinAddr: 0x11A622, symSize: 0x0 }
  - { offsetInCU: 0x90CB, offset: 0x33DA6, size: 0x4, addend: 0x0, symName: __ZL7file913, symObjAddr: 0x113B5A, symBinAddr: 0x11A782, symSize: 0x0 }
  - { offsetInCU: 0x90EF, offset: 0x33DCA, size: 0x4, addend: 0x0, symName: __ZL7file914, symObjAddr: 0x113CAE, symBinAddr: 0x11A8D6, symSize: 0x0 }
  - { offsetInCU: 0x9106, offset: 0x33DE1, size: 0x4, addend: 0x0, symName: __ZL7file915, symObjAddr: 0x113E17, symBinAddr: 0x11AA3F, symSize: 0x0 }
  - { offsetInCU: 0x911D, offset: 0x33DF8, size: 0x4, addend: 0x0, symName: __ZL7file916, symObjAddr: 0x113F9E, symBinAddr: 0x11ABC6, symSize: 0x0 }
  - { offsetInCU: 0x9134, offset: 0x33E0F, size: 0x4, addend: 0x0, symName: __ZL9layouts81, symObjAddr: 0x4AC28, symBinAddr: 0x51850, symSize: 0x0 }
  - { offsetInCU: 0x914B, offset: 0x33E26, size: 0x4, addend: 0x0, symName: __ZL7file917, symObjAddr: 0x1140DC, symBinAddr: 0x11AD04, symSize: 0x0 }
  - { offsetInCU: 0x916F, offset: 0x33E4A, size: 0x4, addend: 0x0, symName: __ZL7file918, symObjAddr: 0x1146B9, symBinAddr: 0x11B2E1, symSize: 0x0 }
  - { offsetInCU: 0x9193, offset: 0x33E6E, size: 0x4, addend: 0x0, symName: __ZL7file919, symObjAddr: 0x114A2E, symBinAddr: 0x11B656, symSize: 0x0 }
  - { offsetInCU: 0x91B7, offset: 0x33E92, size: 0x4, addend: 0x0, symName: __ZL7file920, symObjAddr: 0x11542D, symBinAddr: 0x11C055, symSize: 0x0 }
  - { offsetInCU: 0x91CE, offset: 0x33EA9, size: 0x4, addend: 0x0, symName: __ZL7file921, symObjAddr: 0x1154E6, symBinAddr: 0x11C10E, symSize: 0x0 }
  - { offsetInCU: 0x91F2, offset: 0x33ECD, size: 0x4, addend: 0x0, symName: __ZL7file922, symObjAddr: 0x116238, symBinAddr: 0x11CE60, symSize: 0x0 }
  - { offsetInCU: 0x9216, offset: 0x33EF1, size: 0x4, addend: 0x0, symName: __ZL7file923, symObjAddr: 0x116FC4, symBinAddr: 0x11DBEC, symSize: 0x0 }
  - { offsetInCU: 0x923A, offset: 0x33F15, size: 0x4, addend: 0x0, symName: __ZL7file924, symObjAddr: 0x117D4A, symBinAddr: 0x11E972, symSize: 0x0 }
  - { offsetInCU: 0x9251, offset: 0x33F2C, size: 0x4, addend: 0x0, symName: __ZL7file925, symObjAddr: 0x118AD7, symBinAddr: 0x11F6FF, symSize: 0x0 }
  - { offsetInCU: 0x9275, offset: 0x33F50, size: 0x4, addend: 0x0, symName: __ZL7file926, symObjAddr: 0x1190CA, symBinAddr: 0x11FCF2, symSize: 0x0 }
  - { offsetInCU: 0x9299, offset: 0x33F74, size: 0x4, addend: 0x0, symName: __ZL7file927, symObjAddr: 0x11A2E1, symBinAddr: 0x120F09, symSize: 0x0 }
  - { offsetInCU: 0x92BD, offset: 0x33F98, size: 0x4, addend: 0x0, symName: __ZL7file928, symObjAddr: 0x11AC1E, symBinAddr: 0x121846, symSize: 0x0 }
  - { offsetInCU: 0x92E1, offset: 0x33FBC, size: 0x4, addend: 0x0, symName: __ZL7file929, symObjAddr: 0x11B54B, symBinAddr: 0x122173, symSize: 0x0 }
  - { offsetInCU: 0x9305, offset: 0x33FE0, size: 0x4, addend: 0x0, symName: __ZL7file930, symObjAddr: 0x11C75F, symBinAddr: 0x123387, symSize: 0x0 }
  - { offsetInCU: 0x9329, offset: 0x34004, size: 0x4, addend: 0x0, symName: __ZL7file931, symObjAddr: 0x11D4E9, symBinAddr: 0x124111, symSize: 0x0 }
  - { offsetInCU: 0x9340, offset: 0x3401B, size: 0x4, addend: 0x0, symName: __ZL7file932, symObjAddr: 0x11E2A9, symBinAddr: 0x124ED1, symSize: 0x0 }
  - { offsetInCU: 0x9364, offset: 0x3403F, size: 0x4, addend: 0x0, symName: __ZL7file933, symObjAddr: 0x11F3D2, symBinAddr: 0x125FFA, symSize: 0x0 }
  - { offsetInCU: 0x937B, offset: 0x34056, size: 0x4, addend: 0x0, symName: __ZL7file934, symObjAddr: 0x12015F, symBinAddr: 0x126D87, symSize: 0x0 }
  - { offsetInCU: 0x939E, offset: 0x34079, size: 0x4, addend: 0x0, symName: __ZL7file935, symObjAddr: 0x120242, symBinAddr: 0x126E6A, symSize: 0x0 }
  - { offsetInCU: 0x93C1, offset: 0x3409C, size: 0x4, addend: 0x0, symName: __ZL7file936, symObjAddr: 0x120334, symBinAddr: 0x126F5C, symSize: 0x0 }
  - { offsetInCU: 0x93E5, offset: 0x340C0, size: 0x4, addend: 0x0, symName: __ZL7file937, symObjAddr: 0x121489, symBinAddr: 0x1280B1, symSize: 0x0 }
  - { offsetInCU: 0x93FC, offset: 0x340D7, size: 0x4, addend: 0x0, symName: __ZL7file938, symObjAddr: 0x122216, symBinAddr: 0x128E3E, symSize: 0x0 }
  - { offsetInCU: 0x9420, offset: 0x340FB, size: 0x4, addend: 0x0, symName: __ZL7file939, symObjAddr: 0x122FB2, symBinAddr: 0x129BDA, symSize: 0x0 }
  - { offsetInCU: 0x9437, offset: 0x34112, size: 0x4, addend: 0x0, symName: __ZL7file940, symObjAddr: 0x123F7C, symBinAddr: 0x12ABA4, symSize: 0x0 }
  - { offsetInCU: 0x945B, offset: 0x34136, size: 0x4, addend: 0x0, symName: __ZL7file941, symObjAddr: 0x124D4D, symBinAddr: 0x12B975, symSize: 0x0 }
  - { offsetInCU: 0x947F, offset: 0x3415A, size: 0x4, addend: 0x0, symName: __ZL7file942, symObjAddr: 0x12527B, symBinAddr: 0x12BEA3, symSize: 0x0 }
  - { offsetInCU: 0x94A3, offset: 0x3417E, size: 0x4, addend: 0x0, symName: __ZL7file943, symObjAddr: 0x125BA7, symBinAddr: 0x12C7CF, symSize: 0x0 }
  - { offsetInCU: 0x94BA, offset: 0x34195, size: 0x4, addend: 0x0, symName: __ZL9patches81, symObjAddr: 0x1A1C48, symBinAddr: 0x1A87E8, symSize: 0x0 }
  - { offsetInCU: 0x94D1, offset: 0x341AC, size: 0x4, addend: 0x0, symName: __ZL11patchBuf106, symObjAddr: 0x125EF7, symBinAddr: 0x12CB1F, symSize: 0x0 }
  - { offsetInCU: 0x94E8, offset: 0x341C3, size: 0x4, addend: 0x0, symName: __ZL11platforms82, symObjAddr: 0x4AE44, symBinAddr: 0x51A6C, symSize: 0x0 }
  - { offsetInCU: 0x94FF, offset: 0x341DA, size: 0x4, addend: 0x0, symName: __ZL7file944, symObjAddr: 0x125EFB, symBinAddr: 0x12CB23, symSize: 0x0 }
  - { offsetInCU: 0x9523, offset: 0x341FE, size: 0x4, addend: 0x0, symName: __ZL9layouts82, symObjAddr: 0x4AE6C, symBinAddr: 0x51A94, symSize: 0x0 }
  - { offsetInCU: 0x953A, offset: 0x34215, size: 0x4, addend: 0x0, symName: __ZL7file945, symObjAddr: 0x126A72, symBinAddr: 0x12D69A, symSize: 0x0 }
  - { offsetInCU: 0x9551, offset: 0x3422C, size: 0x4, addend: 0x0, symName: __ZL7file946, symObjAddr: 0x127832, symBinAddr: 0x12E45A, symSize: 0x0 }
  - { offsetInCU: 0x9568, offset: 0x34243, size: 0x4, addend: 0x0, symName: __ZL9patches82, symObjAddr: 0x1A1D60, symBinAddr: 0x1A8900, symSize: 0x0 }
  - { offsetInCU: 0x957F, offset: 0x3425A, size: 0x4, addend: 0x0, symName: __ZL11patchBuf107, symObjAddr: 0x1285F2, symBinAddr: 0x12F21A, symSize: 0x0 }
  - { offsetInCU: 0x9596, offset: 0x34271, size: 0x4, addend: 0x0, symName: __ZL11revisions34, symObjAddr: 0x4AE94, symBinAddr: 0x51ABC, symSize: 0x0 }
  - { offsetInCU: 0x95AD, offset: 0x34288, size: 0x4, addend: 0x0, symName: __ZL11platforms83, symObjAddr: 0x4AE9C, symBinAddr: 0x51AC4, symSize: 0x0 }
  - { offsetInCU: 0x95C4, offset: 0x3429F, size: 0x4, addend: 0x0, symName: __ZL7file947, symObjAddr: 0x1285F6, symBinAddr: 0x12F21E, symSize: 0x0 }
  - { offsetInCU: 0x95E8, offset: 0x342C3, size: 0x4, addend: 0x0, symName: __ZL7file948, symObjAddr: 0x1287E7, symBinAddr: 0x12F40F, symSize: 0x0 }
  - { offsetInCU: 0x960C, offset: 0x342E7, size: 0x4, addend: 0x0, symName: __ZL7file949, symObjAddr: 0x1298EE, symBinAddr: 0x130516, symSize: 0x0 }
  - { offsetInCU: 0x9623, offset: 0x342FE, size: 0x4, addend: 0x0, symName: __ZL7file950, symObjAddr: 0x129A38, symBinAddr: 0x130660, symSize: 0x0 }
  - { offsetInCU: 0x963A, offset: 0x34315, size: 0x4, addend: 0x0, symName: __ZL7file951, symObjAddr: 0x129B69, symBinAddr: 0x130791, symSize: 0x0 }
  - { offsetInCU: 0x9651, offset: 0x3432C, size: 0x4, addend: 0x0, symName: __ZL7file952, symObjAddr: 0x129CB1, symBinAddr: 0x1308D9, symSize: 0x0 }
  - { offsetInCU: 0x9668, offset: 0x34343, size: 0x4, addend: 0x0, symName: __ZL7file953, symObjAddr: 0x129E05, symBinAddr: 0x130A2D, symSize: 0x0 }
  - { offsetInCU: 0x967F, offset: 0x3435A, size: 0x4, addend: 0x0, symName: __ZL7file954, symObjAddr: 0x12A34B, symBinAddr: 0x130F73, symSize: 0x0 }
  - { offsetInCU: 0x96A3, offset: 0x3437E, size: 0x4, addend: 0x0, symName: __ZL7file955, symObjAddr: 0x12A478, symBinAddr: 0x1310A0, symSize: 0x0 }
  - { offsetInCU: 0x96BA, offset: 0x34395, size: 0x4, addend: 0x0, symName: __ZL7file956, symObjAddr: 0x12A5C8, symBinAddr: 0x1311F0, symSize: 0x0 }
  - { offsetInCU: 0x96D1, offset: 0x343AC, size: 0x4, addend: 0x0, symName: __ZL9layouts83, symObjAddr: 0x4AF78, symBinAddr: 0x51BA0, symSize: 0x0 }
  - { offsetInCU: 0x96E8, offset: 0x343C3, size: 0x4, addend: 0x0, symName: __ZL7file957, symObjAddr: 0x12A716, symBinAddr: 0x13133E, symSize: 0x0 }
  - { offsetInCU: 0x970C, offset: 0x343E7, size: 0x4, addend: 0x0, symName: __ZL7file958, symObjAddr: 0x12AA2A, symBinAddr: 0x131652, symSize: 0x0 }
  - { offsetInCU: 0x9730, offset: 0x3440B, size: 0x4, addend: 0x0, symName: __ZL7file959, symObjAddr: 0x12AD3F, symBinAddr: 0x131967, symSize: 0x0 }
  - { offsetInCU: 0x9747, offset: 0x34422, size: 0x4, addend: 0x0, symName: __ZL7file960, symObjAddr: 0x12B03B, symBinAddr: 0x131C63, symSize: 0x0 }
  - { offsetInCU: 0x976B, offset: 0x34446, size: 0x4, addend: 0x0, symName: __ZL7file961, symObjAddr: 0x12B336, symBinAddr: 0x131F5E, symSize: 0x0 }
  - { offsetInCU: 0x978F, offset: 0x3446A, size: 0x4, addend: 0x0, symName: __ZL7file962, symObjAddr: 0x12B858, symBinAddr: 0x132480, symSize: 0x0 }
  - { offsetInCU: 0x97A6, offset: 0x34481, size: 0x4, addend: 0x0, symName: __ZL7file963, symObjAddr: 0x12BB89, symBinAddr: 0x1327B1, symSize: 0x0 }
  - { offsetInCU: 0x97BD, offset: 0x34498, size: 0x4, addend: 0x0, symName: __ZL7file964, symObjAddr: 0x12BEB7, symBinAddr: 0x132ADF, symSize: 0x0 }
  - { offsetInCU: 0x97D4, offset: 0x344AF, size: 0x4, addend: 0x0, symName: __ZL7file965, symObjAddr: 0x12C1E6, symBinAddr: 0x132E0E, symSize: 0x0 }
  - { offsetInCU: 0x97EB, offset: 0x344C6, size: 0x4, addend: 0x0, symName: __ZL7file966, symObjAddr: 0x12C515, symBinAddr: 0x13313D, symSize: 0x0 }
  - { offsetInCU: 0x980F, offset: 0x344EA, size: 0x4, addend: 0x0, symName: __ZL7file967, symObjAddr: 0x12C825, symBinAddr: 0x13344D, symSize: 0x0 }
  - { offsetInCU: 0x9826, offset: 0x34501, size: 0x4, addend: 0x0, symName: __ZL9patches83, symObjAddr: 0x1A1E5C, symBinAddr: 0x1A89FC, symSize: 0x0 }
  - { offsetInCU: 0x983D, offset: 0x34518, size: 0x4, addend: 0x0, symName: __ZL11patchBuf108, symObjAddr: 0x12CB54, symBinAddr: 0x13377C, symSize: 0x0 }
  - { offsetInCU: 0x9854, offset: 0x3452F, size: 0x4, addend: 0x0, symName: __ZL11platforms84, symObjAddr: 0x4B054, symBinAddr: 0x51C7C, symSize: 0x0 }
  - { offsetInCU: 0x986B, offset: 0x34546, size: 0x4, addend: 0x0, symName: __ZL7file968, symObjAddr: 0x12CB58, symBinAddr: 0x133780, symSize: 0x0 }
  - { offsetInCU: 0x9882, offset: 0x3455D, size: 0x4, addend: 0x0, symName: __ZL7file969, symObjAddr: 0x12CCB7, symBinAddr: 0x1338DF, symSize: 0x0 }
  - { offsetInCU: 0x9899, offset: 0x34574, size: 0x4, addend: 0x0, symName: __ZL9layouts84, symObjAddr: 0x4B07C, symBinAddr: 0x51CA4, symSize: 0x0 }
  - { offsetInCU: 0x98B0, offset: 0x3458B, size: 0x4, addend: 0x0, symName: __ZL7file970, symObjAddr: 0x12CE17, symBinAddr: 0x133A3F, symSize: 0x0 }
  - { offsetInCU: 0x98D4, offset: 0x345AF, size: 0x4, addend: 0x0, symName: __ZL7file971, symObjAddr: 0x12DDB4, symBinAddr: 0x1349DC, symSize: 0x0 }
  - { offsetInCU: 0x98F8, offset: 0x345D3, size: 0x4, addend: 0x0, symName: __ZL9patches84, symObjAddr: 0x1A1F3C, symBinAddr: 0x1A8ADC, symSize: 0x0 }
  - { offsetInCU: 0x990F, offset: 0x345EA, size: 0x4, addend: 0x0, symName: __ZL11patchBuf109, symObjAddr: 0x12ED5D, symBinAddr: 0x135985, symSize: 0x0 }
  - { offsetInCU: 0x9926, offset: 0x34601, size: 0x4, addend: 0x0, symName: __ZL11revisions35, symObjAddr: 0x4B0A4, symBinAddr: 0x51CCC, symSize: 0x0 }
  - { offsetInCU: 0x993D, offset: 0x34618, size: 0x4, addend: 0x0, symName: __ZL11platforms85, symObjAddr: 0x4B0A8, symBinAddr: 0x51CD0, symSize: 0x0 }
  - { offsetInCU: 0x9954, offset: 0x3462F, size: 0x4, addend: 0x0, symName: __ZL7file972, symObjAddr: 0x12ED61, symBinAddr: 0x135989, symSize: 0x0 }
  - { offsetInCU: 0x9978, offset: 0x34653, size: 0x4, addend: 0x0, symName: __ZL7file973, symObjAddr: 0x12EE95, symBinAddr: 0x135ABD, symSize: 0x0 }
  - { offsetInCU: 0x999C, offset: 0x34677, size: 0x4, addend: 0x0, symName: __ZL9layouts85, symObjAddr: 0x4B0D0, symBinAddr: 0x51CF8, symSize: 0x0 }
  - { offsetInCU: 0x99B3, offset: 0x3468E, size: 0x4, addend: 0x0, symName: __ZL7file974, symObjAddr: 0x12F9F4, symBinAddr: 0x13661C, symSize: 0x0 }
  - { offsetInCU: 0x99D7, offset: 0x346B2, size: 0x4, addend: 0x0, symName: __ZL7file975, symObjAddr: 0x12FD2D, symBinAddr: 0x136955, symSize: 0x0 }
  - { offsetInCU: 0x99EE, offset: 0x346C9, size: 0x4, addend: 0x0, symName: __ZL9patches85, symObjAddr: 0x1A2054, symBinAddr: 0x1A8BF4, symSize: 0x0 }
  - { offsetInCU: 0x9A05, offset: 0x346E0, size: 0x4, addend: 0x0, symName: __ZL11patchBuf110, symObjAddr: 0x1300A9, symBinAddr: 0x136CD1, symSize: 0x0 }
  - { offsetInCU: 0x9A1C, offset: 0x346F7, size: 0x4, addend: 0x0, symName: __ZL11revisions36, symObjAddr: 0x4B0F8, symBinAddr: 0x51D20, symSize: 0x0 }
  - { offsetInCU: 0x9A33, offset: 0x3470E, size: 0x4, addend: 0x0, symName: __ZL11platforms86, symObjAddr: 0x4B100, symBinAddr: 0x51D28, symSize: 0x0 }
  - { offsetInCU: 0x9A56, offset: 0x34731, size: 0x4, addend: 0x0, symName: __ZL7file976, symObjAddr: 0x1300AD, symBinAddr: 0x136CD5, symSize: 0x0 }
  - { offsetInCU: 0x9A6D, offset: 0x34748, size: 0x4, addend: 0x0, symName: __ZL7file977, symObjAddr: 0x1301F7, symBinAddr: 0x136E1F, symSize: 0x0 }
  - { offsetInCU: 0x9A84, offset: 0x3475F, size: 0x4, addend: 0x0, symName: __ZL7file978, symObjAddr: 0x13032F, symBinAddr: 0x136F57, symSize: 0x0 }
  - { offsetInCU: 0x9A9B, offset: 0x34776, size: 0x4, addend: 0x0, symName: __ZL7file979, symObjAddr: 0x1304B2, symBinAddr: 0x1370DA, symSize: 0x0 }
  - { offsetInCU: 0x9AB2, offset: 0x3478D, size: 0x4, addend: 0x0, symName: __ZL7file980, symObjAddr: 0x1305FB, symBinAddr: 0x137223, symSize: 0x0 }
  - { offsetInCU: 0x9AC9, offset: 0x347A4, size: 0x4, addend: 0x0, symName: __ZL7file981, symObjAddr: 0x130743, symBinAddr: 0x13736B, symSize: 0x0 }
  - { offsetInCU: 0x9AE0, offset: 0x347BB, size: 0x4, addend: 0x0, symName: __ZL7file982, symObjAddr: 0x13088B, symBinAddr: 0x1374B3, symSize: 0x0 }
  - { offsetInCU: 0x9AF7, offset: 0x347D2, size: 0x4, addend: 0x0, symName: __ZL7file983, symObjAddr: 0x1309D4, symBinAddr: 0x1375FC, symSize: 0x0 }
  - { offsetInCU: 0x9B0E, offset: 0x347E9, size: 0x4, addend: 0x0, symName: __ZL7file984, symObjAddr: 0x130B2F, symBinAddr: 0x137757, symSize: 0x0 }
  - { offsetInCU: 0x9B25, offset: 0x34800, size: 0x4, addend: 0x0, symName: __ZL7file985, symObjAddr: 0x130C66, symBinAddr: 0x13788E, symSize: 0x0 }
  - { offsetInCU: 0x9B3C, offset: 0x34817, size: 0x4, addend: 0x0, symName: __ZL7file986, symObjAddr: 0x130DAE, symBinAddr: 0x1379D6, symSize: 0x0 }
  - { offsetInCU: 0x9B53, offset: 0x3482E, size: 0x4, addend: 0x0, symName: __ZL7file987, symObjAddr: 0x130EF6, symBinAddr: 0x137B1E, symSize: 0x0 }
  - { offsetInCU: 0x9B6A, offset: 0x34845, size: 0x4, addend: 0x0, symName: __ZL7file988, symObjAddr: 0x13102D, symBinAddr: 0x137C55, symSize: 0x0 }
  - { offsetInCU: 0x9B81, offset: 0x3485C, size: 0x4, addend: 0x0, symName: __ZL7file989, symObjAddr: 0x1311B1, symBinAddr: 0x137DD9, symSize: 0x0 }
  - { offsetInCU: 0x9B98, offset: 0x34873, size: 0x4, addend: 0x0, symName: __ZL7file990, symObjAddr: 0x1312E9, symBinAddr: 0x137F11, symSize: 0x0 }
  - { offsetInCU: 0x9BAF, offset: 0x3488A, size: 0x4, addend: 0x0, symName: __ZL7file991, symObjAddr: 0x131462, symBinAddr: 0x13808A, symSize: 0x0 }
  - { offsetInCU: 0x9BC6, offset: 0x348A1, size: 0x4, addend: 0x0, symName: __ZL7file992, symObjAddr: 0x13159F, symBinAddr: 0x1381C7, symSize: 0x0 }
  - { offsetInCU: 0x9BDD, offset: 0x348B8, size: 0x4, addend: 0x0, symName: __ZL7file993, symObjAddr: 0x1316DC, symBinAddr: 0x138304, symSize: 0x0 }
  - { offsetInCU: 0x9BF4, offset: 0x348CF, size: 0x4, addend: 0x0, symName: __ZL7file994, symObjAddr: 0x131847, symBinAddr: 0x13846F, symSize: 0x0 }
  - { offsetInCU: 0x9C18, offset: 0x348F3, size: 0x4, addend: 0x0, symName: __ZL7file995, symObjAddr: 0x1319C2, symBinAddr: 0x1385EA, symSize: 0x0 }
  - { offsetInCU: 0x9C2F, offset: 0x3490A, size: 0x4, addend: 0x0, symName: __ZL9layouts86, symObjAddr: 0x4B290, symBinAddr: 0x51EB8, symSize: 0x0 }
  - { offsetInCU: 0x9C46, offset: 0x34921, size: 0x4, addend: 0x0, symName: __ZL7file996, symObjAddr: 0x131B4D, symBinAddr: 0x138775, symSize: 0x0 }
  - { offsetInCU: 0x9C5D, offset: 0x34938, size: 0x4, addend: 0x0, symName: __ZL7file997, symObjAddr: 0x13290C, symBinAddr: 0x139534, symSize: 0x0 }
  - { offsetInCU: 0x9C74, offset: 0x3494F, size: 0x4, addend: 0x0, symName: __ZL7file998, symObjAddr: 0x132F2E, symBinAddr: 0x139B56, symSize: 0x0 }
  - { offsetInCU: 0x9C98, offset: 0x34973, size: 0x4, addend: 0x0, symName: __ZL7file999, symObjAddr: 0x1334DF, symBinAddr: 0x13A107, symSize: 0x0 }
  - { offsetInCU: 0x9CAF, offset: 0x3498A, size: 0x4, addend: 0x0, symName: __ZL8file1000, symObjAddr: 0x1342A5, symBinAddr: 0x13AECD, symSize: 0x0 }
  - { offsetInCU: 0x9CC6, offset: 0x349A1, size: 0x4, addend: 0x0, symName: __ZL8file1001, symObjAddr: 0x135065, symBinAddr: 0x13BC8D, symSize: 0x0 }
  - { offsetInCU: 0x9CEA, offset: 0x349C5, size: 0x4, addend: 0x0, symName: __ZL8file1002, symObjAddr: 0x135673, symBinAddr: 0x13C29B, symSize: 0x0 }
  - { offsetInCU: 0x9D01, offset: 0x349DC, size: 0x4, addend: 0x0, symName: __ZL8file1003, symObjAddr: 0x136435, symBinAddr: 0x13D05D, symSize: 0x0 }
  - { offsetInCU: 0x9D18, offset: 0x349F3, size: 0x4, addend: 0x0, symName: __ZL8file1004, symObjAddr: 0x1371F5, symBinAddr: 0x13DE1D, symSize: 0x0 }
  - { offsetInCU: 0x9D3C, offset: 0x34A17, size: 0x4, addend: 0x0, symName: __ZL8file1005, symObjAddr: 0x137F6C, symBinAddr: 0x13EB94, symSize: 0x0 }
  - { offsetInCU: 0x9D60, offset: 0x34A3B, size: 0x4, addend: 0x0, symName: __ZL8file1006, symObjAddr: 0x138D29, symBinAddr: 0x13F951, symSize: 0x0 }
  - { offsetInCU: 0x9D77, offset: 0x34A52, size: 0x4, addend: 0x0, symName: __ZL8file1007, symObjAddr: 0x139AEB, symBinAddr: 0x140713, symSize: 0x0 }
  - { offsetInCU: 0x9D9B, offset: 0x34A76, size: 0x4, addend: 0x0, symName: __ZL8file1008, symObjAddr: 0x13A857, symBinAddr: 0x14147F, symSize: 0x0 }
  - { offsetInCU: 0x9DBF, offset: 0x34A9A, size: 0x4, addend: 0x0, symName: __ZL8file1009, symObjAddr: 0x13B61E, symBinAddr: 0x142246, symSize: 0x0 }
  - { offsetInCU: 0x9DD6, offset: 0x34AB1, size: 0x4, addend: 0x0, symName: __ZL8file1010, symObjAddr: 0x13BC43, symBinAddr: 0x14286B, symSize: 0x0 }
  - { offsetInCU: 0x9DED, offset: 0x34AC8, size: 0x4, addend: 0x0, symName: __ZL8file1011, symObjAddr: 0x13CA08, symBinAddr: 0x143630, symSize: 0x0 }
  - { offsetInCU: 0x9E04, offset: 0x34ADF, size: 0x4, addend: 0x0, symName: __ZL8file1012, symObjAddr: 0x13D029, symBinAddr: 0x143C51, symSize: 0x0 }
  - { offsetInCU: 0x9E28, offset: 0x34B03, size: 0x4, addend: 0x0, symName: __ZL8file1013, symObjAddr: 0x13D5AD, symBinAddr: 0x1441D5, symSize: 0x0 }
  - { offsetInCU: 0x9E4C, offset: 0x34B27, size: 0x4, addend: 0x0, symName: __ZL8file1014, symObjAddr: 0x13E468, symBinAddr: 0x145090, symSize: 0x0 }
  - { offsetInCU: 0x9E63, offset: 0x34B3E, size: 0x4, addend: 0x0, symName: __ZL8file1015, symObjAddr: 0x13F22E, symBinAddr: 0x145E56, symSize: 0x0 }
  - { offsetInCU: 0x9E87, offset: 0x34B62, size: 0x4, addend: 0x0, symName: __ZL9patches86, symObjAddr: 0x1A2118, symBinAddr: 0x1A8CB8, symSize: 0x0 }
  - { offsetInCU: 0x9E9E, offset: 0x34B79, size: 0x4, addend: 0x0, symName: __ZL11patchBuf111, symObjAddr: 0x13F9E7, symBinAddr: 0x14660F, symSize: 0x0 }
  - { offsetInCU: 0x9EB5, offset: 0x34B90, size: 0x4, addend: 0x0, symName: __ZL11revisions37, symObjAddr: 0x4B420, symBinAddr: 0x52048, symSize: 0x0 }
  - { offsetInCU: 0x9ECC, offset: 0x34BA7, size: 0x4, addend: 0x0, symName: __ZL11platforms87, symObjAddr: 0x4B424, symBinAddr: 0x5204C, symSize: 0x0 }
  - { offsetInCU: 0x9EEF, offset: 0x34BCA, size: 0x4, addend: 0x0, symName: __ZL8file1016, symObjAddr: 0x13F9EB, symBinAddr: 0x146613, symSize: 0x0 }
  - { offsetInCU: 0x9F06, offset: 0x34BE1, size: 0x4, addend: 0x0, symName: __ZL8file1017, symObjAddr: 0x13FDF3, symBinAddr: 0x146A1B, symSize: 0x0 }
  - { offsetInCU: 0x9F1D, offset: 0x34BF8, size: 0x4, addend: 0x0, symName: __ZL8file1018, symObjAddr: 0x13FFB2, symBinAddr: 0x146BDA, symSize: 0x0 }
  - { offsetInCU: 0x9F41, offset: 0x34C1C, size: 0x4, addend: 0x0, symName: __ZL8file1019, symObjAddr: 0x140E47, symBinAddr: 0x147A6F, symSize: 0x0 }
  - { offsetInCU: 0x9F58, offset: 0x34C33, size: 0x4, addend: 0x0, symName: __ZL8file1020, symObjAddr: 0x1411DC, symBinAddr: 0x147E04, symSize: 0x0 }
  - { offsetInCU: 0x9F7C, offset: 0x34C57, size: 0x4, addend: 0x0, symName: __ZL8file1021, symObjAddr: 0x141CFD, symBinAddr: 0x148925, symSize: 0x0 }
  - { offsetInCU: 0x9F93, offset: 0x34C6E, size: 0x4, addend: 0x0, symName: __ZL8file1022, symObjAddr: 0x141E67, symBinAddr: 0x148A8F, symSize: 0x0 }
  - { offsetInCU: 0x9FAA, offset: 0x34C85, size: 0x4, addend: 0x0, symName: __ZL8file1023, symObjAddr: 0x141FD4, symBinAddr: 0x148BFC, symSize: 0x0 }
  - { offsetInCU: 0x9FC1, offset: 0x34C9C, size: 0x4, addend: 0x0, symName: __ZL8file1024, symObjAddr: 0x142147, symBinAddr: 0x148D6F, symSize: 0x0 }
  - { offsetInCU: 0x9FD8, offset: 0x34CB3, size: 0x4, addend: 0x0, symName: __ZL8file1025, symObjAddr: 0x142294, symBinAddr: 0x148EBC, symSize: 0x0 }
  - { offsetInCU: 0x9FEF, offset: 0x34CCA, size: 0x4, addend: 0x0, symName: __ZL9layouts87, symObjAddr: 0x4B528, symBinAddr: 0x52150, symSize: 0x0 }
  - { offsetInCU: 0xA006, offset: 0x34CE1, size: 0x4, addend: 0x0, symName: __ZL8file1026, symObjAddr: 0x1423F5, symBinAddr: 0x14901D, symSize: 0x0 }
  - { offsetInCU: 0xA01D, offset: 0x34CF8, size: 0x4, addend: 0x0, symName: __ZL8file1027, symObjAddr: 0x142745, symBinAddr: 0x14936D, symSize: 0x0 }
  - { offsetInCU: 0xA034, offset: 0x34D0F, size: 0x4, addend: 0x0, symName: __ZL8file1028, symObjAddr: 0x142A94, symBinAddr: 0x1496BC, symSize: 0x0 }
  - { offsetInCU: 0xA04B, offset: 0x34D26, size: 0x4, addend: 0x0, symName: __ZL8file1029, symObjAddr: 0x142DB0, symBinAddr: 0x1499D8, symSize: 0x0 }
  - { offsetInCU: 0xA062, offset: 0x34D3D, size: 0x4, addend: 0x0, symName: __ZL8file1030, symObjAddr: 0x1430FE, symBinAddr: 0x149D26, symSize: 0x0 }
  - { offsetInCU: 0xA079, offset: 0x34D54, size: 0x4, addend: 0x0, symName: __ZL8file1031, symObjAddr: 0x14344D, symBinAddr: 0x14A075, symSize: 0x0 }
  - { offsetInCU: 0xA09D, offset: 0x34D78, size: 0x4, addend: 0x0, symName: __ZL8file1032, symObjAddr: 0x1447B4, symBinAddr: 0x14B3DC, symSize: 0x0 }
  - { offsetInCU: 0xA0B4, offset: 0x34D8F, size: 0x4, addend: 0x0, symName: __ZL8file1033, symObjAddr: 0x144C16, symBinAddr: 0x14B83E, symSize: 0x0 }
  - { offsetInCU: 0xA0D8, offset: 0x34DB3, size: 0x4, addend: 0x0, symName: __ZL8file1034, symObjAddr: 0x144F8A, symBinAddr: 0x14BBB2, symSize: 0x0 }
  - { offsetInCU: 0xA0EF, offset: 0x34DCA, size: 0x4, addend: 0x0, symName: __ZL8file1035, symObjAddr: 0x1452FE, symBinAddr: 0x14BF26, symSize: 0x0 }
  - { offsetInCU: 0xA113, offset: 0x34DEE, size: 0x4, addend: 0x0, symName: __ZL8file1036, symObjAddr: 0x1457CD, symBinAddr: 0x14C3F5, symSize: 0x0 }
  - { offsetInCU: 0xA137, offset: 0x34E12, size: 0x4, addend: 0x0, symName: __ZL8file1037, symObjAddr: 0x145C2B, symBinAddr: 0x14C853, symSize: 0x0 }
  - { offsetInCU: 0xA14E, offset: 0x34E29, size: 0x4, addend: 0x0, symName: __ZL9patches87, symObjAddr: 0x1A21F8, symBinAddr: 0x1A8D98, symSize: 0x0 }
  - { offsetInCU: 0xA165, offset: 0x34E40, size: 0x4, addend: 0x0, symName: __ZL11patchBuf112, symObjAddr: 0x145F7C, symBinAddr: 0x14CBA4, symSize: 0x0 }
  - { offsetInCU: 0xA17C, offset: 0x34E57, size: 0x4, addend: 0x0, symName: __ZL11revisions38, symObjAddr: 0x4B62C, symBinAddr: 0x52254, symSize: 0x0 }
  - { offsetInCU: 0xA193, offset: 0x34E6E, size: 0x4, addend: 0x0, symName: __ZL11platforms88, symObjAddr: 0x4B638, symBinAddr: 0x52260, symSize: 0x0 }
  - { offsetInCU: 0xA1AA, offset: 0x34E85, size: 0x4, addend: 0x0, symName: __ZL8file1038, symObjAddr: 0x145F80, symBinAddr: 0x14CBA8, symSize: 0x0 }
  - { offsetInCU: 0xA1C1, offset: 0x34E9C, size: 0x4, addend: 0x0, symName: __ZL8file1039, symObjAddr: 0x1460CD, symBinAddr: 0x14CCF5, symSize: 0x0 }
  - { offsetInCU: 0xA1D8, offset: 0x34EB3, size: 0x4, addend: 0x0, symName: __ZL8file1040, symObjAddr: 0x146216, symBinAddr: 0x14CE3E, symSize: 0x0 }
  - { offsetInCU: 0xA1EF, offset: 0x34ECA, size: 0x4, addend: 0x0, symName: __ZL8file1041, symObjAddr: 0x146361, symBinAddr: 0x14CF89, symSize: 0x0 }
  - { offsetInCU: 0xA206, offset: 0x34EE1, size: 0x4, addend: 0x0, symName: __ZL8file1042, symObjAddr: 0x1464B4, symBinAddr: 0x14D0DC, symSize: 0x0 }
  - { offsetInCU: 0xA21D, offset: 0x34EF8, size: 0x4, addend: 0x0, symName: __ZL8file1043, symObjAddr: 0x1465F6, symBinAddr: 0x14D21E, symSize: 0x0 }
  - { offsetInCU: 0xA234, offset: 0x34F0F, size: 0x4, addend: 0x0, symName: __ZL8file1044, symObjAddr: 0x14673C, symBinAddr: 0x14D364, symSize: 0x0 }
  - { offsetInCU: 0xA24B, offset: 0x34F26, size: 0x4, addend: 0x0, symName: __ZL9layouts88, symObjAddr: 0x4B6C4, symBinAddr: 0x522EC, symSize: 0x0 }
  - { offsetInCU: 0xA262, offset: 0x34F3D, size: 0x4, addend: 0x0, symName: __ZL8file1045, symObjAddr: 0x14688F, symBinAddr: 0x14D4B7, symSize: 0x0 }
  - { offsetInCU: 0xA279, offset: 0x34F54, size: 0x4, addend: 0x0, symName: __ZL8file1046, symObjAddr: 0x146F45, symBinAddr: 0x14DB6D, symSize: 0x0 }
  - { offsetInCU: 0xA29D, offset: 0x34F78, size: 0x4, addend: 0x0, symName: __ZL8file1047, symObjAddr: 0x14760B, symBinAddr: 0x14E233, symSize: 0x0 }
  - { offsetInCU: 0xA2C0, offset: 0x34F9B, size: 0x4, addend: 0x0, symName: __ZL8file1048, symObjAddr: 0x1476FA, symBinAddr: 0x14E322, symSize: 0x0 }
  - { offsetInCU: 0xA2D7, offset: 0x34FB2, size: 0x4, addend: 0x0, symName: __ZL8file1049, symObjAddr: 0x147A29, symBinAddr: 0x14E651, symSize: 0x0 }
  - { offsetInCU: 0xA2FB, offset: 0x34FD6, size: 0x4, addend: 0x0, symName: __ZL8file1050, symObjAddr: 0x147EE4, symBinAddr: 0x14EB0C, symSize: 0x0 }
  - { offsetInCU: 0xA31F, offset: 0x34FFA, size: 0x4, addend: 0x0, symName: __ZL8file1051, symObjAddr: 0x1483CA, symBinAddr: 0x14EFF2, symSize: 0x0 }
  - { offsetInCU: 0xA336, offset: 0x35011, size: 0x4, addend: 0x0, symName: __ZL9patches88, symObjAddr: 0x1A22BC, symBinAddr: 0x1A8E5C, symSize: 0x0 }
  - { offsetInCU: 0xA34D, offset: 0x35028, size: 0x4, addend: 0x0, symName: __ZL11revisions39, symObjAddr: 0x4B750, symBinAddr: 0x52378, symSize: 0x0 }
  - { offsetInCU: 0xA364, offset: 0x3503F, size: 0x4, addend: 0x0, symName: __ZL11platforms89, symObjAddr: 0x4B758, symBinAddr: 0x52380, symSize: 0x0 }
  - { offsetInCU: 0xA387, offset: 0x35062, size: 0x4, addend: 0x0, symName: __ZL8file1052, symObjAddr: 0x148A80, symBinAddr: 0x14F6A8, symSize: 0x0 }
  - { offsetInCU: 0xA39E, offset: 0x35079, size: 0x4, addend: 0x0, symName: __ZL8file1053, symObjAddr: 0x148BC3, symBinAddr: 0x14F7EB, symSize: 0x0 }
  - { offsetInCU: 0xA3C2, offset: 0x3509D, size: 0x4, addend: 0x0, symName: __ZL8file1054, symObjAddr: 0x148CF3, symBinAddr: 0x14F91B, symSize: 0x0 }
  - { offsetInCU: 0xA3D9, offset: 0x350B4, size: 0x4, addend: 0x0, symName: __ZL8file1055, symObjAddr: 0x14981F, symBinAddr: 0x150447, symSize: 0x0 }
  - { offsetInCU: 0xA3F0, offset: 0x350CB, size: 0x4, addend: 0x0, symName: __ZL8file1056, symObjAddr: 0x14A34B, symBinAddr: 0x150F73, symSize: 0x0 }
  - { offsetInCU: 0xA407, offset: 0x350E2, size: 0x4, addend: 0x0, symName: __ZL8file1057, symObjAddr: 0x14A47F, symBinAddr: 0x1510A7, symSize: 0x0 }
  - { offsetInCU: 0xA41E, offset: 0x350F9, size: 0x4, addend: 0x0, symName: __ZL8file1058, symObjAddr: 0x14A5B3, symBinAddr: 0x1511DB, symSize: 0x0 }
  - { offsetInCU: 0xA435, offset: 0x35110, size: 0x4, addend: 0x0, symName: __ZL8file1059, symObjAddr: 0x14A6EC, symBinAddr: 0x151314, symSize: 0x0 }
  - { offsetInCU: 0xA44C, offset: 0x35127, size: 0x4, addend: 0x0, symName: __ZL8file1060, symObjAddr: 0x14A84B, symBinAddr: 0x151473, symSize: 0x0 }
  - { offsetInCU: 0xA463, offset: 0x3513E, size: 0x4, addend: 0x0, symName: __ZL8file1061, symObjAddr: 0x14A98E, symBinAddr: 0x1515B6, symSize: 0x0 }
  - { offsetInCU: 0xA47A, offset: 0x35155, size: 0x4, addend: 0x0, symName: __ZL8file1062, symObjAddr: 0x14AACB, symBinAddr: 0x1516F3, symSize: 0x0 }
  - { offsetInCU: 0xA491, offset: 0x3516C, size: 0x4, addend: 0x0, symName: __ZL8file1063, symObjAddr: 0x14AC0E, symBinAddr: 0x151836, symSize: 0x0 }
  - { offsetInCU: 0xA4A8, offset: 0x35183, size: 0x4, addend: 0x0, symName: __ZL8file1064, symObjAddr: 0x14AD41, symBinAddr: 0x151969, symSize: 0x0 }
  - { offsetInCU: 0xA4BF, offset: 0x3519A, size: 0x4, addend: 0x0, symName: __ZL8file1065, symObjAddr: 0x14AE84, symBinAddr: 0x151AAC, symSize: 0x0 }
  - { offsetInCU: 0xA4D6, offset: 0x351B1, size: 0x4, addend: 0x0, symName: __ZL8file1066, symObjAddr: 0x14AFD0, symBinAddr: 0x151BF8, symSize: 0x0 }
  - { offsetInCU: 0xA4ED, offset: 0x351C8, size: 0x4, addend: 0x0, symName: __ZL8file1067, symObjAddr: 0x14B12D, symBinAddr: 0x151D55, symSize: 0x0 }
  - { offsetInCU: 0xA504, offset: 0x351DF, size: 0x4, addend: 0x0, symName: __ZL8file1068, symObjAddr: 0x14B296, symBinAddr: 0x151EBE, symSize: 0x0 }
  - { offsetInCU: 0xA51B, offset: 0x351F6, size: 0x4, addend: 0x0, symName: __ZL9layouts89, symObjAddr: 0x4B8AC, symBinAddr: 0x524D4, symSize: 0x0 }
  - { offsetInCU: 0xA532, offset: 0x3520D, size: 0x4, addend: 0x0, symName: __ZL8file1069, symObjAddr: 0x14B3D9, symBinAddr: 0x152001, symSize: 0x0 }
  - { offsetInCU: 0xA556, offset: 0x35231, size: 0x4, addend: 0x0, symName: __ZL8file1070, symObjAddr: 0x14BA64, symBinAddr: 0x15268C, symSize: 0x0 }
  - { offsetInCU: 0xA57A, offset: 0x35255, size: 0x4, addend: 0x0, symName: __ZL8file1071, symObjAddr: 0x14BD36, symBinAddr: 0x15295E, symSize: 0x0 }
  - { offsetInCU: 0xA59E, offset: 0x35279, size: 0x4, addend: 0x0, symName: __ZL8file1072, symObjAddr: 0x14CD06, symBinAddr: 0x15392E, symSize: 0x0 }
  - { offsetInCU: 0xA5B5, offset: 0x35290, size: 0x4, addend: 0x0, symName: __ZL8file1073, symObjAddr: 0x14DCD6, symBinAddr: 0x1548FE, symSize: 0x0 }
  - { offsetInCU: 0xA5CC, offset: 0x352A7, size: 0x4, addend: 0x0, symName: __ZL8file1074, symObjAddr: 0x14E36B, symBinAddr: 0x154F93, symSize: 0x0 }
  - { offsetInCU: 0xA5E3, offset: 0x352BE, size: 0x4, addend: 0x0, symName: __ZL8file1075, symObjAddr: 0x14EA01, symBinAddr: 0x155629, symSize: 0x0 }
  - { offsetInCU: 0xA5FA, offset: 0x352D5, size: 0x4, addend: 0x0, symName: __ZL8file1076, symObjAddr: 0x14F097, symBinAddr: 0x155CBF, symSize: 0x0 }
  - { offsetInCU: 0xA61E, offset: 0x352F9, size: 0x4, addend: 0x0, symName: __ZL8file1077, symObjAddr: 0x1500E1, symBinAddr: 0x156D09, symSize: 0x0 }
  - { offsetInCU: 0xA642, offset: 0x3531D, size: 0x4, addend: 0x0, symName: __ZL8file1078, symObjAddr: 0x15076B, symBinAddr: 0x157393, symSize: 0x0 }
  - { offsetInCU: 0xA666, offset: 0x35341, size: 0x4, addend: 0x0, symName: __ZL8file1079, symObjAddr: 0x150E03, symBinAddr: 0x157A2B, symSize: 0x0 }
  - { offsetInCU: 0xA68A, offset: 0x35365, size: 0x4, addend: 0x0, symName: __ZL8file1080, symObjAddr: 0x15148A, symBinAddr: 0x1580B2, symSize: 0x0 }
  - { offsetInCU: 0xA6AE, offset: 0x35389, size: 0x4, addend: 0x0, symName: __ZL8file1081, symObjAddr: 0x151B1D, symBinAddr: 0x158745, symSize: 0x0 }
  - { offsetInCU: 0xA6C5, offset: 0x353A0, size: 0x4, addend: 0x0, symName: __ZL8file1082, symObjAddr: 0x1521A8, symBinAddr: 0x158DD0, symSize: 0x0 }
  - { offsetInCU: 0xA6DC, offset: 0x353B7, size: 0x4, addend: 0x0, symName: __ZL8file1083, symObjAddr: 0x1524F1, symBinAddr: 0x159119, symSize: 0x0 }
  - { offsetInCU: 0xA6F3, offset: 0x353CE, size: 0x4, addend: 0x0, symName: __ZL8file1084, symObjAddr: 0x152B12, symBinAddr: 0x15973A, symSize: 0x0 }
  - { offsetInCU: 0xA70A, offset: 0x353E5, size: 0x4, addend: 0x0, symName: __ZL8file1085, symObjAddr: 0x153132, symBinAddr: 0x159D5A, symSize: 0x0 }
  - { offsetInCU: 0xA72E, offset: 0x35409, size: 0x4, addend: 0x0, symName: __ZL9patches89, symObjAddr: 0x1A239C, symBinAddr: 0x1A8F3C, symSize: 0x0 }
  - { offsetInCU: 0xA745, offset: 0x35420, size: 0x4, addend: 0x0, symName: __ZL11patchBuf113, symObjAddr: 0x1537BE, symBinAddr: 0x15A3E6, symSize: 0x0 }
  - { offsetInCU: 0xA75C, offset: 0x35437, size: 0x4, addend: 0x0, symName: __ZL11revisions40, symObjAddr: 0x4BA00, symBinAddr: 0x52628, symSize: 0x0 }
  - { offsetInCU: 0xA773, offset: 0x3544E, size: 0x4, addend: 0x0, symName: __ZL11platforms90, symObjAddr: 0x4BA08, symBinAddr: 0x52630, symSize: 0x0 }
  - { offsetInCU: 0xA78A, offset: 0x35465, size: 0x4, addend: 0x0, symName: __ZL8file1086, symObjAddr: 0x1537C2, symBinAddr: 0x15A3EA, symSize: 0x0 }
  - { offsetInCU: 0xA7A1, offset: 0x3547C, size: 0x4, addend: 0x0, symName: __ZL8file1087, symObjAddr: 0x1538F4, symBinAddr: 0x15A51C, symSize: 0x0 }
  - { offsetInCU: 0xA7B8, offset: 0x35493, size: 0x4, addend: 0x0, symName: __ZL8file1088, symObjAddr: 0x153A36, symBinAddr: 0x15A65E, symSize: 0x0 }
  - { offsetInCU: 0xA7CF, offset: 0x354AA, size: 0x4, addend: 0x0, symName: __ZL8file1089, symObjAddr: 0x153B83, symBinAddr: 0x15A7AB, symSize: 0x0 }
  - { offsetInCU: 0xA7E6, offset: 0x354C1, size: 0x4, addend: 0x0, symName: __ZL8file1090, symObjAddr: 0x153CF0, symBinAddr: 0x15A918, symSize: 0x0 }
  - { offsetInCU: 0xA80A, offset: 0x354E5, size: 0x4, addend: 0x0, symName: __ZL9layouts90, symObjAddr: 0x4BA6C, symBinAddr: 0x52694, symSize: 0x0 }
  - { offsetInCU: 0xA821, offset: 0x354FC, size: 0x4, addend: 0x0, symName: __ZL8file1091, symObjAddr: 0x15482C, symBinAddr: 0x15B454, symSize: 0x0 }
  - { offsetInCU: 0xA845, offset: 0x35520, size: 0x4, addend: 0x0, symName: __ZL8file1092, symObjAddr: 0x154DED, symBinAddr: 0x15BA15, symSize: 0x0 }
  - { offsetInCU: 0xA85C, offset: 0x35537, size: 0x4, addend: 0x0, symName: __ZL8file1093, symObjAddr: 0x1553AE, symBinAddr: 0x15BFD6, symSize: 0x0 }
  - { offsetInCU: 0xA880, offset: 0x3555B, size: 0x4, addend: 0x0, symName: __ZL8file1094, symObjAddr: 0x155A21, symBinAddr: 0x15C649, symSize: 0x0 }
  - { offsetInCU: 0xA897, offset: 0x35572, size: 0x4, addend: 0x0, symName: __ZL8file1095, symObjAddr: 0x156054, symBinAddr: 0x15CC7C, symSize: 0x0 }
  - { offsetInCU: 0xA8AE, offset: 0x35589, size: 0x4, addend: 0x0, symName: __ZL9patches90, symObjAddr: 0x1A24D0, symBinAddr: 0x1A9070, symSize: 0x0 }
  - { offsetInCU: 0xA8C5, offset: 0x355A0, size: 0x4, addend: 0x0, symName: __ZL11patchBuf114, symObjAddr: 0x156689, symBinAddr: 0x15D2B1, symSize: 0x0 }
  - { offsetInCU: 0xA8DC, offset: 0x355B7, size: 0x4, addend: 0x0, symName: __ZL11revisions41, symObjAddr: 0x4BAD0, symBinAddr: 0x526F8, symSize: 0x0 }
  - { offsetInCU: 0xA8F3, offset: 0x355CE, size: 0x4, addend: 0x0, symName: __ZL11platforms91, symObjAddr: 0x4BAD8, symBinAddr: 0x52700, symSize: 0x0 }
  - { offsetInCU: 0xA90A, offset: 0x355E5, size: 0x4, addend: 0x0, symName: __ZL8file1096, symObjAddr: 0x15668D, symBinAddr: 0x15D2B5, symSize: 0x0 }
  - { offsetInCU: 0xA921, offset: 0x355FC, size: 0x4, addend: 0x0, symName: __ZL8file1097, symObjAddr: 0x1568CB, symBinAddr: 0x15D4F3, symSize: 0x0 }
  - { offsetInCU: 0xA938, offset: 0x35613, size: 0x4, addend: 0x0, symName: __ZL8file1098, symObjAddr: 0x156A5B, symBinAddr: 0x15D683, symSize: 0x0 }
  - { offsetInCU: 0xA95C, offset: 0x35637, size: 0x4, addend: 0x0, symName: __ZL8file1099, symObjAddr: 0x156C09, symBinAddr: 0x15D831, symSize: 0x0 }
  - { offsetInCU: 0xA973, offset: 0x3564E, size: 0x4, addend: 0x0, symName: __ZL8file1100, symObjAddr: 0x156D6E, symBinAddr: 0x15D996, symSize: 0x0 }
  - { offsetInCU: 0xA98A, offset: 0x35665, size: 0x4, addend: 0x0, symName: __ZL8file1101, symObjAddr: 0x156EED, symBinAddr: 0x15DB15, symSize: 0x0 }
  - { offsetInCU: 0xA9A1, offset: 0x3567C, size: 0x4, addend: 0x0, symName: __ZL8file1102, symObjAddr: 0x15705E, symBinAddr: 0x15DC86, symSize: 0x0 }
  - { offsetInCU: 0xA9B8, offset: 0x35693, size: 0x4, addend: 0x0, symName: __ZL8file1103, symObjAddr: 0x1571AA, symBinAddr: 0x15DDD2, symSize: 0x0 }
  - { offsetInCU: 0xA9CF, offset: 0x356AA, size: 0x4, addend: 0x0, symName: __ZL9layouts91, symObjAddr: 0x4BBC8, symBinAddr: 0x527F0, symSize: 0x0 }
  - { offsetInCU: 0xA9E6, offset: 0x356C1, size: 0x4, addend: 0x0, symName: __ZL8file1104, symObjAddr: 0x157319, symBinAddr: 0x15DF41, symSize: 0x0 }
  - { offsetInCU: 0xA9FD, offset: 0x356D8, size: 0x4, addend: 0x0, symName: __ZL8file1105, symObjAddr: 0x157669, symBinAddr: 0x15E291, symSize: 0x0 }
  - { offsetInCU: 0xAA14, offset: 0x356EF, size: 0x4, addend: 0x0, symName: __ZL8file1106, symObjAddr: 0x1579B8, symBinAddr: 0x15E5E0, symSize: 0x0 }
  - { offsetInCU: 0xAA2B, offset: 0x35706, size: 0x4, addend: 0x0, symName: __ZL8file1107, symObjAddr: 0x157D08, symBinAddr: 0x15E930, symSize: 0x0 }
  - { offsetInCU: 0xAA4F, offset: 0x3572A, size: 0x4, addend: 0x0, symName: __ZL8file1108, symObjAddr: 0x158038, symBinAddr: 0x15EC60, symSize: 0x0 }
  - { offsetInCU: 0xAA66, offset: 0x35741, size: 0x4, addend: 0x0, symName: __ZL8file1109, symObjAddr: 0x158368, symBinAddr: 0x15EF90, symSize: 0x0 }
  - { offsetInCU: 0xAA7D, offset: 0x35758, size: 0x4, addend: 0x0, symName: __ZL8file1110, symObjAddr: 0x1586CA, symBinAddr: 0x15F2F2, symSize: 0x0 }
  - { offsetInCU: 0xAA94, offset: 0x3576F, size: 0x4, addend: 0x0, symName: __ZL8file1111, symObjAddr: 0x158A2C, symBinAddr: 0x15F654, symSize: 0x0 }
  - { offsetInCU: 0xAAAB, offset: 0x35786, size: 0x4, addend: 0x0, symName: __ZL8file1112, symObjAddr: 0x158DA7, symBinAddr: 0x15F9CF, symSize: 0x0 }
  - { offsetInCU: 0xAACF, offset: 0x357AA, size: 0x4, addend: 0x0, symName: __ZL8file1113, symObjAddr: 0x1592EF, symBinAddr: 0x15FF17, symSize: 0x0 }
  - { offsetInCU: 0xAAF3, offset: 0x357CE, size: 0x4, addend: 0x0, symName: __ZL8file1114, symObjAddr: 0x15982E, symBinAddr: 0x160456, symSize: 0x0 }
  - { offsetInCU: 0xAB17, offset: 0x357F2, size: 0x4, addend: 0x0, symName: __ZL8file1115, symObjAddr: 0x159BA6, symBinAddr: 0x1607CE, symSize: 0x0 }
  - { offsetInCU: 0xAB2E, offset: 0x35809, size: 0x4, addend: 0x0, symName: __ZL9patches91, symObjAddr: 0x1A25CC, symBinAddr: 0x1A916C, symSize: 0x0 }
  - { offsetInCU: 0xAB45, offset: 0x35820, size: 0x4, addend: 0x0, symName: __ZL11patchBuf115, symObjAddr: 0x159ED7, symBinAddr: 0x160AFF, symSize: 0x0 }
  - { offsetInCU: 0xAB5C, offset: 0x35837, size: 0x4, addend: 0x0, symName: __ZL11revisions42, symObjAddr: 0x4BCB8, symBinAddr: 0x528E0, symSize: 0x0 }
  - { offsetInCU: 0xAB73, offset: 0x3584E, size: 0x4, addend: 0x0, symName: __ZL11platforms92, symObjAddr: 0x4BCBC, symBinAddr: 0x528E4, symSize: 0x0 }
  - { offsetInCU: 0xAB8A, offset: 0x35865, size: 0x4, addend: 0x0, symName: __ZL8file1116, symObjAddr: 0x159EDB, symBinAddr: 0x160B03, symSize: 0x0 }
  - { offsetInCU: 0xABAE, offset: 0x35889, size: 0x4, addend: 0x0, symName: __ZL8file1117, symObjAddr: 0x15A001, symBinAddr: 0x160C29, symSize: 0x0 }
  - { offsetInCU: 0xABC5, offset: 0x358A0, size: 0x4, addend: 0x0, symName: __ZL9layouts92, symObjAddr: 0x4BCE4, symBinAddr: 0x5290C, symSize: 0x0 }
  - { offsetInCU: 0xABDC, offset: 0x358B7, size: 0x4, addend: 0x0, symName: __ZL8file1118, symObjAddr: 0x15A13D, symBinAddr: 0x160D65, symSize: 0x0 }
  - { offsetInCU: 0xABF3, offset: 0x358CE, size: 0x4, addend: 0x0, symName: __ZL8file1119, symObjAddr: 0x15A20E, symBinAddr: 0x160E36, symSize: 0x0 }
  - { offsetInCU: 0xAC17, offset: 0x358F2, size: 0x4, addend: 0x0, symName: __ZL9patches92, symObjAddr: 0x1A2690, symBinAddr: 0x1A9230, symSize: 0x0 }
  - { offsetInCU: 0xAC2E, offset: 0x35909, size: 0x4, addend: 0x0, symName: __ZL11patchBuf116, symObjAddr: 0x15A8A9, symBinAddr: 0x1614D1, symSize: 0x0 }
  - { offsetInCU: 0xAC45, offset: 0x35920, size: 0x4, addend: 0x0, symName: __ZL11platforms93, symObjAddr: 0x4BD0C, symBinAddr: 0x52934, symSize: 0x0 }
  - { offsetInCU: 0xAC5C, offset: 0x35937, size: 0x4, addend: 0x0, symName: __ZL8file1120, symObjAddr: 0x15A8AD, symBinAddr: 0x1614D5, symSize: 0x0 }
  - { offsetInCU: 0xAC73, offset: 0x3594E, size: 0x4, addend: 0x0, symName: __ZL8file1121, symObjAddr: 0x15A9E9, symBinAddr: 0x161611, symSize: 0x0 }
  - { offsetInCU: 0xAC8A, offset: 0x35965, size: 0x4, addend: 0x0, symName: __ZL8file1122, symObjAddr: 0x15AB22, symBinAddr: 0x16174A, symSize: 0x0 }
  - { offsetInCU: 0xACA1, offset: 0x3597C, size: 0x4, addend: 0x0, symName: __ZL8file1123, symObjAddr: 0x15AC59, symBinAddr: 0x161881, symSize: 0x0 }
  - { offsetInCU: 0xACB8, offset: 0x35993, size: 0x4, addend: 0x0, symName: __ZL9layouts93, symObjAddr: 0x4BD5C, symBinAddr: 0x52984, symSize: 0x0 }
  - { offsetInCU: 0xACCF, offset: 0x359AA, size: 0x4, addend: 0x0, symName: __ZL8file1124, symObjAddr: 0x15AD8F, symBinAddr: 0x1619B7, symSize: 0x0 }
  - { offsetInCU: 0xACE6, offset: 0x359C1, size: 0x4, addend: 0x0, symName: __ZL8file1125, symObjAddr: 0x15BB51, symBinAddr: 0x162779, symSize: 0x0 }
  - { offsetInCU: 0xACFD, offset: 0x359D8, size: 0x4, addend: 0x0, symName: __ZL8file1126, symObjAddr: 0x15C914, symBinAddr: 0x16353C, symSize: 0x0 }
  - { offsetInCU: 0xAD21, offset: 0x359FC, size: 0x4, addend: 0x0, symName: __ZL8file1127, symObjAddr: 0x15D8C7, symBinAddr: 0x1644EF, symSize: 0x0 }
  - { offsetInCU: 0xAD38, offset: 0x35A13, size: 0x4, addend: 0x0, symName: __ZL9patches93, symObjAddr: 0x1A27A8, symBinAddr: 0x1A9348, symSize: 0x0 }
  - { offsetInCU: 0xAD4F, offset: 0x35A2A, size: 0x4, addend: 0x0, symName: __ZL11patchBuf117, symObjAddr: 0x15E689, symBinAddr: 0x1652B1, symSize: 0x0 }
  - { offsetInCU: 0xAD66, offset: 0x35A41, size: 0x4, addend: 0x0, symName: __ZL11revisions43, symObjAddr: 0x4BDAC, symBinAddr: 0x529D4, symSize: 0x0 }
  - { offsetInCU: 0xAD7D, offset: 0x35A58, size: 0x4, addend: 0x0, symName: __ZL11platforms94, symObjAddr: 0x4BDB0, symBinAddr: 0x529D8, symSize: 0x0 }
  - { offsetInCU: 0xAD94, offset: 0x35A6F, size: 0x4, addend: 0x0, symName: __ZL8file1128, symObjAddr: 0x15E68D, symBinAddr: 0x1652B5, symSize: 0x0 }
  - { offsetInCU: 0xADAB, offset: 0x35A86, size: 0x4, addend: 0x0, symName: __ZL9layouts94, symObjAddr: 0x4BDC4, symBinAddr: 0x529EC, symSize: 0x0 }
  - { offsetInCU: 0xADC2, offset: 0x35A9D, size: 0x4, addend: 0x0, symName: __ZL8file1129, symObjAddr: 0x15E7E6, symBinAddr: 0x16540E, symSize: 0x0 }
  - { offsetInCU: 0xADE6, offset: 0x35AC1, size: 0x4, addend: 0x0, symName: __ZL9patches94, symObjAddr: 0x1A28A4, symBinAddr: 0x1A9444, symSize: 0x0 }
  - { offsetInCU: 0xADFD, offset: 0x35AD8, size: 0x4, addend: 0x0, symName: __ZL11patchBuf118, symObjAddr: 0x15EEAA, symBinAddr: 0x165AD2, symSize: 0x0 }
  - { offsetInCU: 0xAE14, offset: 0x35AEF, size: 0x4, addend: 0x0, symName: __ZL11revisions44, symObjAddr: 0x4BDD8, symBinAddr: 0x52A00, symSize: 0x0 }
  - { offsetInCU: 0xAE2B, offset: 0x35B06, size: 0x4, addend: 0x0, symName: __ZL11platforms95, symObjAddr: 0x4BDE0, symBinAddr: 0x52A08, symSize: 0x0 }
  - { offsetInCU: 0xAE42, offset: 0x35B1D, size: 0x4, addend: 0x0, symName: __ZL8file1130, symObjAddr: 0x15EEAE, symBinAddr: 0x165AD6, symSize: 0x0 }
  - { offsetInCU: 0xAE59, offset: 0x35B34, size: 0x4, addend: 0x0, symName: __ZL8file1131, symObjAddr: 0x15F2B6, symBinAddr: 0x165EDE, symSize: 0x0 }
  - { offsetInCU: 0xAE70, offset: 0x35B4B, size: 0x4, addend: 0x0, symName: __ZL8file1132, symObjAddr: 0x15F6BE, symBinAddr: 0x1662E6, symSize: 0x0 }
  - { offsetInCU: 0xAE87, offset: 0x35B62, size: 0x4, addend: 0x0, symName: __ZL8file1133, symObjAddr: 0x15F841, symBinAddr: 0x166469, symSize: 0x0 }
  - { offsetInCU: 0xAEAD, offset: 0x35B88, size: 0x4, addend: 0x0, symName: __ZL9layouts95, symObjAddr: 0x4BF48, symBinAddr: 0x52B70, symSize: 0x0 }
  - { offsetInCU: 0xAEC4, offset: 0x35B9F, size: 0x4, addend: 0x0, symName: __ZL8file1134, symObjAddr: 0x1605D6, symBinAddr: 0x1671FE, symSize: 0x0 }
  - { offsetInCU: 0xAEE8, offset: 0x35BC3, size: 0x4, addend: 0x0, symName: __ZL8file1135, symObjAddr: 0x160B76, symBinAddr: 0x16779E, symSize: 0x0 }
  - { offsetInCU: 0xAEFF, offset: 0x35BDA, size: 0x4, addend: 0x0, symName: __ZL8file1136, symObjAddr: 0x161116, symBinAddr: 0x167D3E, symSize: 0x0 }
  - { offsetInCU: 0xAF23, offset: 0x35BFE, size: 0x4, addend: 0x0, symName: __ZL8file1137, symObjAddr: 0x161700, symBinAddr: 0x168328, symSize: 0x0 }
  - { offsetInCU: 0xAF47, offset: 0x35C22, size: 0x4, addend: 0x0, symName: __ZL8file1138, symObjAddr: 0x162A86, symBinAddr: 0x1696AE, symSize: 0x0 }
  - { offsetInCU: 0xAF6B, offset: 0x35C46, size: 0x4, addend: 0x0, symName: __ZL8file1139, symObjAddr: 0x1632EF, symBinAddr: 0x169F17, symSize: 0x0 }
  - { offsetInCU: 0xAF8F, offset: 0x35C6A, size: 0x4, addend: 0x0, symName: __ZL8file1140, symObjAddr: 0x163D47, symBinAddr: 0x16A96F, symSize: 0x0 }
  - { offsetInCU: 0xAFB3, offset: 0x35C8E, size: 0x4, addend: 0x0, symName: __ZL8file1141, symObjAddr: 0x1645B4, symBinAddr: 0x16B1DC, symSize: 0x0 }
  - { offsetInCU: 0xAFD7, offset: 0x35CB2, size: 0x4, addend: 0x0, symName: __ZL8file1142, symObjAddr: 0x164EDC, symBinAddr: 0x16BB04, symSize: 0x0 }
  - { offsetInCU: 0xAFFD, offset: 0x35CD8, size: 0x4, addend: 0x0, symName: __ZL8file1143, symObjAddr: 0x166599, symBinAddr: 0x16D1C1, symSize: 0x0 }
  - { offsetInCU: 0xB023, offset: 0x35CFE, size: 0x4, addend: 0x0, symName: __ZL8file1144, symObjAddr: 0x167F23, symBinAddr: 0x16EB4B, symSize: 0x0 }
  - { offsetInCU: 0xB049, offset: 0x35D24, size: 0x4, addend: 0x0, symName: __ZL8file1145, symObjAddr: 0x168535, symBinAddr: 0x16F15D, symSize: 0x0 }
  - { offsetInCU: 0xB06F, offset: 0x35D4A, size: 0x4, addend: 0x0, symName: __ZL8file1146, symObjAddr: 0x168BD4, symBinAddr: 0x16F7FC, symSize: 0x0 }
  - { offsetInCU: 0xB095, offset: 0x35D70, size: 0x4, addend: 0x0, symName: __ZL8file1147, symObjAddr: 0x169278, symBinAddr: 0x16FEA0, symSize: 0x0 }
  - { offsetInCU: 0xB0BB, offset: 0x35D96, size: 0x4, addend: 0x0, symName: __ZL8file1148, symObjAddr: 0x169B70, symBinAddr: 0x170798, symSize: 0x0 }
  - { offsetInCU: 0xB0E1, offset: 0x35DBC, size: 0x4, addend: 0x0, symName: __ZL8file1149, symObjAddr: 0x16A476, symBinAddr: 0x17109E, symSize: 0x0 }
  - { offsetInCU: 0xB107, offset: 0x35DE2, size: 0x4, addend: 0x0, symName: __ZL8file1150, symObjAddr: 0x16B38D, symBinAddr: 0x171FB5, symSize: 0x0 }
  - { offsetInCU: 0xB12D, offset: 0x35E08, size: 0x4, addend: 0x0, symName: __ZL8file1151, symObjAddr: 0x16BF9D, symBinAddr: 0x172BC5, symSize: 0x0 }
  - { offsetInCU: 0xB153, offset: 0x35E2E, size: 0x4, addend: 0x0, symName: __ZL9patches95, symObjAddr: 0x1A2984, symBinAddr: 0x1A9524, symSize: 0x0 }
  - { offsetInCU: 0xB16C, offset: 0x35E47, size: 0x4, addend: 0x0, symName: __ZL11platforms96, symObjAddr: 0x4C0B0, symBinAddr: 0x52CD8, symSize: 0x0 }
  - { offsetInCU: 0xB185, offset: 0x35E60, size: 0x4, addend: 0x0, symName: __ZL8file1152, symObjAddr: 0x16C2C7, symBinAddr: 0x172EEF, symSize: 0x0 }
  - { offsetInCU: 0xB19E, offset: 0x35E79, size: 0x4, addend: 0x0, symName: __ZL9layouts96, symObjAddr: 0x4C0D8, symBinAddr: 0x52D00, symSize: 0x0 }
  - { offsetInCU: 0xB1B7, offset: 0x35E92, size: 0x4, addend: 0x0, symName: __ZL8file1153, symObjAddr: 0x16C486, symBinAddr: 0x1730AE, symSize: 0x0 }
  - { offsetInCU: 0xB1DD, offset: 0x35EB8, size: 0x4, addend: 0x0, symName: __ZL8file1154, symObjAddr: 0x16C7A4, symBinAddr: 0x1733CC, symSize: 0x0 }
  - { offsetInCU: 0xB1F6, offset: 0x35ED1, size: 0x4, addend: 0x0, symName: __ZL9patches96, symObjAddr: 0x1A2A48, symBinAddr: 0x1A95E8, symSize: 0x0 }
  - { offsetInCU: 0xB20F, offset: 0x35EEA, size: 0x4, addend: 0x0, symName: __ZL11patchBuf119, symObjAddr: 0x16CAC2, symBinAddr: 0x1736EA, symSize: 0x0 }
  - { offsetInCU: 0xB228, offset: 0x35F03, size: 0x4, addend: 0x0, symName: __ZL11revisions45, symObjAddr: 0x4C100, symBinAddr: 0x52D28, symSize: 0x0 }
  - { offsetInCU: 0xB241, offset: 0x35F1C, size: 0x4, addend: 0x0, symName: __ZL11platforms97, symObjAddr: 0x4C104, symBinAddr: 0x52D2C, symSize: 0x0 }
  - { offsetInCU: 0xB25A, offset: 0x35F35, size: 0x4, addend: 0x0, symName: __ZL8file1155, symObjAddr: 0x16CAC6, symBinAddr: 0x1736EE, symSize: 0x0 }
  - { offsetInCU: 0xB273, offset: 0x35F4E, size: 0x4, addend: 0x0, symName: __ZL8file1156, symObjAddr: 0x16CFFC, symBinAddr: 0x173C24, symSize: 0x0 }
  - { offsetInCU: 0xB299, offset: 0x35F74, size: 0x4, addend: 0x0, symName: __ZL9layouts97, symObjAddr: 0x4C12C, symBinAddr: 0x52D54, symSize: 0x0 }
  - { offsetInCU: 0xB2B2, offset: 0x35F8D, size: 0x4, addend: 0x0, symName: __ZL8file1157, symObjAddr: 0x16D549, symBinAddr: 0x174171, symSize: 0x0 }
  - { offsetInCU: 0xB2CB, offset: 0x35FA6, size: 0x4, addend: 0x0, symName: __ZL8file1158, symObjAddr: 0x16D877, symBinAddr: 0x17449F, symSize: 0x0 }
  - { offsetInCU: 0xB2E4, offset: 0x35FBF, size: 0x4, addend: 0x0, symName: __ZL9patches97, symObjAddr: 0x1A2B28, symBinAddr: 0x1A96C8, symSize: 0x0 }
  - { offsetInCU: 0xB2FD, offset: 0x35FD8, size: 0x4, addend: 0x0, symName: __ZL11patchBuf120, symObjAddr: 0x16DBA6, symBinAddr: 0x1747CE, symSize: 0x0 }
  - { offsetInCU: 0xB316, offset: 0x35FF1, size: 0x4, addend: 0x0, symName: __ZL11platforms98, symObjAddr: 0x4C154, symBinAddr: 0x52D7C, symSize: 0x0 }
  - { offsetInCU: 0xB32F, offset: 0x3600A, size: 0x4, addend: 0x0, symName: __ZL8file1159, symObjAddr: 0x16DBAA, symBinAddr: 0x1747D2, symSize: 0x0 }
  - { offsetInCU: 0xB348, offset: 0x36023, size: 0x4, addend: 0x0, symName: __ZL8file1160, symObjAddr: 0x16DCE7, symBinAddr: 0x17490F, symSize: 0x0 }
  - { offsetInCU: 0xB361, offset: 0x3603C, size: 0x4, addend: 0x0, symName: __ZL8file1161, symObjAddr: 0x16DE22, symBinAddr: 0x174A4A, symSize: 0x0 }
  - { offsetInCU: 0xB37A, offset: 0x36055, size: 0x4, addend: 0x0, symName: __ZL8file1162, symObjAddr: 0x16DF5B, symBinAddr: 0x174B83, symSize: 0x0 }
  - { offsetInCU: 0xB393, offset: 0x3606E, size: 0x4, addend: 0x0, symName: __ZL8file1163, symObjAddr: 0x16E0D5, symBinAddr: 0x174CFD, symSize: 0x0 }
  - { offsetInCU: 0xB3AC, offset: 0x36087, size: 0x4, addend: 0x0, symName: __ZL8file1164, symObjAddr: 0x16E213, symBinAddr: 0x174E3B, symSize: 0x0 }
  - { offsetInCU: 0xB3C5, offset: 0x360A0, size: 0x4, addend: 0x0, symName: __ZL8file1165, symObjAddr: 0x16E33D, symBinAddr: 0x174F65, symSize: 0x0 }
  - { offsetInCU: 0xB3DE, offset: 0x360B9, size: 0x4, addend: 0x0, symName: __ZL8file1166, symObjAddr: 0x16E47C, symBinAddr: 0x1750A4, symSize: 0x0 }
  - { offsetInCU: 0xB3F7, offset: 0x360D2, size: 0x4, addend: 0x0, symName: __ZL8file1167, symObjAddr: 0x16E5CD, symBinAddr: 0x1751F5, symSize: 0x0 }
  - { offsetInCU: 0xB41D, offset: 0x360F8, size: 0x4, addend: 0x0, symName: __ZL9layouts98, symObjAddr: 0x4C208, symBinAddr: 0x52E30, symSize: 0x0 }
  - { offsetInCU: 0xB436, offset: 0x36111, size: 0x4, addend: 0x0, symName: __ZL8file1168, symObjAddr: 0x16E6F4, symBinAddr: 0x17531C, symSize: 0x0 }
  - { offsetInCU: 0xB45C, offset: 0x36137, size: 0x4, addend: 0x0, symName: __ZL8file1169, symObjAddr: 0x16ECCD, symBinAddr: 0x1758F5, symSize: 0x0 }
  - { offsetInCU: 0xB475, offset: 0x36150, size: 0x4, addend: 0x0, symName: __ZL8file1170, symObjAddr: 0x16F2A3, symBinAddr: 0x175ECB, symSize: 0x0 }
  - { offsetInCU: 0xB48E, offset: 0x36169, size: 0x4, addend: 0x0, symName: __ZL8file1171, symObjAddr: 0x16F879, symBinAddr: 0x1764A1, symSize: 0x0 }
  - { offsetInCU: 0xB4A7, offset: 0x36182, size: 0x4, addend: 0x0, symName: __ZL8file1172, symObjAddr: 0x1707B1, symBinAddr: 0x1773D9, symSize: 0x0 }
  - { offsetInCU: 0xB4C0, offset: 0x3619B, size: 0x4, addend: 0x0, symName: __ZL8file1173, symObjAddr: 0x170D89, symBinAddr: 0x1779B1, symSize: 0x0 }
  - { offsetInCU: 0xB4E6, offset: 0x361C1, size: 0x4, addend: 0x0, symName: __ZL8file1174, symObjAddr: 0x17135B, symBinAddr: 0x177F83, symSize: 0x0 }
  - { offsetInCU: 0xB4FF, offset: 0x361DA, size: 0x4, addend: 0x0, symName: __ZL8file1175, symObjAddr: 0x17192B, symBinAddr: 0x178553, symSize: 0x0 }
  - { offsetInCU: 0xB518, offset: 0x361F3, size: 0x4, addend: 0x0, symName: __ZL8file1176, symObjAddr: 0x171F01, symBinAddr: 0x178B29, symSize: 0x0 }
  - { offsetInCU: 0xB53E, offset: 0x36219, size: 0x4, addend: 0x0, symName: __ZL9patches98, symObjAddr: 0x1A2C08, symBinAddr: 0x1A97A8, symSize: 0x0 }
  - { offsetInCU: 0xB557, offset: 0x36232, size: 0x4, addend: 0x0, symName: __ZL11patchBuf121, symObjAddr: 0x1724D2, symBinAddr: 0x1790FA, symSize: 0x0 }
  - { offsetInCU: 0xB570, offset: 0x3624B, size: 0x4, addend: 0x0, symName: __ZL11revisions46, symObjAddr: 0x4C2BC, symBinAddr: 0x52EE4, symSize: 0x0 }
  - { offsetInCU: 0xB589, offset: 0x36264, size: 0x4, addend: 0x0, symName: __ZL11platforms99, symObjAddr: 0x4C2C0, symBinAddr: 0x52EE8, symSize: 0x0 }
  - { offsetInCU: 0xB5AE, offset: 0x36289, size: 0x4, addend: 0x0, symName: __ZL8file1177, symObjAddr: 0x1724D6, symBinAddr: 0x1790FE, symSize: 0x0 }
  - { offsetInCU: 0xB5C7, offset: 0x362A2, size: 0x4, addend: 0x0, symName: __ZL8file1178, symObjAddr: 0x17262B, symBinAddr: 0x179253, symSize: 0x0 }
  - { offsetInCU: 0xB5E0, offset: 0x362BB, size: 0x4, addend: 0x0, symName: __ZL8file1179, symObjAddr: 0x17276B, symBinAddr: 0x179393, symSize: 0x0 }
  - { offsetInCU: 0xB5F9, offset: 0x362D4, size: 0x4, addend: 0x0, symName: __ZL8file1180, symObjAddr: 0x1728B3, symBinAddr: 0x1794DB, symSize: 0x0 }
  - { offsetInCU: 0xB612, offset: 0x362ED, size: 0x4, addend: 0x0, symName: __ZL8file1181, symObjAddr: 0x1729F1, symBinAddr: 0x179619, symSize: 0x0 }
  - { offsetInCU: 0xB638, offset: 0x36313, size: 0x4, addend: 0x0, symName: __ZL8file1182, symObjAddr: 0x172B10, symBinAddr: 0x179738, symSize: 0x0 }
  - { offsetInCU: 0xB65E, offset: 0x36339, size: 0x4, addend: 0x0, symName: __ZL8file1183, symObjAddr: 0x173683, symBinAddr: 0x17A2AB, symSize: 0x0 }
  - { offsetInCU: 0xB684, offset: 0x3635F, size: 0x4, addend: 0x0, symName: __ZL8file1184, symObjAddr: 0x1740CB, symBinAddr: 0x17ACF3, symSize: 0x0 }
  - { offsetInCU: 0xB69D, offset: 0x36378, size: 0x4, addend: 0x0, symName: __ZL8file1185, symObjAddr: 0x174203, symBinAddr: 0x17AE2B, symSize: 0x0 }
  - { offsetInCU: 0xB6C3, offset: 0x3639E, size: 0x4, addend: 0x0, symName: __ZL8file1186, symObjAddr: 0x175DBA, symBinAddr: 0x17C9E2, symSize: 0x0 }
  - { offsetInCU: 0xB6DC, offset: 0x363B7, size: 0x4, addend: 0x0, symName: __ZL8file1187, symObjAddr: 0x175EFA, symBinAddr: 0x17CB22, symSize: 0x0 }
  - { offsetInCU: 0xB6F5, offset: 0x363D0, size: 0x4, addend: 0x0, symName: __ZL8file1188, symObjAddr: 0x176A46, symBinAddr: 0x17D66E, symSize: 0x0 }
  - { offsetInCU: 0xB70E, offset: 0x363E9, size: 0x4, addend: 0x0, symName: __ZL8file1189, symObjAddr: 0x177592, symBinAddr: 0x17E1BA, symSize: 0x0 }
  - { offsetInCU: 0xB734, offset: 0x3640F, size: 0x4, addend: 0x0, symName: __ZL8file1190, symObjAddr: 0x1780D4, symBinAddr: 0x17ECFC, symSize: 0x0 }
  - { offsetInCU: 0xB74D, offset: 0x36428, size: 0x4, addend: 0x0, symName: __ZL8file1191, symObjAddr: 0x17821B, symBinAddr: 0x17EE43, symSize: 0x0 }
  - { offsetInCU: 0xB766, offset: 0x36441, size: 0x4, addend: 0x0, symName: __ZL8file1192, symObjAddr: 0x17835B, symBinAddr: 0x17EF83, symSize: 0x0 }
  - { offsetInCU: 0xB77F, offset: 0x3645A, size: 0x4, addend: 0x0, symName: __ZL9layouts99, symObjAddr: 0x4C400, symBinAddr: 0x53028, symSize: 0x0 }
  - { offsetInCU: 0xB798, offset: 0x36473, size: 0x4, addend: 0x0, symName: __ZL8file1193, symObjAddr: 0x178498, symBinAddr: 0x17F0C0, symSize: 0x0 }
  - { offsetInCU: 0xB7BE, offset: 0x36499, size: 0x4, addend: 0x0, symName: __ZL8file1194, symObjAddr: 0x17944E, symBinAddr: 0x180076, symSize: 0x0 }
  - { offsetInCU: 0xB7D7, offset: 0x364B2, size: 0x4, addend: 0x0, symName: __ZL8file1195, symObjAddr: 0x179A70, symBinAddr: 0x180698, symSize: 0x0 }
  - { offsetInCU: 0xB7FD, offset: 0x364D8, size: 0x4, addend: 0x0, symName: __ZL8file1196, symObjAddr: 0x17A04C, symBinAddr: 0x180C74, symSize: 0x0 }
  - { offsetInCU: 0xB816, offset: 0x364F1, size: 0x4, addend: 0x0, symName: __ZL8file1197, symObjAddr: 0x17A628, symBinAddr: 0x181250, symSize: 0x0 }
  - { offsetInCU: 0xB82F, offset: 0x3650A, size: 0x4, addend: 0x0, symName: __ZL8file1198, symObjAddr: 0x17AC3F, symBinAddr: 0x181867, symSize: 0x0 }
  - { offsetInCU: 0xB855, offset: 0x36530, size: 0x4, addend: 0x0, symName: __ZL8file1199, symObjAddr: 0x17BBCD, symBinAddr: 0x1827F5, symSize: 0x0 }
  - { offsetInCU: 0xB86E, offset: 0x36549, size: 0x4, addend: 0x0, symName: __ZL8file1200, symObjAddr: 0x17C98D, symBinAddr: 0x1835B5, symSize: 0x0 }
  - { offsetInCU: 0xB887, offset: 0x36562, size: 0x4, addend: 0x0, symName: __ZL8file1201, symObjAddr: 0x17CEE9, symBinAddr: 0x183B11, symSize: 0x0 }
  - { offsetInCU: 0xB8A0, offset: 0x3657B, size: 0x4, addend: 0x0, symName: __ZL8file1202, symObjAddr: 0x17DCA7, symBinAddr: 0x1848CF, symSize: 0x0 }
  - { offsetInCU: 0xB8C6, offset: 0x365A1, size: 0x4, addend: 0x0, symName: __ZL8file1203, symObjAddr: 0x17E205, symBinAddr: 0x184E2D, symSize: 0x0 }
  - { offsetInCU: 0xB8EC, offset: 0x365C7, size: 0x4, addend: 0x0, symName: __ZL8file1204, symObjAddr: 0x17F1C6, symBinAddr: 0x185DEE, symSize: 0x0 }
  - { offsetInCU: 0xB905, offset: 0x365E0, size: 0x4, addend: 0x0, symName: __ZL8file1205, symObjAddr: 0x180187, symBinAddr: 0x186DAF, symSize: 0x0 }
  - { offsetInCU: 0xB92B, offset: 0x36606, size: 0x4, addend: 0x0, symName: __ZL8file1206, symObjAddr: 0x18114A, symBinAddr: 0x187D72, symSize: 0x0 }
  - { offsetInCU: 0xB951, offset: 0x3662C, size: 0x4, addend: 0x0, symName: __ZL8file1207, symObjAddr: 0x181ACE, symBinAddr: 0x1886F6, symSize: 0x0 }
  - { offsetInCU: 0xB977, offset: 0x36652, size: 0x4, addend: 0x0, symName: __ZL8file1208, symObjAddr: 0x182538, symBinAddr: 0x189160, symSize: 0x0 }
  - { offsetInCU: 0xB990, offset: 0x3666B, size: 0x4, addend: 0x0, symName: __ZL9patches99, symObjAddr: 0x1A2CCC, symBinAddr: 0x1A986C, symSize: 0x0 }
  - { offsetInCU: 0xB9A9, offset: 0x36684, size: 0x4, addend: 0x0, symName: __ZL11patchBuf122, symObjAddr: 0x1832FC, symBinAddr: 0x189F24, symSize: 0x0 }
  - { offsetInCU: 0xB9C2, offset: 0x3669D, size: 0x4, addend: 0x0, symName: __ZL11revisions47, symObjAddr: 0x4C540, symBinAddr: 0x53168, symSize: 0x0 }
  - { offsetInCU: 0xB9DB, offset: 0x366B6, size: 0x4, addend: 0x0, symName: __ZL12platforms100, symObjAddr: 0x4C544, symBinAddr: 0x5316C, symSize: 0x0 }
  - { offsetInCU: 0xB9F4, offset: 0x366CF, size: 0x4, addend: 0x0, symName: __ZL8file1209, symObjAddr: 0x183300, symBinAddr: 0x189F28, symSize: 0x0 }
  - { offsetInCU: 0xBA0D, offset: 0x366E8, size: 0x4, addend: 0x0, symName: __ZL10layouts100, symObjAddr: 0x4C558, symBinAddr: 0x53180, symSize: 0x0 }
  - { offsetInCU: 0xBA26, offset: 0x36701, size: 0x4, addend: 0x0, symName: __ZL8file1210, symObjAddr: 0x183440, symBinAddr: 0x18A068, symSize: 0x0 }
  - { offsetInCU: 0xBA3F, offset: 0x3671A, size: 0x4, addend: 0x0, symName: __ZL10patches100, symObjAddr: 0x1A2DAC, symBinAddr: 0x1A994C, symSize: 0x0 }
  - { offsetInCU: 0xBA58, offset: 0x36733, size: 0x4, addend: 0x0, symName: __ZL11patchBuf123, symObjAddr: 0x183ACB, symBinAddr: 0x18A6F3, symSize: 0x0 }
  - { offsetInCU: 0xBA71, offset: 0x3674C, size: 0x4, addend: 0x0, symName: __ZL12platforms101, symObjAddr: 0x4C56C, symBinAddr: 0x53194, symSize: 0x0 }
  - { offsetInCU: 0xBA8A, offset: 0x36765, size: 0x4, addend: 0x0, symName: __ZL8file1211, symObjAddr: 0x183ACF, symBinAddr: 0x18A6F7, symSize: 0x0 }
  - { offsetInCU: 0xBAB0, offset: 0x3678B, size: 0x4, addend: 0x0, symName: __ZL8file1212, symObjAddr: 0x183BE3, symBinAddr: 0x18A80B, symSize: 0x0 }
  - { offsetInCU: 0xBAC9, offset: 0x367A4, size: 0x4, addend: 0x0, symName: __ZL8file1213, symObjAddr: 0x183D26, symBinAddr: 0x18A94E, symSize: 0x0 }
  - { offsetInCU: 0xBAE2, offset: 0x367BD, size: 0x4, addend: 0x0, symName: __ZL8file1214, symObjAddr: 0x183E67, symBinAddr: 0x18AA8F, symSize: 0x0 }
  - { offsetInCU: 0xBAFB, offset: 0x367D6, size: 0x4, addend: 0x0, symName: __ZL10layouts101, symObjAddr: 0x4C5BC, symBinAddr: 0x531E4, symSize: 0x0 }
  - { offsetInCU: 0xBB14, offset: 0x367EF, size: 0x4, addend: 0x0, symName: __ZL8file1215, symObjAddr: 0x183FB2, symBinAddr: 0x18ABDA, symSize: 0x0 }
  - { offsetInCU: 0xBB39, offset: 0x36814, size: 0x4, addend: 0x0, symName: __ZL8file1216, symObjAddr: 0x18405A, symBinAddr: 0x18AC82, symSize: 0x0 }
  - { offsetInCU: 0xBB52, offset: 0x3682D, size: 0x4, addend: 0x0, symName: __ZL8file1217, symObjAddr: 0x18457F, symBinAddr: 0x18B1A7, symSize: 0x0 }
  - { offsetInCU: 0xBB6B, offset: 0x36846, size: 0x4, addend: 0x0, symName: __ZL8file1218, symObjAddr: 0x184AAD, symBinAddr: 0x18B6D5, symSize: 0x0 }
  - { offsetInCU: 0xBB91, offset: 0x3686C, size: 0x4, addend: 0x0, symName: __ZL10patches101, symObjAddr: 0x1A2EE0, symBinAddr: 0x1A9A80, symSize: 0x0 }
  - { offsetInCU: 0xBBAA, offset: 0x36885, size: 0x4, addend: 0x0, symName: __ZL11patchBuf124, symObjAddr: 0x184EAA, symBinAddr: 0x18BAD2, symSize: 0x0 }
  - { offsetInCU: 0xBBC3, offset: 0x3689E, size: 0x4, addend: 0x0, symName: __ZL12platforms102, symObjAddr: 0x4C60C, symBinAddr: 0x53234, symSize: 0x0 }
  - { offsetInCU: 0xBBDC, offset: 0x368B7, size: 0x4, addend: 0x0, symName: __ZL8file1219, symObjAddr: 0x184EAE, symBinAddr: 0x18BAD6, symSize: 0x0 }
  - { offsetInCU: 0xBC02, offset: 0x368DD, size: 0x4, addend: 0x0, symName: __ZL8file1220, symObjAddr: 0x1859C9, symBinAddr: 0x18C5F1, symSize: 0x0 }
  - { offsetInCU: 0xBC1B, offset: 0x368F6, size: 0x4, addend: 0x0, symName: __ZL8file1221, symObjAddr: 0x185B09, symBinAddr: 0x18C731, symSize: 0x0 }
  - { offsetInCU: 0xBC41, offset: 0x3691C, size: 0x4, addend: 0x0, symName: __ZL8file1222, symObjAddr: 0x18661F, symBinAddr: 0x18D247, symSize: 0x0 }
  - { offsetInCU: 0xBC67, offset: 0x36942, size: 0x4, addend: 0x0, symName: __ZL8file1223, symObjAddr: 0x18714E, symBinAddr: 0x18DD76, symSize: 0x0 }
  - { offsetInCU: 0xBC80, offset: 0x3695B, size: 0x4, addend: 0x0, symName: __ZL8file1224, symObjAddr: 0x187283, symBinAddr: 0x18DEAB, symSize: 0x0 }
  - { offsetInCU: 0xBC99, offset: 0x36974, size: 0x4, addend: 0x0, symName: __ZL8file1225, symObjAddr: 0x1873E0, symBinAddr: 0x18E008, symSize: 0x0 }
  - { offsetInCU: 0xBCB2, offset: 0x3698D, size: 0x4, addend: 0x0, symName: __ZL8file1226, symObjAddr: 0x18752C, symBinAddr: 0x18E154, symSize: 0x0 }
  - { offsetInCU: 0xBCCB, offset: 0x369A6, size: 0x4, addend: 0x0, symName: __ZL8file1227, symObjAddr: 0x18765E, symBinAddr: 0x18E286, symSize: 0x0 }
  - { offsetInCU: 0xBCE4, offset: 0x369BF, size: 0x4, addend: 0x0, symName: __ZL8file1228, symObjAddr: 0x1877A8, symBinAddr: 0x18E3D0, symSize: 0x0 }
  - { offsetInCU: 0xBCFD, offset: 0x369D8, size: 0x4, addend: 0x0, symName: __ZL8file1229, symObjAddr: 0x1878E8, symBinAddr: 0x18E510, symSize: 0x0 }
  - { offsetInCU: 0xBD16, offset: 0x369F1, size: 0x4, addend: 0x0, symName: __ZL10layouts102, symObjAddr: 0x4C6E8, symBinAddr: 0x53310, symSize: 0x0 }
  - { offsetInCU: 0xBD2F, offset: 0x36A0A, size: 0x4, addend: 0x0, symName: __ZL8file1230, symObjAddr: 0x187A2E, symBinAddr: 0x18E656, symSize: 0x0 }
  - { offsetInCU: 0xBD48, offset: 0x36A23, size: 0x4, addend: 0x0, symName: __ZL8file1231, symObjAddr: 0x187FFF, symBinAddr: 0x18EC27, symSize: 0x0 }
  - { offsetInCU: 0xBD61, offset: 0x36A3C, size: 0x4, addend: 0x0, symName: __ZL8file1232, symObjAddr: 0x188623, symBinAddr: 0x18F24B, symSize: 0x0 }
  - { offsetInCU: 0xBD87, offset: 0x36A62, size: 0x4, addend: 0x0, symName: __ZL8file1233, symObjAddr: 0x188BE6, symBinAddr: 0x18F80E, symSize: 0x0 }
  - { offsetInCU: 0xBDA0, offset: 0x36A7B, size: 0x4, addend: 0x0, symName: __ZL8file1234, symObjAddr: 0x18920B, symBinAddr: 0x18FE33, symSize: 0x0 }
  - { offsetInCU: 0xBDB9, offset: 0x36A94, size: 0x4, addend: 0x0, symName: __ZL8file1235, symObjAddr: 0x18982C, symBinAddr: 0x190454, symSize: 0x0 }
  - { offsetInCU: 0xBDD2, offset: 0x36AAD, size: 0x4, addend: 0x0, symName: __ZL8file1236, symObjAddr: 0x189E51, symBinAddr: 0x190A79, symSize: 0x0 }
  - { offsetInCU: 0xBDF8, offset: 0x36AD3, size: 0x4, addend: 0x0, symName: __ZL8file1237, symObjAddr: 0x18A706, symBinAddr: 0x19132E, symSize: 0x0 }
  - { offsetInCU: 0xBE1E, offset: 0x36AF9, size: 0x4, addend: 0x0, symName: __ZL8file1238, symObjAddr: 0x18AD2E, symBinAddr: 0x191956, symSize: 0x0 }
  - { offsetInCU: 0xBE37, offset: 0x36B12, size: 0x4, addend: 0x0, symName: __ZL8file1239, symObjAddr: 0x18B351, symBinAddr: 0x191F79, symSize: 0x0 }
  - { offsetInCU: 0xBE50, offset: 0x36B2B, size: 0x4, addend: 0x0, symName: __ZL8file1240, symObjAddr: 0x18B973, symBinAddr: 0x19259B, symSize: 0x0 }
  - { offsetInCU: 0xBE69, offset: 0x36B44, size: 0x4, addend: 0x0, symName: __ZL10patches102, symObjAddr: 0x1A2FC0, symBinAddr: 0x1A9B60, symSize: 0x0 }
  - { offsetInCU: 0xBE82, offset: 0x36B5D, size: 0x4, addend: 0x0, symName: __ZL11patchBuf125, symObjAddr: 0x18BF95, symBinAddr: 0x192BBD, symSize: 0x0 }
  - { offsetInCU: 0xBE9B, offset: 0x36B76, size: 0x4, addend: 0x0, symName: __ZL12platforms103, symObjAddr: 0x4C7C4, symBinAddr: 0x533EC, symSize: 0x0 }
  - { offsetInCU: 0xBEB4, offset: 0x36B8F, size: 0x4, addend: 0x0, symName: __ZL8file1241, symObjAddr: 0x18BF99, symBinAddr: 0x192BC1, symSize: 0x0 }
  - { offsetInCU: 0xBECD, offset: 0x36BA8, size: 0x4, addend: 0x0, symName: __ZL10layouts103, symObjAddr: 0x4C7D8, symBinAddr: 0x53400, symSize: 0x0 }
  - { offsetInCU: 0xBEE6, offset: 0x36BC1, size: 0x4, addend: 0x0, symName: __ZL8file1242, symObjAddr: 0x18C0DE, symBinAddr: 0x192D06, symSize: 0x0 }
  - { offsetInCU: 0xBF0C, offset: 0x36BE7, size: 0x4, addend: 0x0, symName: __ZL10patches103, symObjAddr: 0x1A3084, symBinAddr: 0x1A9C24, symSize: 0x0 }
  - { offsetInCU: 0xBF25, offset: 0x36C00, size: 0x4, addend: 0x0, symName: __ZL11patchBuf126, symObjAddr: 0x18C6B8, symBinAddr: 0x1932E0, symSize: 0x0 }
  - { offsetInCU: 0xBF3E, offset: 0x36C19, size: 0x4, addend: 0x0, symName: __ZL11revisions48, symObjAddr: 0x4C7EC, symBinAddr: 0x53414, symSize: 0x0 }
  - { offsetInCU: 0xBF57, offset: 0x36C32, size: 0x4, addend: 0x0, symName: __ZL12platforms104, symObjAddr: 0x4C7F0, symBinAddr: 0x53418, symSize: 0x0 }
  - { offsetInCU: 0xBF70, offset: 0x36C4B, size: 0x4, addend: 0x0, symName: __ZL8file1243, symObjAddr: 0x18C6BC, symBinAddr: 0x1932E4, symSize: 0x0 }
  - { offsetInCU: 0xBF89, offset: 0x36C64, size: 0x4, addend: 0x0, symName: __ZL8file1244, symObjAddr: 0x18C7EE, symBinAddr: 0x193416, symSize: 0x0 }
  - { offsetInCU: 0xBFA2, offset: 0x36C7D, size: 0x4, addend: 0x0, symName: __ZL8file1245, symObjAddr: 0x18C920, symBinAddr: 0x193548, symSize: 0x0 }
  - { offsetInCU: 0xBFBB, offset: 0x36C96, size: 0x4, addend: 0x0, symName: __ZL8file1246, symObjAddr: 0x18CA61, symBinAddr: 0x193689, symSize: 0x0 }
  - { offsetInCU: 0xBFD4, offset: 0x36CAF, size: 0x4, addend: 0x0, symName: __ZL8file1247, symObjAddr: 0x18CBAE, symBinAddr: 0x1937D6, symSize: 0x0 }
  - { offsetInCU: 0xBFED, offset: 0x36CC8, size: 0x4, addend: 0x0, symName: __ZL10layouts104, symObjAddr: 0x4C854, symBinAddr: 0x5347C, symSize: 0x0 }
  - { offsetInCU: 0xC006, offset: 0x36CE1, size: 0x4, addend: 0x0, symName: __ZL8file1248, symObjAddr: 0x18CCEF, symBinAddr: 0x193917, symSize: 0x0 }
  - { offsetInCU: 0xC02C, offset: 0x36D07, size: 0x4, addend: 0x0, symName: __ZL8file1249, symObjAddr: 0x18CFE6, symBinAddr: 0x193C0E, symSize: 0x0 }
  - { offsetInCU: 0xC045, offset: 0x36D20, size: 0x4, addend: 0x0, symName: __ZL8file1250, symObjAddr: 0x18D2DD, symBinAddr: 0x193F05, symSize: 0x0 }
  - { offsetInCU: 0xC06B, offset: 0x36D46, size: 0x4, addend: 0x0, symName: __ZL8file1251, symObjAddr: 0x18D93C, symBinAddr: 0x194564, symSize: 0x0 }
  - { offsetInCU: 0xC084, offset: 0x36D5F, size: 0x4, addend: 0x0, symName: __ZL8file1252, symObjAddr: 0x18DFCE, symBinAddr: 0x194BF6, symSize: 0x0 }
  - { offsetInCU: 0xC0AA, offset: 0x36D85, size: 0x4, addend: 0x0, symName: __ZL10patches104, symObjAddr: 0x1A3148, symBinAddr: 0x1A9CE8, symSize: 0x0 }
  - { offsetInCU: 0xC0C3, offset: 0x36D9E, size: 0x4, addend: 0x0, symName: __ZL11patchBuf127, symObjAddr: 0x18E62C, symBinAddr: 0x195254, symSize: 0x0 }
  - { offsetInCU: 0xC0DC, offset: 0x36DB7, size: 0x4, addend: 0x0, symName: __ZL11revisions49, symObjAddr: 0x4C8B8, symBinAddr: 0x534E0, symSize: 0x0 }
  - { offsetInCU: 0xC0F5, offset: 0x36DD0, size: 0x4, addend: 0x0, symName: __ZL12platforms105, symObjAddr: 0x4C8BC, symBinAddr: 0x534E4, symSize: 0x0 }
  - { offsetInCU: 0xC10E, offset: 0x36DE9, size: 0x4, addend: 0x0, symName: __ZL8file1253, symObjAddr: 0x18E630, symBinAddr: 0x195258, symSize: 0x0 }
  - { offsetInCU: 0xC134, offset: 0x36E0F, size: 0x4, addend: 0x0, symName: __ZL8file1254, symObjAddr: 0x18F12C, symBinAddr: 0x195D54, symSize: 0x0 }
  - { offsetInCU: 0xC14D, offset: 0x36E28, size: 0x4, addend: 0x0, symName: __ZL10layouts105, symObjAddr: 0x4C8E4, symBinAddr: 0x5350C, symSize: 0x0 }
  - { offsetInCU: 0xC166, offset: 0x36E41, size: 0x4, addend: 0x0, symName: __ZL8file1255, symObjAddr: 0x18F275, symBinAddr: 0x195E9D, symSize: 0x0 }
  - { offsetInCU: 0xC17F, offset: 0x36E5A, size: 0x4, addend: 0x0, symName: __ZL8file1256, symObjAddr: 0x18F380, symBinAddr: 0x195FA8, symSize: 0x0 }
  - { offsetInCU: 0xC198, offset: 0x36E73, size: 0x4, addend: 0x0, symName: __ZL10patches105, symObjAddr: 0x1A3244, symBinAddr: 0x1A9DE4, symSize: 0x0 }
  - { offsetInCU: 0xC1B1, offset: 0x36E8C, size: 0x4, addend: 0x0, symName: __ZL11patchBuf128, symObjAddr: 0x18F9DF, symBinAddr: 0x196607, symSize: 0x0 }
  - { offsetInCU: 0xC1CA, offset: 0x36EA5, size: 0x4, addend: 0x0, symName: __ZL14codecModAMDZEN, symObjAddr: 0x1E0524, symBinAddr: 0x1AA6E8, symSize: 0x0 }
  - { offsetInCU: 0xC1E3, offset: 0x36EBE, size: 0x4, addend: 0x0, symName: __ZL16codecModCreative, symObjAddr: 0x19C704, symBinAddr: 0x1A32A4, symSize: 0x0 }
  - { offsetInCU: 0xC208, offset: 0x36EE3, size: 0x4, addend: 0x0, symName: __ZL11revisions50, symObjAddr: 0x18F9E4, symBinAddr: 0x19660C, symSize: 0x0 }
  - { offsetInCU: 0xC221, offset: 0x36EFC, size: 0x4, addend: 0x0, symName: __ZL12platforms106, symObjAddr: 0x18F9E8, symBinAddr: 0x196610, symSize: 0x0 }
  - { offsetInCU: 0xC23A, offset: 0x36F15, size: 0x4, addend: 0x0, symName: __ZL8file1257, symObjAddr: 0x18FBF0, symBinAddr: 0x196818, symSize: 0x0 }
  - { offsetInCU: 0xC260, offset: 0x36F3B, size: 0x4, addend: 0x0, symName: __ZL8file1258, symObjAddr: 0x190D1C, symBinAddr: 0x197944, symSize: 0x0 }
  - { offsetInCU: 0xC286, offset: 0x36F61, size: 0x4, addend: 0x0, symName: __ZL8file1259, symObjAddr: 0x191D7D, symBinAddr: 0x1989A5, symSize: 0x0 }
  - { offsetInCU: 0xC2AC, offset: 0x36F87, size: 0x4, addend: 0x0, symName: __ZL8file1260, symObjAddr: 0x192DE2, symBinAddr: 0x199A0A, symSize: 0x0 }
  - { offsetInCU: 0xC2C5, offset: 0x36FA0, size: 0x4, addend: 0x0, symName: __ZL8file1261, symObjAddr: 0x193E43, symBinAddr: 0x19AA6B, symSize: 0x0 }
  - { offsetInCU: 0xC2DE, offset: 0x36FB9, size: 0x4, addend: 0x0, symName: __ZL8file1262, symObjAddr: 0x194EA8, symBinAddr: 0x19BAD0, symSize: 0x0 }
  - { offsetInCU: 0xC304, offset: 0x36FDF, size: 0x4, addend: 0x0, symName: __ZL10layouts106, symObjAddr: 0x18FAEC, symBinAddr: 0x196714, symSize: 0x0 }
  - { offsetInCU: 0xC31D, offset: 0x36FF8, size: 0x4, addend: 0x0, symName: __ZL8file1263, symObjAddr: 0x195EF7, symBinAddr: 0x19CB1F, symSize: 0x0 }
  - { offsetInCU: 0xC343, offset: 0x3701E, size: 0x4, addend: 0x0, symName: __ZL8file1264, symObjAddr: 0x196266, symBinAddr: 0x19CE8E, symSize: 0x0 }
  - { offsetInCU: 0xC35C, offset: 0x37037, size: 0x4, addend: 0x0, symName: __ZL8file1265, symObjAddr: 0x1965E9, symBinAddr: 0x19D211, symSize: 0x0 }
  - { offsetInCU: 0xC382, offset: 0x3705D, size: 0x4, addend: 0x0, symName: __ZL8file1266, symObjAddr: 0x196BD4, symBinAddr: 0x19D7FC, symSize: 0x0 }
  - { offsetInCU: 0xC3A8, offset: 0x37083, size: 0x4, addend: 0x0, symName: __ZL8file1267, symObjAddr: 0x1971AF, symBinAddr: 0x19DDD7, symSize: 0x0 }
  - { offsetInCU: 0xC3C1, offset: 0x3709C, size: 0x4, addend: 0x0, symName: __ZL8file1268, symObjAddr: 0x197789, symBinAddr: 0x19E3B1, symSize: 0x0 }
  - { offsetInCU: 0xC3DA, offset: 0x370B5, size: 0x4, addend: 0x0, symName: __ZL8file1269, symObjAddr: 0x197D5B, symBinAddr: 0x19E983, symSize: 0x0 }
  - { offsetInCU: 0xC3F3, offset: 0x370CE, size: 0x4, addend: 0x0, symName: __ZL8file1270, symObjAddr: 0x19832D, symBinAddr: 0x19EF55, symSize: 0x0 }
  - { offsetInCU: 0xC40C, offset: 0x370E7, size: 0x4, addend: 0x0, symName: __ZL8file1271, symObjAddr: 0x1988FF, symBinAddr: 0x19F527, symSize: 0x0 }
  - { offsetInCU: 0xC425, offset: 0x37100, size: 0x4, addend: 0x0, symName: __ZL8file1272, symObjAddr: 0x198EDC, symBinAddr: 0x19FB04, symSize: 0x0 }
  - { offsetInCU: 0xC43E, offset: 0x37119, size: 0x4, addend: 0x0, symName: __ZL8file1273, symObjAddr: 0x1994B9, symBinAddr: 0x1A00E1, symSize: 0x0 }
  - { offsetInCU: 0xC457, offset: 0x37132, size: 0x4, addend: 0x0, symName: __ZL8file1274, symObjAddr: 0x199A94, symBinAddr: 0x1A06BC, symSize: 0x0 }
  - { offsetInCU: 0xC47D, offset: 0x37158, size: 0x4, addend: 0x0, symName: __ZL8file1275, symObjAddr: 0x19A04A, symBinAddr: 0x1A0C72, symSize: 0x0 }
  - { offsetInCU: 0xC4A3, offset: 0x3717E, size: 0x4, addend: 0x0, symName: __ZL10patches106, symObjAddr: 0x1A3340, symBinAddr: 0x1A9EE0, symSize: 0x0 }
  - { offsetInCU: 0xC4BC, offset: 0x37197, size: 0x4, addend: 0x0, symName: __ZL11patchBuf129, symObjAddr: 0x19A639, symBinAddr: 0x1A1261, symSize: 0x0 }
  - { offsetInCU: 0xC4D5, offset: 0x371B0, size: 0x4, addend: 0x0, symName: __ZL11patchBuf130, symObjAddr: 0x19A63D, symBinAddr: 0x1A1265, symSize: 0x0 }
  - { offsetInCU: 0xC4EE, offset: 0x371C9, size: 0x4, addend: 0x0, symName: __ZL11patchBuf131, symObjAddr: 0x19A641, symBinAddr: 0x1A1269, symSize: 0x0 }
  - { offsetInCU: 0xC507, offset: 0x371E2, size: 0x4, addend: 0x0, symName: __ZL11patchBuf132, symObjAddr: 0x19A645, symBinAddr: 0x1A126D, symSize: 0x0 }
  - { offsetInCU: 0xC520, offset: 0x371FB, size: 0x4, addend: 0x0, symName: __ZL11patchBuf133, symObjAddr: 0x19A649, symBinAddr: 0x1A1271, symSize: 0x0 }
  - { offsetInCU: 0xC539, offset: 0x37214, size: 0x4, addend: 0x0, symName: __ZL16codecModSigmaTel, symObjAddr: 0x19C72C, symBinAddr: 0x1A32CC, symSize: 0x0 }
  - { offsetInCU: 0xC552, offset: 0x3722D, size: 0x4, addend: 0x0, symName: __ZL11revisions51, symObjAddr: 0x19A650, symBinAddr: 0x1A1278, symSize: 0x0 }
  - { offsetInCU: 0xC56B, offset: 0x37246, size: 0x4, addend: 0x0, symName: __ZL12platforms107, symObjAddr: 0x19A654, symBinAddr: 0x1A127C, symSize: 0x0 }
  - { offsetInCU: 0xC584, offset: 0x3725F, size: 0x4, addend: 0x0, symName: __ZL8file1276, symObjAddr: 0x19A6D4, symBinAddr: 0x1A12FC, symSize: 0x0 }
  - { offsetInCU: 0xC59D, offset: 0x37278, size: 0x4, addend: 0x0, symName: __ZL10layouts107, symObjAddr: 0x19A668, symBinAddr: 0x1A1290, symSize: 0x0 }
  - { offsetInCU: 0xC5B6, offset: 0x37291, size: 0x4, addend: 0x0, symName: __ZL8file1277, symObjAddr: 0x19A80C, symBinAddr: 0x1A1434, symSize: 0x0 }
  - { offsetInCU: 0xC5DC, offset: 0x372B7, size: 0x4, addend: 0x0, symName: __ZL10patches107, symObjAddr: 0x1A343C, symBinAddr: 0x1A9FDC, symSize: 0x0 }
  - { offsetInCU: 0xC601, offset: 0x372DC, size: 0x4, addend: 0x0, symName: __ZL11patchBuf134, symObjAddr: 0x19AA6B, symBinAddr: 0x1A1693, symSize: 0x0 }
  - { offsetInCU: 0xC61A, offset: 0x372F5, size: 0x4, addend: 0x0, symName: __ZL11patchBuf135, symObjAddr: 0x19AA6F, symBinAddr: 0x1A1697, symSize: 0x0 }
  - { offsetInCU: 0xC633, offset: 0x3730E, size: 0x4, addend: 0x0, symName: __ZL11patchBuf136, symObjAddr: 0x19AA73, symBinAddr: 0x1A169B, symSize: 0x0 }
  - { offsetInCU: 0xC64C, offset: 0x37327, size: 0x4, addend: 0x0, symName: __ZL11patchBuf137, symObjAddr: 0x19AA77, symBinAddr: 0x1A169F, symSize: 0x0 }
  - { offsetInCU: 0xC665, offset: 0x37340, size: 0x4, addend: 0x0, symName: __ZL11patchBuf138, symObjAddr: 0x19AA7B, symBinAddr: 0x1A16A3, symSize: 0x0 }
  - { offsetInCU: 0xC67E, offset: 0x37359, size: 0x4, addend: 0x0, symName: __ZL11patchBuf139, symObjAddr: 0x19AA7F, symBinAddr: 0x1A16A7, symSize: 0x0 }
  - { offsetInCU: 0xC697, offset: 0x37372, size: 0x4, addend: 0x0, symName: __ZL11patchBuf140, symObjAddr: 0x19AA83, symBinAddr: 0x1A16AB, symSize: 0x0 }
  - { offsetInCU: 0xC6B0, offset: 0x3738B, size: 0x4, addend: 0x0, symName: __ZL11patchBuf141, symObjAddr: 0x19AA87, symBinAddr: 0x1A16AF, symSize: 0x0 }
  - { offsetInCU: 0xC6C9, offset: 0x373A4, size: 0x4, addend: 0x0, symName: __ZL11patchBuf142, symObjAddr: 0x19AA8B, symBinAddr: 0x1A16B3, symSize: 0x0 }
  - { offsetInCU: 0xC6E2, offset: 0x373BD, size: 0x4, addend: 0x0, symName: __ZL11patchBuf143, symObjAddr: 0x19AA8F, symBinAddr: 0x1A16B7, symSize: 0x0 }
  - { offsetInCU: 0xC6FB, offset: 0x373D6, size: 0x4, addend: 0x0, symName: __ZL11revisions52, symObjAddr: 0x19A67C, symBinAddr: 0x1A12A4, symSize: 0x0 }
  - { offsetInCU: 0xC714, offset: 0x373EF, size: 0x4, addend: 0x0, symName: __ZL12platforms108, symObjAddr: 0x19A680, symBinAddr: 0x1A12A8, symSize: 0x0 }
  - { offsetInCU: 0xC72D, offset: 0x37408, size: 0x4, addend: 0x0, symName: __ZL8file1278, symObjAddr: 0x19AA93, symBinAddr: 0x1A16BB, symSize: 0x0 }
  - { offsetInCU: 0xC746, offset: 0x37421, size: 0x4, addend: 0x0, symName: __ZL10layouts108, symObjAddr: 0x19A694, symBinAddr: 0x1A12BC, symSize: 0x0 }
  - { offsetInCU: 0xC75F, offset: 0x3743A, size: 0x4, addend: 0x0, symName: __ZL8file1279, symObjAddr: 0x19ABD3, symBinAddr: 0x1A17FB, symSize: 0x0 }
  - { offsetInCU: 0xC785, offset: 0x37460, size: 0x4, addend: 0x0, symName: __ZL10patches108, symObjAddr: 0x1A3688, symBinAddr: 0x1AA228, symSize: 0x0 }
  - { offsetInCU: 0xC7AA, offset: 0x37485, size: 0x4, addend: 0x0, symName: __ZL11patchBuf144, symObjAddr: 0x19AE44, symBinAddr: 0x1A1A6C, symSize: 0x0 }
  - { offsetInCU: 0xC7C3, offset: 0x3749E, size: 0x4, addend: 0x0, symName: __ZL11revisions53, symObjAddr: 0x19A6A8, symBinAddr: 0x1A12D0, symSize: 0x0 }
  - { offsetInCU: 0xC7DC, offset: 0x374B7, size: 0x4, addend: 0x0, symName: __ZL12platforms109, symObjAddr: 0x19A6AC, symBinAddr: 0x1A12D4, symSize: 0x0 }
  - { offsetInCU: 0xC7F5, offset: 0x374D0, size: 0x4, addend: 0x0, symName: __ZL8file1280, symObjAddr: 0x19AE48, symBinAddr: 0x1A1A70, symSize: 0x0 }
  - { offsetInCU: 0xC80E, offset: 0x374E9, size: 0x4, addend: 0x0, symName: __ZL10layouts109, symObjAddr: 0x19A6C0, symBinAddr: 0x1A12E8, symSize: 0x0 }
  - { offsetInCU: 0xC827, offset: 0x37502, size: 0x4, addend: 0x0, symName: __ZL8file1281, symObjAddr: 0x19AF98, symBinAddr: 0x1A1BC0, symSize: 0x0 }
  - { offsetInCU: 0xC840, offset: 0x3751B, size: 0x4, addend: 0x0, symName: __ZL10patches109, symObjAddr: 0x1A389C, symBinAddr: 0x1AA43C, symSize: 0x0 }
  - { offsetInCU: 0xC859, offset: 0x37534, size: 0x4, addend: 0x0, symName: __ZL11patchBuf145, symObjAddr: 0x19B1F7, symBinAddr: 0x1A1E1F, symSize: 0x0 }
  - { offsetInCU: 0xC872, offset: 0x3754D, size: 0x4, addend: 0x0, symName: __ZL14codecModNVIDIA, symObjAddr: 0x1E0528, symBinAddr: 0x1AA6EC, symSize: 0x0 }
  - { offsetInCU: 0xC88B, offset: 0x37566, size: 0x4, addend: 0x0, symName: __ZL10patches110, symObjAddr: 0x19C864, symBinAddr: 0x1A3404, symSize: 0x0 }
  - { offsetInCU: 0xC8B0, offset: 0x3758B, size: 0x4, addend: 0x0, symName: __ZL11patchBuf146, symObjAddr: 0x19B1FB, symBinAddr: 0x1A1E23, symSize: 0x0 }
  - { offsetInCU: 0xC8C9, offset: 0x375A4, size: 0x4, addend: 0x0, symName: __ZL11patchBuf147, symObjAddr: 0x19B1FF, symBinAddr: 0x1A1E27, symSize: 0x0 }
  - { offsetInCU: 0xC8E2, offset: 0x375BD, size: 0x4, addend: 0x0, symName: __ZL10patches111, symObjAddr: 0x19C880, symBinAddr: 0x1A3420, symSize: 0x0 }
  - { offsetInCU: 0xC8FB, offset: 0x375D6, size: 0x4, addend: 0x0, symName: __ZL11patchBuf148, symObjAddr: 0x19B203, symBinAddr: 0x1A1E2B, symSize: 0x0 }
  - { offsetInCU: 0xC914, offset: 0x375EF, size: 0x4, addend: 0x0, symName: __ZL10patches112, symObjAddr: 0x19C89C, symBinAddr: 0x1A343C, symSize: 0x0 }
  - { offsetInCU: 0xC92D, offset: 0x37608, size: 0x4, addend: 0x0, symName: __ZL11patchBuf149, symObjAddr: 0x19B207, symBinAddr: 0x1A1E2F, symSize: 0x0 }
  - { offsetInCU: 0xC946, offset: 0x37621, size: 0x4, addend: 0x0, symName: __ZL10patches113, symObjAddr: 0x19C8B8, symBinAddr: 0x1A3458, symSize: 0x0 }
  - { offsetInCU: 0xC95F, offset: 0x3763A, size: 0x4, addend: 0x0, symName: __ZL11patchBuf150, symObjAddr: 0x19B20B, symBinAddr: 0x1A1E33, symSize: 0x0 }
  - { offsetInCU: 0xC978, offset: 0x37653, size: 0x4, addend: 0x0, symName: __ZL10patches114, symObjAddr: 0x19C8D4, symBinAddr: 0x1A3474, symSize: 0x0 }
  - { offsetInCU: 0xC991, offset: 0x3766C, size: 0x4, addend: 0x0, symName: __ZL11patchBuf151, symObjAddr: 0x19B20F, symBinAddr: 0x1A1E37, symSize: 0x0 }
  - { offsetInCU: 0xC9AA, offset: 0x37685, size: 0x4, addend: 0x0, symName: __ZL10patches115, symObjAddr: 0x19C8F0, symBinAddr: 0x1A3490, symSize: 0x0 }
  - { offsetInCU: 0xC9C3, offset: 0x3769E, size: 0x4, addend: 0x0, symName: __ZL11patchBuf152, symObjAddr: 0x19B213, symBinAddr: 0x1A1E3B, symSize: 0x0 }
  - { offsetInCU: 0xC9DC, offset: 0x376B7, size: 0x4, addend: 0x0, symName: __ZL10patches116, symObjAddr: 0x19C90C, symBinAddr: 0x1A34AC, symSize: 0x0 }
  - { offsetInCU: 0xC9F5, offset: 0x376D0, size: 0x4, addend: 0x0, symName: __ZL11patchBuf153, symObjAddr: 0x19B217, symBinAddr: 0x1A1E3F, symSize: 0x0 }
  - { offsetInCU: 0xCA0E, offset: 0x376E9, size: 0x4, addend: 0x0, symName: __ZL10patches117, symObjAddr: 0x19C928, symBinAddr: 0x1A34C8, symSize: 0x0 }
  - { offsetInCU: 0xCA27, offset: 0x37702, size: 0x4, addend: 0x0, symName: __ZL11patchBuf154, symObjAddr: 0x19B21B, symBinAddr: 0x1A1E43, symSize: 0x0 }
  - { offsetInCU: 0xCA40, offset: 0x3771B, size: 0x4, addend: 0x0, symName: __ZL11patchBuf155, symObjAddr: 0x19B21F, symBinAddr: 0x1A1E47, symSize: 0x0 }
  - { offsetInCU: 0xCA59, offset: 0x37734, size: 0x4, addend: 0x0, symName: __ZL10patches118, symObjAddr: 0x19C944, symBinAddr: 0x1A34E4, symSize: 0x0 }
  - { offsetInCU: 0xCA72, offset: 0x3774D, size: 0x4, addend: 0x0, symName: __ZL11patchBuf156, symObjAddr: 0x19B223, symBinAddr: 0x1A1E4B, symSize: 0x0 }
  - { offsetInCU: 0xCA8B, offset: 0x37766, size: 0x4, addend: 0x0, symName: __ZL10patches119, symObjAddr: 0x19C960, symBinAddr: 0x1A3500, symSize: 0x0 }
  - { offsetInCU: 0xCAA4, offset: 0x3777F, size: 0x4, addend: 0x0, symName: __ZL11patchBuf157, symObjAddr: 0x19B227, symBinAddr: 0x1A1E4F, symSize: 0x0 }
  - { offsetInCU: 0xCABD, offset: 0x37798, size: 0x4, addend: 0x0, symName: __ZL10patches120, symObjAddr: 0x19C97C, symBinAddr: 0x1A351C, symSize: 0x0 }
  - { offsetInCU: 0xCAD6, offset: 0x377B1, size: 0x4, addend: 0x0, symName: __ZL11patchBuf158, symObjAddr: 0x19B22B, symBinAddr: 0x1A1E53, symSize: 0x0 }
  - { offsetInCU: 0xCAEF, offset: 0x377CA, size: 0x4, addend: 0x0, symName: __ZL10patches121, symObjAddr: 0x19C998, symBinAddr: 0x1A3538, symSize: 0x0 }
  - { offsetInCU: 0xCB14, offset: 0x377EF, size: 0x4, addend: 0x0, symName: __ZL11patchBuf159, symObjAddr: 0x19B22F, symBinAddr: 0x1A1E57, symSize: 0x0 }
  - { offsetInCU: 0xCB2D, offset: 0x37808, size: 0x4, addend: 0x0, symName: __ZL11patchBuf160, symObjAddr: 0x19B233, symBinAddr: 0x1A1E5B, symSize: 0x0 }
  - { offsetInCU: 0xCB46, offset: 0x37821, size: 0x4, addend: 0x0, symName: __ZL11patchBuf161, symObjAddr: 0x19B237, symBinAddr: 0x1A1E5F, symSize: 0x0 }
  - { offsetInCU: 0xCB5F, offset: 0x3783A, size: 0x4, addend: 0x0, symName: __ZL11patchBuf162, symObjAddr: 0x19B23B, symBinAddr: 0x1A1E63, symSize: 0x0 }
  - { offsetInCU: 0xCB78, offset: 0x37853, size: 0x4, addend: 0x0, symName: __ZL11patchBuf163, symObjAddr: 0x19B23F, symBinAddr: 0x1A1E67, symSize: 0x0 }
  - { offsetInCU: 0xCB91, offset: 0x3786C, size: 0x4, addend: 0x0, symName: __ZL11patchBuf164, symObjAddr: 0x19B243, symBinAddr: 0x1A1E6B, symSize: 0x0 }
  - { offsetInCU: 0xCBAA, offset: 0x37885, size: 0x4, addend: 0x0, symName: __ZL11patchBuf165, symObjAddr: 0x19B247, symBinAddr: 0x1A1E6F, symSize: 0x0 }
  - { offsetInCU: 0xCBC3, offset: 0x3789E, size: 0x4, addend: 0x0, symName: __ZL11patchBuf166, symObjAddr: 0x19B24B, symBinAddr: 0x1A1E73, symSize: 0x0 }
  - { offsetInCU: 0xCBDC, offset: 0x378B7, size: 0x4, addend: 0x0, symName: __ZL10patches122, symObjAddr: 0x19CA08, symBinAddr: 0x1A35A8, symSize: 0x0 }
  - { offsetInCU: 0xCBF5, offset: 0x378D0, size: 0x4, addend: 0x0, symName: __ZL11patchBuf167, symObjAddr: 0x19B24F, symBinAddr: 0x1A1E77, symSize: 0x0 }
  - { offsetInCU: 0xCC0E, offset: 0x378E9, size: 0x4, addend: 0x0, symName: __ZL11patchBuf168, symObjAddr: 0x19B253, symBinAddr: 0x1A1E7B, symSize: 0x0 }
  - { offsetInCU: 0xCC27, offset: 0x37902, size: 0x4, addend: 0x0, symName: __ZL11patchBuf169, symObjAddr: 0x19B257, symBinAddr: 0x1A1E7F, symSize: 0x0 }
  - { offsetInCU: 0xCC40, offset: 0x3791B, size: 0x4, addend: 0x0, symName: __ZL10patches123, symObjAddr: 0x19CA78, symBinAddr: 0x1A3618, symSize: 0x0 }
  - { offsetInCU: 0xCC65, offset: 0x37940, size: 0x4, addend: 0x0, symName: __ZL11patchBuf170, symObjAddr: 0x19B25B, symBinAddr: 0x1A1E83, symSize: 0x0 }
  - { offsetInCU: 0xCC7E, offset: 0x37959, size: 0x4, addend: 0x0, symName: __ZL11patchBuf171, symObjAddr: 0x19B25F, symBinAddr: 0x1A1E87, symSize: 0x0 }
  - { offsetInCU: 0xCC97, offset: 0x37972, size: 0x4, addend: 0x0, symName: __ZL10patches124, symObjAddr: 0x19CAB0, symBinAddr: 0x1A3650, symSize: 0x0 }
  - { offsetInCU: 0xCCBC, offset: 0x37997, size: 0x4, addend: 0x0, symName: __ZL11patchBuf172, symObjAddr: 0x19B263, symBinAddr: 0x1A1E8B, symSize: 0x0 }
  - { offsetInCU: 0xCCD5, offset: 0x379B0, size: 0x4, addend: 0x0, symName: __ZL11patchBuf173, symObjAddr: 0x19B267, symBinAddr: 0x1A1E8F, symSize: 0x0 }
  - { offsetInCU: 0xCCEE, offset: 0x379C9, size: 0x4, addend: 0x0, symName: __ZL11patchBuf174, symObjAddr: 0x19B26B, symBinAddr: 0x1A1E93, symSize: 0x0 }
  - { offsetInCU: 0xCD07, offset: 0x379E2, size: 0x4, addend: 0x0, symName: __ZL11patchBuf175, symObjAddr: 0x19B271, symBinAddr: 0x1A1E99, symSize: 0x0 }
  - { offsetInCU: 0xCD20, offset: 0x379FB, size: 0x4, addend: 0x0, symName: __ZL11patchBuf176, symObjAddr: 0x19B277, symBinAddr: 0x1A1E9F, symSize: 0x0 }
  - { offsetInCU: 0xCD45, offset: 0x37A20, size: 0x4, addend: 0x0, symName: __ZL11patchBuf177, symObjAddr: 0x19B283, symBinAddr: 0x1A1EAB, symSize: 0x0 }
  - { offsetInCU: 0xCD5E, offset: 0x37A39, size: 0x4, addend: 0x0, symName: __ZL11patchBuf178, symObjAddr: 0x19B28F, symBinAddr: 0x1A1EB7, symSize: 0x0 }
  - { offsetInCU: 0xCD77, offset: 0x37A52, size: 0x4, addend: 0x0, symName: __ZL11patchBuf179, symObjAddr: 0x19B294, symBinAddr: 0x1A1EBC, symSize: 0x0 }
  - { offsetInCU: 0xCD90, offset: 0x37A6B, size: 0x4, addend: 0x0, symName: __ZL11patchBuf180, symObjAddr: 0x19B299, symBinAddr: 0x1A1EC1, symSize: 0x0 }
  - { offsetInCU: 0xCDB5, offset: 0x37A90, size: 0x4, addend: 0x0, symName: __ZL11patchBuf181, symObjAddr: 0x19B2A0, symBinAddr: 0x1A1EC8, symSize: 0x0 }
  - { offsetInCU: 0xCDCE, offset: 0x37AA9, size: 0x4, addend: 0x0, symName: __ZL10patches125, symObjAddr: 0x19CB3C, symBinAddr: 0x1A36DC, symSize: 0x0 }
  - { offsetInCU: 0xCDE7, offset: 0x37AC2, size: 0x4, addend: 0x0, symName: __ZL11patchBuf182, symObjAddr: 0x19B2A7, symBinAddr: 0x1A1ECF, symSize: 0x0 }
  - { offsetInCU: 0xCE00, offset: 0x37ADB, size: 0x4, addend: 0x0, symName: __ZL10patches126, symObjAddr: 0x19CBC8, symBinAddr: 0x1A3768, symSize: 0x0 }
  - { offsetInCU: 0xCE19, offset: 0x37AF4, size: 0x4, addend: 0x0, symName: __ZL11patchBuf183, symObjAddr: 0x19B2AB, symBinAddr: 0x1A1ED3, symSize: 0x0 }
  - { offsetInCU: 0xCE32, offset: 0x37B0D, size: 0x4, addend: 0x0, symName: __ZL10patches127, symObjAddr: 0x19CC54, symBinAddr: 0x1A37F4, symSize: 0x0 }
  - { offsetInCU: 0xCE4B, offset: 0x37B26, size: 0x4, addend: 0x0, symName: __ZL11patchBuf184, symObjAddr: 0x19B2AF, symBinAddr: 0x1A1ED7, symSize: 0x0 }
  - { offsetInCU: 0xCE64, offset: 0x37B3F, size: 0x4, addend: 0x0, symName: __ZL11patchBuf185, symObjAddr: 0x19B2B3, symBinAddr: 0x1A1EDB, symSize: 0x0 }
  - { offsetInCU: 0xCE7D, offset: 0x37B58, size: 0x4, addend: 0x0, symName: __ZL11patchBuf186, symObjAddr: 0x19B2B7, symBinAddr: 0x1A1EDF, symSize: 0x0 }
  - { offsetInCU: 0xCE96, offset: 0x37B71, size: 0x4, addend: 0x0, symName: __ZL11patchBuf187, symObjAddr: 0x19B2BB, symBinAddr: 0x1A1EE3, symSize: 0x0 }
  - { offsetInCU: 0xCEAF, offset: 0x37B8A, size: 0x4, addend: 0x0, symName: __ZL11patchBuf188, symObjAddr: 0x19B2C0, symBinAddr: 0x1A1EE8, symSize: 0x0 }
  - { offsetInCU: 0xCEC8, offset: 0x37BA3, size: 0x4, addend: 0x0, symName: __ZL11patchBuf189, symObjAddr: 0x19B2C5, symBinAddr: 0x1A1EED, symSize: 0x0 }
  - { offsetInCU: 0xCEE1, offset: 0x37BBC, size: 0x4, addend: 0x0, symName: __ZL10patches128, symObjAddr: 0x19CCE0, symBinAddr: 0x1A3880, symSize: 0x0 }
  - { offsetInCU: 0xCEFA, offset: 0x37BD5, size: 0x4, addend: 0x0, symName: __ZL11patchBuf190, symObjAddr: 0x19B2CA, symBinAddr: 0x1A1EF2, symSize: 0x0 }
  - { offsetInCU: 0xCF13, offset: 0x37BEE, size: 0x4, addend: 0x0, symName: __ZL11patchBuf191, symObjAddr: 0x19B2CE, symBinAddr: 0x1A1EF6, symSize: 0x0 }
  - { offsetInCU: 0xCF2C, offset: 0x37C07, size: 0x4, addend: 0x0, symName: __ZL11patchBuf192, symObjAddr: 0x19B2D2, symBinAddr: 0x1A1EFA, symSize: 0x0 }
  - { offsetInCU: 0xCF45, offset: 0x37C20, size: 0x4, addend: 0x0, symName: __ZL10patches129, symObjAddr: 0x19CD18, symBinAddr: 0x1A38B8, symSize: 0x0 }
  - { offsetInCU: 0xCF5E, offset: 0x37C39, size: 0x4, addend: 0x0, symName: __ZL11patchBuf193, symObjAddr: 0x19B2D6, symBinAddr: 0x1A1EFE, symSize: 0x0 }
  - { offsetInCU: 0xCF77, offset: 0x37C52, size: 0x4, addend: 0x0, symName: __ZL11patchBuf194, symObjAddr: 0x19B2DA, symBinAddr: 0x1A1F02, symSize: 0x0 }
  - { offsetInCU: 0xCF90, offset: 0x37C6B, size: 0x4, addend: 0x0, symName: __ZL11patchBuf195, symObjAddr: 0x19B2DE, symBinAddr: 0x1A1F06, symSize: 0x0 }
  - { offsetInCU: 0xCFA9, offset: 0x37C84, size: 0x4, addend: 0x0, symName: __ZL11patchBuf196, symObjAddr: 0x19B2E2, symBinAddr: 0x1A1F0A, symSize: 0x0 }
  - { offsetInCU: 0xCFC2, offset: 0x37C9D, size: 0x4, addend: 0x0, symName: __ZL10patches130, symObjAddr: 0x19CD50, symBinAddr: 0x1A38F0, symSize: 0x0 }
  - { offsetInCU: 0xCFDB, offset: 0x37CB6, size: 0x4, addend: 0x0, symName: __ZL11patchBuf197, symObjAddr: 0x19B2E6, symBinAddr: 0x1A1F0E, symSize: 0x0 }
  - { offsetInCU: 0xCFF4, offset: 0x37CCF, size: 0x4, addend: 0x0, symName: __ZL11patchBuf198, symObjAddr: 0x19B2EA, symBinAddr: 0x1A1F12, symSize: 0x0 }
  - { offsetInCU: 0xD00D, offset: 0x37CE8, size: 0x4, addend: 0x0, symName: __ZL10patches131, symObjAddr: 0x19CDC0, symBinAddr: 0x1A3960, symSize: 0x0 }
  - { offsetInCU: 0xD026, offset: 0x37D01, size: 0x4, addend: 0x0, symName: __ZL11patchBuf199, symObjAddr: 0x19B2EE, symBinAddr: 0x1A1F16, symSize: 0x0 }
  - { offsetInCU: 0xD03F, offset: 0x37D1A, size: 0x4, addend: 0x0, symName: __ZL11patchBuf200, symObjAddr: 0x19B2F2, symBinAddr: 0x1A1F1A, symSize: 0x0 }
  - { offsetInCU: 0xD058, offset: 0x37D33, size: 0x4, addend: 0x0, symName: __ZL10patches132, symObjAddr: 0x19CDF8, symBinAddr: 0x1A3998, symSize: 0x0 }
  - { offsetInCU: 0xD071, offset: 0x37D4C, size: 0x4, addend: 0x0, symName: __ZL11patchBuf201, symObjAddr: 0x19B2F6, symBinAddr: 0x1A1F1E, symSize: 0x0 }
  - { offsetInCU: 0xD096, offset: 0x37D71, size: 0x4, addend: 0x0, symName: __ZL11patchBuf202, symObjAddr: 0x19B301, symBinAddr: 0x1A1F29, symSize: 0x0 }
  - { offsetInCU: 0xD0AF, offset: 0x37D8A, size: 0x4, addend: 0x0, symName: __ZL10patches133, symObjAddr: 0x19CE14, symBinAddr: 0x1A39B4, symSize: 0x0 }
  - { offsetInCU: 0xD0C8, offset: 0x37DA3, size: 0x4, addend: 0x0, symName: __ZL11patchBuf203, symObjAddr: 0x19B30C, symBinAddr: 0x1A1F34, symSize: 0x0 }
  - { offsetInCU: 0xD0E1, offset: 0x37DBC, size: 0x4, addend: 0x0, symName: __ZL11patchBuf204, symObjAddr: 0x19B310, symBinAddr: 0x1A1F38, symSize: 0x0 }
  - { offsetInCU: 0xD0FA, offset: 0x37DD5, size: 0x4, addend: 0x0, symName: __ZL11patchBuf205, symObjAddr: 0x19B314, symBinAddr: 0x1A1F3C, symSize: 0x0 }
  - { offsetInCU: 0xD113, offset: 0x37DEE, size: 0x4, addend: 0x0, symName: __ZL11patchBuf206, symObjAddr: 0x19B318, symBinAddr: 0x1A1F40, symSize: 0x0 }
  - { offsetInCU: 0xD12C, offset: 0x37E07, size: 0x4, addend: 0x0, symName: __ZL11patchBuf207, symObjAddr: 0x19B31C, symBinAddr: 0x1A1F44, symSize: 0x0 }
  - { offsetInCU: 0xD145, offset: 0x37E20, size: 0x4, addend: 0x0, symName: __ZL11patchBuf208, symObjAddr: 0x19B320, symBinAddr: 0x1A1F48, symSize: 0x0 }
  - { offsetInCU: 0xD15E, offset: 0x37E39, size: 0x4, addend: 0x0, symName: __ZL10patches134, symObjAddr: 0x19CE84, symBinAddr: 0x1A3A24, symSize: 0x0 }
  - { offsetInCU: 0xD177, offset: 0x37E52, size: 0x4, addend: 0x0, symName: __ZL10patches135, symObjAddr: 0x19CEBC, symBinAddr: 0x1A3A5C, symSize: 0x0 }
  - { offsetInCU: 0xD190, offset: 0x37E6B, size: 0x4, addend: 0x0, symName: __ZL10patches136, symObjAddr: 0x19CED8, symBinAddr: 0x1A3A78, symSize: 0x0 }
  - { offsetInCU: 0xD1A9, offset: 0x37E84, size: 0x4, addend: 0x0, symName: __ZL11patchBuf209, symObjAddr: 0x19B324, symBinAddr: 0x1A1F4C, symSize: 0x0 }
  - { offsetInCU: 0xD1C2, offset: 0x37E9D, size: 0x4, addend: 0x0, symName: __ZL11patchBuf210, symObjAddr: 0x19B328, symBinAddr: 0x1A1F50, symSize: 0x0 }
  - { offsetInCU: 0xD1DB, offset: 0x37EB6, size: 0x4, addend: 0x0, symName: __ZL11patchBuf211, symObjAddr: 0x19B32C, symBinAddr: 0x1A1F54, symSize: 0x0 }
  - { offsetInCU: 0xD1F4, offset: 0x37ECF, size: 0x4, addend: 0x0, symName: __ZL11patchBuf212, symObjAddr: 0x19B330, symBinAddr: 0x1A1F58, symSize: 0x0 }
  - { offsetInCU: 0xD20D, offset: 0x37EE8, size: 0x4, addend: 0x0, symName: __ZL10patches137, symObjAddr: 0x19CF10, symBinAddr: 0x1A3AB0, symSize: 0x0 }
  - { offsetInCU: 0xD226, offset: 0x37F01, size: 0x4, addend: 0x0, symName: __ZL11patchBuf213, symObjAddr: 0x19B334, symBinAddr: 0x1A1F5C, symSize: 0x0 }
  - { offsetInCU: 0xD23F, offset: 0x37F1A, size: 0x4, addend: 0x0, symName: __ZL10patches138, symObjAddr: 0x19CF2C, symBinAddr: 0x1A3ACC, symSize: 0x0 }
  - { offsetInCU: 0xD258, offset: 0x37F33, size: 0x4, addend: 0x0, symName: __ZL10patches139, symObjAddr: 0x19CF64, symBinAddr: 0x1A3B04, symSize: 0x0 }
  - { offsetInCU: 0xD271, offset: 0x37F4C, size: 0x4, addend: 0x0, symName: __ZL11patchBuf214, symObjAddr: 0x19B33F, symBinAddr: 0x1A1F67, symSize: 0x0 }
  - { offsetInCU: 0xD28A, offset: 0x37F65, size: 0x4, addend: 0x0, symName: __ZL11patchBuf215, symObjAddr: 0x19B343, symBinAddr: 0x1A1F6B, symSize: 0x0 }
  - { offsetInCU: 0xD2A3, offset: 0x37F7E, size: 0x4, addend: 0x0, symName: __ZL10patches140, symObjAddr: 0x19CF9C, symBinAddr: 0x1A3B3C, symSize: 0x0 }
  - { offsetInCU: 0xD2BC, offset: 0x37F97, size: 0x4, addend: 0x0, symName: __ZL11patchBuf216, symObjAddr: 0x19B347, symBinAddr: 0x1A1F6F, symSize: 0x0 }
  - { offsetInCU: 0xD2D5, offset: 0x37FB0, size: 0x4, addend: 0x0, symName: __ZL11patchBuf217, symObjAddr: 0x19B34B, symBinAddr: 0x1A1F73, symSize: 0x0 }
  - { offsetInCU: 0xD2EE, offset: 0x37FC9, size: 0x4, addend: 0x0, symName: __ZL10patches141, symObjAddr: 0x19CFD4, symBinAddr: 0x1A3B74, symSize: 0x0 }
  - { offsetInCU: 0xD307, offset: 0x37FE2, size: 0x4, addend: 0x0, symName: __ZL10patches142, symObjAddr: 0x19CFF0, symBinAddr: 0x1A3B90, symSize: 0x0 }
  - { offsetInCU: 0xD320, offset: 0x37FFB, size: 0x4, addend: 0x0, symName: __ZL11patchBuf218, symObjAddr: 0x19B34F, symBinAddr: 0x1A1F77, symSize: 0x0 }
  - { offsetInCU: 0xD339, offset: 0x38014, size: 0x4, addend: 0x0, symName: __ZL11patchBuf219, symObjAddr: 0x19B353, symBinAddr: 0x1A1F7B, symSize: 0x0 }
  - { offsetInCU: 0xD352, offset: 0x3802D, size: 0x4, addend: 0x0, symName: __ZL10patches143, symObjAddr: 0x19D028, symBinAddr: 0x1A3BC8, symSize: 0x0 }
  - { offsetInCU: 0xD36B, offset: 0x38046, size: 0x4, addend: 0x0, symName: __ZL10patches144, symObjAddr: 0x19D044, symBinAddr: 0x1A3BE4, symSize: 0x0 }
  - { offsetInCU: 0xD384, offset: 0x3805F, size: 0x4, addend: 0x0, symName: __ZL10patches145, symObjAddr: 0x19D060, symBinAddr: 0x1A3C00, symSize: 0x0 }
  - { offsetInCU: 0xD39D, offset: 0x38078, size: 0x4, addend: 0x0, symName: __ZL10patches146, symObjAddr: 0x19D07C, symBinAddr: 0x1A3C1C, symSize: 0x0 }
  - { offsetInCU: 0xD3B6, offset: 0x38091, size: 0x4, addend: 0x0, symName: __ZL11patchBuf220, symObjAddr: 0x19B357, symBinAddr: 0x1A1F7F, symSize: 0x0 }
  - { offsetInCU: 0xD3CF, offset: 0x380AA, size: 0x4, addend: 0x0, symName: __ZL11patchBuf221, symObjAddr: 0x19B35B, symBinAddr: 0x1A1F83, symSize: 0x0 }
  - { offsetInCU: 0xD3E8, offset: 0x380C3, size: 0x4, addend: 0x0, symName: __ZL10patches147, symObjAddr: 0x19D0B4, symBinAddr: 0x1A3C54, symSize: 0x0 }
  - { offsetInCU: 0xD401, offset: 0x380DC, size: 0x4, addend: 0x0, symName: __ZL10patches148, symObjAddr: 0x19D0D0, symBinAddr: 0x1A3C70, symSize: 0x0 }
  - { offsetInCU: 0xD41A, offset: 0x380F5, size: 0x4, addend: 0x0, symName: __ZL11patchBuf222, symObjAddr: 0x19B35F, symBinAddr: 0x1A1F87, symSize: 0x0 }
  - { offsetInCU: 0xD433, offset: 0x3810E, size: 0x4, addend: 0x0, symName: __ZL10patches149, symObjAddr: 0x19D0EC, symBinAddr: 0x1A3C8C, symSize: 0x0 }
  - { offsetInCU: 0xD44C, offset: 0x38127, size: 0x4, addend: 0x0, symName: __ZL10patches150, symObjAddr: 0x19D108, symBinAddr: 0x1A3CA8, symSize: 0x0 }
  - { offsetInCU: 0xD465, offset: 0x38140, size: 0x4, addend: 0x0, symName: __ZL10patches151, symObjAddr: 0x19D124, symBinAddr: 0x1A3CC4, symSize: 0x0 }
...
