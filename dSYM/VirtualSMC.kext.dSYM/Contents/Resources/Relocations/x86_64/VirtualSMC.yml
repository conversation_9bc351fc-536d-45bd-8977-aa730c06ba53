---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/work/VirtualSMC/VirtualSMC/build/Release/VirtualSMC.kext/Contents/MacOS/VirtualSMC'
relocations:
  - { offsetInCU: 0x10, offset: 0x10, size: 0x8, addend: 0x0, symName: _ioTrap<PERSON>and<PERSON>, symObjAddr: 0x0, symBinAddr: 0x10D0, symSize: 0x3C }
  - { offsetInCU: 0x35, offset: 0x35, size: 0x8, addend: 0x0, symName: _ioTrapHandler, symObjAddr: 0x0, symBinAddr: 0x10D0, symSize: 0x3C }
  - { offsetInCU: 0x35, offset: 0x69, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0xA810, symBinAddr: 0xE1D8, symSize: 0x0 }
  - { offsetInCU: 0x206, offset: 0x23A, size: 0x8, addend: 0x0, symName: __realmain, symObjAddr: 0xA8D8, symBinAddr: 0xE2A0, symSize: 0x0 }
  - { offsetInCU: 0x21C, offset: 0x250, size: 0x8, addend: 0x0, symName: __antimain, symObjAddr: 0xA8E0, symBinAddr: 0xE2A8, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0xD67, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueKEY10readAccessEv, symObjAddr: 0x0, symBinAddr: 0x1140, symSize: 0x120 }
  - { offsetInCU: 0x18D38, offset: 0x19A78, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueKEY10readAccessEv, symObjAddr: 0x0, symBinAddr: 0x1140, symSize: 0x120 }
  - { offsetInCU: 0x18E12, offset: 0x19B52, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValue11writeAccessEv, symObjAddr: 0x120, symBinAddr: 0x1260, symSize: 0x10 }
  - { offsetInCU: 0x18E41, offset: 0x19B81, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueKEYD1Ev, symObjAddr: 0x130, symBinAddr: 0x1270, symSize: 0x10 }
  - { offsetInCU: 0x18E76, offset: 0x19BB6, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueKEYD0Ev, symObjAddr: 0x140, symBinAddr: 0x1280, symSize: 0x10 }
  - { offsetInCU: 0x191A1, offset: 0x19EE1, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueCLKT10readAccessEv, symObjAddr: 0x150, symBinAddr: 0x1290, symSize: 0xA0 }
  - { offsetInCU: 0x194A9, offset: 0x1A1E9, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueCLKT6updateEPKh, symObjAddr: 0x1F0, symBinAddr: 0x1330, symSize: 0x70 }
  - { offsetInCU: 0x1978A, offset: 0x1A4CA, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueCLKTD1Ev, symObjAddr: 0x260, symBinAddr: 0x13A0, symSize: 0x10 }
  - { offsetInCU: 0x197BF, offset: 0x1A4FF, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueCLKTD0Ev, symObjAddr: 0x270, symBinAddr: 0x13B0, symSize: 0x10 }
  - { offsetInCU: 0x1999E, offset: 0x1A6DE, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueCLWK10readAccessEv, symObjAddr: 0x280, symBinAddr: 0x13C0, symSize: 0xE0 }
  - { offsetInCU: 0x19A67, offset: 0x1A7A7, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueCLWK6updateEPKh, symObjAddr: 0x360, symBinAddr: 0x14A0, symSize: 0x30 }
  - { offsetInCU: 0x19AAD, offset: 0x1A7ED, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueCLWKD1Ev, symObjAddr: 0x390, symBinAddr: 0x14D0, symSize: 0x10 }
  - { offsetInCU: 0x19AE2, offset: 0x1A822, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueCLWKD0Ev, symObjAddr: 0x3A0, symBinAddr: 0x14E0, symSize: 0x10 }
  - { offsetInCU: 0x19B1A, offset: 0x1A85A, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValue10readAccessEv, symObjAddr: 0x3B0, symBinAddr: 0x14F0, symSize: 0x10 }
  - { offsetInCU: 0x19B49, offset: 0x1A889, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueKPPW6updateEPKh, symObjAddr: 0x3C0, symBinAddr: 0x1500, symSize: 0x50 }
  - { offsetInCU: 0x19B93, offset: 0x1A8D3, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueKPPWD1Ev, symObjAddr: 0x410, symBinAddr: 0x1550, symSize: 0x10 }
  - { offsetInCU: 0x19BC8, offset: 0x1A908, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueKPPWD0Ev, symObjAddr: 0x420, symBinAddr: 0x1560, symSize: 0x10 }
  - { offsetInCU: 0x19D1C, offset: 0x1AA5C, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueNum10readAccessEv, symObjAddr: 0x430, symBinAddr: 0x1570, symSize: 0x10 }
  - { offsetInCU: 0x19D7E, offset: 0x1AABE, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueNum6updateEPKh, symObjAddr: 0x440, symBinAddr: 0x1580, symSize: 0x20 }
  - { offsetInCU: 0x19D86, offset: 0x1AAC6, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueNumD1Ev, symObjAddr: 0x460, symBinAddr: 0x15A0, symSize: 0x10 }
  - { offsetInCU: 0x19DFB, offset: 0x1AB3B, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueNumD1Ev, symObjAddr: 0x460, symBinAddr: 0x15A0, symSize: 0x10 }
  - { offsetInCU: 0x19E30, offset: 0x1AB70, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueNumD0Ev, symObjAddr: 0x470, symBinAddr: 0x15B0, symSize: 0x10 }
  - { offsetInCU: 0x19F56, offset: 0x1AC96, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueLDLG6updateEPKh, symObjAddr: 0x480, symBinAddr: 0x15C0, symSize: 0x60 }
  - { offsetInCU: 0x19FB7, offset: 0x1ACF7, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueLDLGD1Ev, symObjAddr: 0x4E0, symBinAddr: 0x1620, symSize: 0x10 }
  - { offsetInCU: 0x19FEC, offset: 0x1AD2C, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueLDLGD0Ev, symObjAddr: 0x4F0, symBinAddr: 0x1630, symSize: 0x10 }
  - { offsetInCU: 0x1A024, offset: 0x1AD64, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueHBKP6updateEPKh, symObjAddr: 0x500, symBinAddr: 0x1640, symSize: 0x510 }
  - { offsetInCU: 0x1A452, offset: 0x1B192, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueHBKPD1Ev, symObjAddr: 0xA10, symBinAddr: 0x1B50, symSize: 0x10 }
  - { offsetInCU: 0x1A487, offset: 0x1B1C7, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueHBKPD0Ev, symObjAddr: 0xA20, symBinAddr: 0x1B60, symSize: 0x10 }
  - { offsetInCU: 0x1A57E, offset: 0x1B2BE, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueNTOK6updateEPKh, symObjAddr: 0xA30, symBinAddr: 0x1B70, symSize: 0x50 }
  - { offsetInCU: 0x1A5D7, offset: 0x1B317, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueNTOKD1Ev, symObjAddr: 0xA80, symBinAddr: 0x1BC0, symSize: 0x10 }
  - { offsetInCU: 0x1A60C, offset: 0x1B34C, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueNTOKD0Ev, symObjAddr: 0xA90, symBinAddr: 0x1BD0, symSize: 0x10 }
  - { offsetInCU: 0x1A775, offset: 0x1B4B5, size: 0x8, addend: 0x0, symName: __ZN20VirtualSMCValueTimer10readAccessEv, symObjAddr: 0xAA0, symBinAddr: 0x1BE0, symSize: 0xD0 }
  - { offsetInCU: 0x1A9B5, offset: 0x1B6F5, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueNATi6updateEPKh, symObjAddr: 0xB70, symBinAddr: 0x1CB0, symSize: 0x30 }
  - { offsetInCU: 0x1AA01, offset: 0x1B741, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueNATiD1Ev, symObjAddr: 0xBA0, symBinAddr: 0x1CE0, symSize: 0x10 }
  - { offsetInCU: 0x1AA36, offset: 0x1B776, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueNATiD0Ev, symObjAddr: 0xBB0, symBinAddr: 0x1CF0, symSize: 0x10 }
  - { offsetInCU: 0x1AB7B, offset: 0x1B8BB, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueNATJ6updateEPKh, symObjAddr: 0xBC0, symBinAddr: 0x1D00, symSize: 0xF0 }
  - { offsetInCU: 0x1AC79, offset: 0x1B9B9, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueNATJD1Ev, symObjAddr: 0xCB0, symBinAddr: 0x1DF0, symSize: 0x10 }
  - { offsetInCU: 0x1ACAE, offset: 0x1B9EE, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueNATJD0Ev, symObjAddr: 0xCC0, symBinAddr: 0x1E00, symSize: 0x10 }
  - { offsetInCU: 0x1ADA5, offset: 0x1BAE5, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueOSWD6updateEPKh, symObjAddr: 0xCD0, symBinAddr: 0x1E10, symSize: 0x120 }
  - { offsetInCU: 0x1AED7, offset: 0x1BC17, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueOSWDD1Ev, symObjAddr: 0xDF0, symBinAddr: 0x1F30, symSize: 0x10 }
  - { offsetInCU: 0x1AF0C, offset: 0x1BC4C, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueOSWDD0Ev, symObjAddr: 0xE00, symBinAddr: 0x1F40, symSize: 0x10 }
  - { offsetInCU: 0x1B016, offset: 0x1BD56, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueEVCT6updateEPKh, symObjAddr: 0xE10, symBinAddr: 0x1F50, symSize: 0x10 }
  - { offsetInCU: 0x1B055, offset: 0x1BD95, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueEVCTD1Ev, symObjAddr: 0xE20, symBinAddr: 0x1F60, symSize: 0x10 }
  - { offsetInCU: 0x1B08A, offset: 0x1BDCA, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueEVCTD0Ev, symObjAddr: 0xE30, symBinAddr: 0x1F70, symSize: 0x10 }
  - { offsetInCU: 0x1B181, offset: 0x1BEC1, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueEFBS6updateEPKh, symObjAddr: 0xE40, symBinAddr: 0x1F80, symSize: 0x10 }
  - { offsetInCU: 0x1B1C0, offset: 0x1BF00, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueEFBSD1Ev, symObjAddr: 0xE50, symBinAddr: 0x1F90, symSize: 0x10 }
  - { offsetInCU: 0x1B1F5, offset: 0x1BF35, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueEFBSD0Ev, symObjAddr: 0xE60, symBinAddr: 0x1FA0, symSize: 0x10 }
  - { offsetInCU: 0x1B2E2, offset: 0x1C022, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueDUSR6updateEPKh, symObjAddr: 0xE70, symBinAddr: 0x1FB0, symSize: 0x20 }
  - { offsetInCU: 0x1B325, offset: 0x1C065, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueDUSRD1Ev, symObjAddr: 0xE90, symBinAddr: 0x1FD0, symSize: 0x10 }
  - { offsetInCU: 0x1B35A, offset: 0x1C09A, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueDUSRD0Ev, symObjAddr: 0xEA0, symBinAddr: 0x1FE0, symSize: 0x10 }
  - { offsetInCU: 0x1B392, offset: 0x1C0D2, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValueC2Ev, symObjAddr: 0xEB0, symBinAddr: 0x1FF0, symSize: 0xB0 }
  - { offsetInCU: 0x1B498, offset: 0x1C1D8, size: 0x8, addend: 0x0, symName: __ZN23VirtualSMCValueVariableD1Ev, symObjAddr: 0xF60, symBinAddr: 0x20A0, symSize: 0x10 }
  - { offsetInCU: 0x1B4CB, offset: 0x1C20B, size: 0x8, addend: 0x0, symName: __ZN23VirtualSMCValueVariableD0Ev, symObjAddr: 0xF70, symBinAddr: 0x20B0, symSize: 0x10 }
  - { offsetInCU: 0x1B549, offset: 0x1C289, size: 0x8, addend: 0x0, symName: __ZN23VirtualSMCValueVariable8withDataEPKhhjh14SerializeLevel, symObjAddr: 0xF80, symBinAddr: 0x20C0, symSize: 0xA0 }
  - { offsetInCU: 0x1B6A0, offset: 0x1C3E0, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueCLKT9withDeltaEi, symObjAddr: 0x1020, symBinAddr: 0x2160, symSize: 0x60 }
  - { offsetInCU: 0x1B7EE, offset: 0x1C52E, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueCLWK12withLastWakeEPy, symObjAddr: 0x1080, symBinAddr: 0x21C0, symSize: 0x80 }
  - { offsetInCU: 0x1B93F, offset: 0x1C67F, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueKPST12withUnlockedEb, symObjAddr: 0x1100, symBinAddr: 0x2240, symSize: 0x70 }
  - { offsetInCU: 0x1BA6D, offset: 0x1C7AD, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueKPSTD1Ev, symObjAddr: 0x1170, symBinAddr: 0x22B0, symSize: 0x10 }
  - { offsetInCU: 0x1BAA2, offset: 0x1C7E2, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueKPSTD0Ev, symObjAddr: 0x1180, symBinAddr: 0x22C0, symSize: 0x10 }
  - { offsetInCU: 0x1BB22, offset: 0x1C862, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueKPPW8withKPSTEP19VirtualSMCValueKPSTN7SMCInfo10GenerationE, symObjAddr: 0x1190, symBinAddr: 0x22D0, symSize: 0x90 }
  - { offsetInCU: 0x1BC4A, offset: 0x1C98A, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueAdrD1Ev, symObjAddr: 0x1220, symBinAddr: 0x2360, symSize: 0x10 }
  - { offsetInCU: 0x1BC7F, offset: 0x1C9BF, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueAdrD0Ev, symObjAddr: 0x1230, symBinAddr: 0x2370, symSize: 0x10 }
  - { offsetInCU: 0x1BCFF, offset: 0x1CA3F, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueLDLG13withEasterEggEhPKc, symObjAddr: 0x1240, symBinAddr: 0x2380, symSize: 0x70 }
  - { offsetInCU: 0x1BE67, offset: 0x1CBA7, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueNTOK9withStateEb, symObjAddr: 0x12B0, symBinAddr: 0x23F0, symSize: 0x90 }
  - { offsetInCU: 0x1C003, offset: 0x1CD43, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueNATi13withCountdownEt, symObjAddr: 0x1340, symBinAddr: 0x2480, symSize: 0x70 }
  - { offsetInCU: 0x1C180, offset: 0x1CEC0, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueNATJ8withNATiEP19VirtualSMCValueNATi, symObjAddr: 0x13B0, symBinAddr: 0x24F0, symSize: 0x70 }
  - { offsetInCU: 0x1C2E9, offset: 0x1D029, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueOSWD13withCountdownEt, symObjAddr: 0x1420, symBinAddr: 0x2560, symSize: 0x70 }
  - { offsetInCU: 0x1C46F, offset: 0x1D1AF, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueEVRD10withEventsEPh, symObjAddr: 0x1490, symBinAddr: 0x25D0, symSize: 0x60 }
  - { offsetInCU: 0x1C57A, offset: 0x1D2BA, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueEVRDD1Ev, symObjAddr: 0x14F0, symBinAddr: 0x2630, symSize: 0x10 }
  - { offsetInCU: 0x1C5AF, offset: 0x1D2EF, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueEVRDD0Ev, symObjAddr: 0x1500, symBinAddr: 0x2640, symSize: 0x10 }
  - { offsetInCU: 0x1C62F, offset: 0x1D36F, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueEVCT10withBufferEP19VirtualSMCValueEVRD, symObjAddr: 0x1510, symBinAddr: 0x2650, symSize: 0x60 }
  - { offsetInCU: 0x1C785, offset: 0x1D4C5, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueEFBS14withBootStatusEh, symObjAddr: 0x1570, symBinAddr: 0x26B0, symSize: 0x70 }
  - { offsetInCU: 0x1C8DA, offset: 0x1D61A, size: 0x8, addend: 0x0, symName: __ZN19VirtualSMCValueDUSR6createEv, symObjAddr: 0x15E0, symBinAddr: 0x2720, symSize: 0x60 }
  - { offsetInCU: 0x1CA1B, offset: 0x1D75B, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueOSK9withIndexEh, symObjAddr: 0x1640, symBinAddr: 0x2780, symSize: 0x270 }
  - { offsetInCU: 0x1CBAC, offset: 0x1D8EC, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueOSKD1Ev, symObjAddr: 0x18B0, symBinAddr: 0x29F0, symSize: 0x10 }
  - { offsetInCU: 0x1CBE1, offset: 0x1D921, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCValueOSKD0Ev, symObjAddr: 0x18C0, symBinAddr: 0x2A00, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x1DCA4, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI15registerHandlerEPFbPvS0_P9IOServiceP10IONotifierES0_, symObjAddr: 0x18D0, symBinAddr: 0x2A10, symSize: 0x90 }
  - { offsetInCU: 0x45, offset: 0x1DCC2, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI15registerHandlerEPFbPvS0_P9IOServiceP10IONotifierES0_, symObjAddr: 0x18D0, symBinAddr: 0x2A10, symSize: 0x90 }
  - { offsetInCU: 0xD1, offset: 0x1DD4E, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI13postInterruptEhPKvj, symObjAddr: 0x1960, symBinAddr: 0x2AA0, symSize: 0x10 }
  - { offsetInCU: 0x130, offset: 0x1DDAD, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI13getDeviceInfoER7SMCInfo, symObjAddr: 0x1970, symBinAddr: 0x2AB0, symSize: 0x40 }
  - { offsetInCU: 0x1B8, offset: 0x1DE35, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI6addKeyEjR7evectorIR18VirtualSMCKeyValueXadL_ZNS1_7deleterES2_EEEP15VirtualSMCValue, symObjAddr: 0x19B0, symBinAddr: 0x2AF0, symSize: 0xC0 }
  - { offsetInCU: 0x299, offset: 0x1DF16, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI13valueWithDataEPKhhjP15VirtualSMCValueh14SerializeLevel, symObjAddr: 0x1A70, symBinAddr: 0x2BB0, symSize: 0x180 }
  - { offsetInCU: 0x2A1, offset: 0x1DF1E, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI8decodeSpEjt, symObjAddr: 0x1BF0, symBinAddr: 0x2D30, symSize: 0x170 }
  - { offsetInCU: 0x3F4, offset: 0x1E071, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI8decodeSpEjt, symObjAddr: 0x1BF0, symBinAddr: 0x2D30, symSize: 0x170 }
  - { offsetInCU: 0x4AF, offset: 0x1E12C, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI8encodeSpEjd, symObjAddr: 0x1D60, symBinAddr: 0x2EA0, symSize: 0x180 }
  - { offsetInCU: 0x591, offset: 0x1E20E, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI8decodeFpEjt, symObjAddr: 0x1EE0, symBinAddr: 0x3020, symSize: 0x160 }
  - { offsetInCU: 0x64C, offset: 0x1E2C9, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI8encodeFpEjd, symObjAddr: 0x2040, symBinAddr: 0x3180, symSize: 0x160 }
  - { offsetInCU: 0x707, offset: 0x1E384, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI11decodeIntSpEjt, symObjAddr: 0x21A0, symBinAddr: 0x32E0, symSize: 0x150 }
  - { offsetInCU: 0x7A5, offset: 0x1E422, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI11encodeIntSpEjs, symObjAddr: 0x22F0, symBinAddr: 0x3430, symSize: 0x170 }
  - { offsetInCU: 0x86B, offset: 0x1E4E8, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI11decodeIntFpEjt, symObjAddr: 0x2460, symBinAddr: 0x35A0, symSize: 0x140 }
  - { offsetInCU: 0x909, offset: 0x1E586, size: 0x8, addend: 0x0, symName: __ZN13VirtualSMCAPI11encodeIntFpEjt, symObjAddr: 0x25A0, symBinAddr: 0x36E0, symSize: 0x140 }
  - { offsetInCU: 0x27, offset: 0x1E81B, size: 0x8, addend: 0x0, symName: _VirtualSMC_kern_start, symObjAddr: 0x26E0, symBinAddr: 0x3820, symSize: 0x230 }
  - { offsetInCU: 0x2F, offset: 0x1E823, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMCD1Ev, symObjAddr: 0x2920, symBinAddr: 0x3A60, symSize: 0x10 }
  - { offsetInCU: 0x4A, offset: 0x1E83E, size: 0x8, addend: 0x0, symName: _VirtualSMC_startSuccess, symObjAddr: 0x81AEC, symBinAddr: 0xE34C, symSize: 0x0 }
  - { offsetInCU: 0x6B, offset: 0x1E85F, size: 0x8, addend: 0x0, symName: _VirtualSMC_debugEnabled, symObjAddr: 0x81B31, symBinAddr: 0xE391, symSize: 0x0 }
  - { offsetInCU: 0x86, offset: 0x1E87A, size: 0x8, addend: 0x0, symName: _VirtualSMC_debugPrintDelay, symObjAddr: 0x81AE8, symBinAddr: 0xE348, symSize: 0x0 }
  - { offsetInCU: 0x631F, offset: 0x24B13, size: 0x8, addend: 0x0, symName: _VirtualSMC_kern_start, symObjAddr: 0x26E0, symBinAddr: 0x3820, symSize: 0x230 }
  - { offsetInCU: 0x6327, offset: 0x24B1B, size: 0x8, addend: 0x0, symName: _VirtualSMC_kern_stop, symObjAddr: 0x2910, symBinAddr: 0x3A50, symSize: 0x10 }
  - { offsetInCU: 0x64C4, offset: 0x24CB8, size: 0x8, addend: 0x0, symName: _VirtualSMC_kern_stop, symObjAddr: 0x2910, symBinAddr: 0x3A50, symSize: 0x10 }
  - { offsetInCU: 0x64CC, offset: 0x24CC0, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMCD1Ev, symObjAddr: 0x2920, symBinAddr: 0x3A60, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x24D24, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMCD1Ev, symObjAddr: 0x2920, symBinAddr: 0x3A60, symSize: 0x10 }
  - { offsetInCU: 0x43, offset: 0x24D40, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC10gMetaClassE, symObjAddr: 0x81B38, symBinAddr: 0xE308, symSize: 0x0 }
  - { offsetInCU: 0x5B, offset: 0x24D58, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC9metaClassE, symObjAddr: 0xC4B0, symBinAddr: 0xD588, symSize: 0x0 }
  - { offsetInCU: 0x73, offset: 0x24D70, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC10superClassE, symObjAddr: 0xC4B8, symBinAddr: 0xD590, symSize: 0x0 }
  - { offsetInCU: 0x8B, offset: 0x24D88, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC8instanceE, symObjAddr: 0x81B68, symBinAddr: 0xE338, symSize: 0x0 }
  - { offsetInCU: 0xA3, offset: 0x24DA0, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC9mmioReadyE, symObjAddr: 0x81B70, symBinAddr: 0xE340, symSize: 0x0 }
  - { offsetInCU: 0xBB, offset: 0x24DB8, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC14servicingReadyE, symObjAddr: 0x81B60, symBinAddr: 0xE330, symSize: 0x0 }
  - { offsetInCU: 0xE0, offset: 0x24DDD, size: 0x8, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0xBFC0, symBinAddr: 0xBA80, symSize: 0x0 }
  - { offsetInCU: 0x65A, offset: 0x25357, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMCD1Ev, symObjAddr: 0x2920, symBinAddr: 0x3A60, symSize: 0x10 }
  - { offsetInCU: 0x6D5, offset: 0x253D2, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMCD0Ev, symObjAddr: 0x2930, symBinAddr: 0x3A70, symSize: 0x30 }
  - { offsetInCU: 0x763, offset: 0x25460, size: 0x8, addend: 0x0, symName: __ZNK10VirtualSMC12getMetaClassEv, symObjAddr: 0x2960, symBinAddr: 0x3AA0, symSize: 0x10 }
  - { offsetInCU: 0x7E6, offset: 0x254E3, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC5probeEP9IOServicePi, symObjAddr: 0x2970, symBinAddr: 0x3AB0, symSize: 0xB0 }
  - { offsetInCU: 0xFA5, offset: 0x25CA2, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC5startEP9IOService, symObjAddr: 0x2A20, symBinAddr: 0x3B60, symSize: 0x2150 }
  - { offsetInCU: 0x3BA3, offset: 0x288A0, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC4stopEP9IOService, symObjAddr: 0x4B70, symBinAddr: 0x5CB0, symSize: 0x30 }
  - { offsetInCU: 0x3BEB, offset: 0x288E8, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC20callPlatformFunctionEPK8OSSymbolbPvS3_S3_S3_, symObjAddr: 0x4BA0, symBinAddr: 0x5CE0, symSize: 0x8C0 }
  - { offsetInCU: 0x41AC, offset: 0x28EA9, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC24mapDeviceMemoryWithIndexEjj, symObjAddr: 0x5460, symBinAddr: 0x65A0, symSize: 0x40 }
  - { offsetInCU: 0x426D, offset: 0x28F6A, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC17registerInterruptEiP8OSObjectPFvS1_PvP9IOServiceiES2_, symObjAddr: 0x54A0, symBinAddr: 0x65E0, symSize: 0x160 }
  - { offsetInCU: 0x4415, offset: 0x29112, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC19unregisterInterruptEi, symObjAddr: 0x5600, symBinAddr: 0x6740, symSize: 0x70 }
  - { offsetInCU: 0x44BD, offset: 0x291BA, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC16getInterruptTypeEiPi, symObjAddr: 0x5670, symBinAddr: 0x67B0, symSize: 0x20 }
  - { offsetInCU: 0x453C, offset: 0x29239, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC15enableInterruptEi, symObjAddr: 0x5690, symBinAddr: 0x67D0, symSize: 0x40 }
  - { offsetInCU: 0x45A0, offset: 0x2929D, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC16disableInterruptEi, symObjAddr: 0x56D0, symBinAddr: 0x6810, symSize: 0x40 }
  - { offsetInCU: 0x4604, offset: 0x29301, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC14causeInterruptEi, symObjAddr: 0x5710, symBinAddr: 0x6850, symSize: 0x60 }
  - { offsetInCU: 0x4678, offset: 0x29375, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC13setPowerStateEmP9IOService, symObjAddr: 0x5770, symBinAddr: 0x68B0, symSize: 0x80 }
  - { offsetInCU: 0x4783, offset: 0x29480, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC9ioWrite32EtjP11IOMemoryMap, symObjAddr: 0x57F0, symBinAddr: 0x6930, symSize: 0x30 }
  - { offsetInCU: 0x47F0, offset: 0x294ED, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC9ioWrite16EttP11IOMemoryMap, symObjAddr: 0x5820, symBinAddr: 0x6960, symSize: 0x30 }
  - { offsetInCU: 0x48A0, offset: 0x2959D, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC8ioWrite8EthP11IOMemoryMap, symObjAddr: 0x5850, symBinAddr: 0x6990, symSize: 0x520 }
  - { offsetInCU: 0x4CEB, offset: 0x299E8, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC8ioRead32EtP11IOMemoryMap, symObjAddr: 0x5D70, symBinAddr: 0x6EB0, symSize: 0x30 }
  - { offsetInCU: 0x4D43, offset: 0x29A40, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC8ioRead16EtP11IOMemoryMap, symObjAddr: 0x5DA0, symBinAddr: 0x6EE0, symSize: 0x30 }
  - { offsetInCU: 0x4D9B, offset: 0x29A98, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC7ioRead8EtP11IOMemoryMap, symObjAddr: 0x5DD0, symBinAddr: 0x6F10, symSize: 0x250 }
  - { offsetInCU: 0x4F19, offset: 0x29C16, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC14devicesPresentEP9IOService, symObjAddr: 0x6020, symBinAddr: 0x7160, symSize: 0x350 }
  - { offsetInCU: 0x5053, offset: 0x29D50, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC14watchDogActionEP8OSObjectP18IOTimerEventSource, symObjAddr: 0x6370, symBinAddr: 0x74B0, symSize: 0x160 }
  - { offsetInCU: 0x519D, offset: 0x29E9A, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC21obtainBooterModelInfoER7SMCInfo, symObjAddr: 0x64D0, symBinAddr: 0x7610, symSize: 0x330 }
  - { offsetInCU: 0x53E2, offset: 0x2A0DF, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC15obtainModelInfoER7SMCInfoPKcPK12OSDictionaryS6_, symObjAddr: 0x6800, symBinAddr: 0x7940, symSize: 0x330 }
  - { offsetInCU: 0x587C, offset: 0x2A579, size: 0x8, addend: 0x0, symName: __ZN15SMCProtocolPMIOC1Ev, symObjAddr: 0x6B30, symBinAddr: 0x7C70, symSize: 0xA0 }
  - { offsetInCU: 0x5902, offset: 0x2A5FF, size: 0x8, addend: 0x0, symName: __ZN15SMCProtocolMMIOC1Ev, symObjAddr: 0x6BD0, symBinAddr: 0x7D10, symSize: 0x90 }
  - { offsetInCU: 0x5976, offset: 0x2A673, size: 0x8, addend: 0x0, symName: '__ZZN10VirtualSMC15obtainModelInfoER7SMCInfoPKcPK12OSDictionaryS6_ENK3$_0clES3_b', symObjAddr: 0x6C60, symBinAddr: 0x7DA0, symSize: 0x310 }
  - { offsetInCU: 0x5E1D, offset: 0x2AB1A, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC9MetaClassD1Ev, symObjAddr: 0x6F70, symBinAddr: 0x80B0, symSize: 0x10 }
  - { offsetInCU: 0x5E98, offset: 0x2AB95, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC9MetaClassD0Ev, symObjAddr: 0x6F80, symBinAddr: 0x80C0, symSize: 0x10 }
  - { offsetInCU: 0x60F8, offset: 0x2ADF5, size: 0x8, addend: 0x0, symName: __ZNK10VirtualSMC9MetaClass5allocEv, symObjAddr: 0x6F90, symBinAddr: 0x80D0, symSize: 0x120 }
  - { offsetInCU: 0x62C2, offset: 0x2AFBF, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_kern_vsmc.cpp, symObjAddr: 0x70B0, symBinAddr: 0x81F0, symSize: 0x40 }
  - { offsetInCU: 0x6352, offset: 0x2B04F, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x70F0, symBinAddr: 0x8230, symSize: 0x20 }
  - { offsetInCU: 0x63C4, offset: 0x2B0C1, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC9MetaClassC1Ev, symObjAddr: 0x7110, symBinAddr: 0x8250, symSize: 0x40 }
  - { offsetInCU: 0x641F, offset: 0x2B11C, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMCC2EPK11OSMetaClass, symObjAddr: 0x7150, symBinAddr: 0x8290, symSize: 0x100 }
  - { offsetInCU: 0x6583, offset: 0x2B280, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMCC1EPK11OSMetaClass, symObjAddr: 0x7250, symBinAddr: 0x8390, symSize: 0x100 }
  - { offsetInCU: 0x66FF, offset: 0x2B3FC, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMCD2Ev, symObjAddr: 0x7350, symBinAddr: 0x8490, symSize: 0x10 }
  - { offsetInCU: 0x672C, offset: 0x2B429, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC9MetaClassC2Ev, symObjAddr: 0x7360, symBinAddr: 0x84A0, symSize: 0x40 }
  - { offsetInCU: 0x675B, offset: 0x2B458, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMCC1Ev, symObjAddr: 0x73A0, symBinAddr: 0x84E0, symSize: 0x110 }
  - { offsetInCU: 0x68AA, offset: 0x2B5A7, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMCC2Ev, symObjAddr: 0x74B0, symBinAddr: 0x85F0, symSize: 0x110 }
  - { offsetInCU: 0x69CD, offset: 0x2B6CA, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC16forcedGenerationEv, symObjAddr: 0x75C0, symBinAddr: 0x8700, symSize: 0x70 }
  - { offsetInCU: 0x6A16, offset: 0x2B713, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC13setInterruptsEb, symObjAddr: 0x7630, symBinAddr: 0x8770, symSize: 0x50 }
  - { offsetInCU: 0x6A7F, offset: 0x2B77C, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC13postInterruptEhPKvj, symObjAddr: 0x7680, symBinAddr: 0x87C0, symSize: 0x390 }
  - { offsetInCU: 0x6BFD, offset: 0x2B8FA, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC12getInterruptEv, symObjAddr: 0x7A10, symBinAddr: 0x8B50, symSize: 0x1C0 }
  - { offsetInCU: 0x6D48, offset: 0x2BA45, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC15postWatchDogJobEhyb, symObjAddr: 0x7BD0, symBinAddr: 0x8D10, symSize: 0xB0 }
  - { offsetInCU: 0x6D8B, offset: 0x2BA88, size: 0x8, addend: 0x0, symName: __ZN10VirtualSMC8ioVerifyERtP11IOMemoryMap, symObjAddr: 0x7C80, symBinAddr: 0x8DC0, symSize: 0xF0 }
  - { offsetInCU: 0x35, offset: 0x2CB9F, size: 0x8, addend: 0x0, symName: __ZL10bootargOff, symObjAddr: 0xA8F8, symBinAddr: 0xE2C0, symSize: 0x0 }
  - { offsetInCU: 0x6E, offset: 0x2CBD8, size: 0x8, addend: 0x0, symName: __ZL12bootargDebug, symObjAddr: 0xA900, symBinAddr: 0xE2C8, symSize: 0x0 }
  - { offsetInCU: 0x8C, offset: 0x2CBF6, size: 0x8, addend: 0x0, symName: __ZL11bootargBeta, symObjAddr: 0xA908, symBinAddr: 0xE2D0, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x2CD03, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValue10readAccessEv.116, symObjAddr: 0x7D70, symBinAddr: 0x8EB0, symSize: 0x10 }
  - { offsetInCU: 0x59B, offset: 0x2D277, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValue10readAccessEv.116, symObjAddr: 0x7D70, symBinAddr: 0x8EB0, symSize: 0x10 }
  - { offsetInCU: 0x5CA, offset: 0x2D2A6, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValue11writeAccessEv.117, symObjAddr: 0x7D80, symBinAddr: 0x8EC0, symSize: 0x10 }
  - { offsetInCU: 0x5F9, offset: 0x2D2D5, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValue6updateEPKh, symObjAddr: 0x7D90, symBinAddr: 0x8ED0, symSize: 0x20 }
  - { offsetInCU: 0x643, offset: 0x2D31F, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValueD1Ev, symObjAddr: 0x7DB0, symBinAddr: 0x8EF0, symSize: 0x10 }
  - { offsetInCU: 0x676, offset: 0x2D352, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValueD0Ev, symObjAddr: 0x7DC0, symBinAddr: 0x8F00, symSize: 0x10 }
  - { offsetInCU: 0x6AC, offset: 0x2D388, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValue4initEPKhhjh14SerializeLevel, symObjAddr: 0x7DD0, symBinAddr: 0x8F10, symSize: 0x70 }
  - { offsetInCU: 0x89F, offset: 0x2D57B, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValue4initEPK12OSDictionary, symObjAddr: 0x7E40, symBinAddr: 0x8F80, symSize: 0x280 }
  - { offsetInCU: 0xA76, offset: 0x2D752, size: 0x8, addend: 0x0, symName: __ZNK15VirtualSMCValue3getERh, symObjAddr: 0x80C0, symBinAddr: 0x9200, symSize: 0x20 }
  - { offsetInCU: 0x27, offset: 0x2D7B1, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider4initEv, symObjAddr: 0x80E0, symBinAddr: 0x9220, symSize: 0x4C0 }
  - { offsetInCU: 0x43, offset: 0x2D7CD, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider8instanceE, symObjAddr: 0x81AF0, symBinAddr: 0xE350, symSize: 0x0 }
  - { offsetInCU: 0x5B, offset: 0x2D7E5, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider12monitorStartE, symObjAddr: 0x81AF8, symBinAddr: 0xE358, symSize: 0x0 }
  - { offsetInCU: 0x73, offset: 0x2D7FD, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider10monitorEndE, symObjAddr: 0x81B00, symBinAddr: 0xE360, symSize: 0x0 }
  - { offsetInCU: 0x8B, offset: 0x2D815, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider15monitorSmcStartE, symObjAddr: 0x81B08, symBinAddr: 0xE368, symSize: 0x0 }
  - { offsetInCU: 0xA3, offset: 0x2D82D, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider13monitorSmcEndE, symObjAddr: 0x81B10, symBinAddr: 0xE370, symSize: 0x0 }
  - { offsetInCU: 0xBB, offset: 0x2D845, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider13orgKernelTrapE, symObjAddr: 0x81B18, symBinAddr: 0xE378, symSize: 0x0 }
  - { offsetInCU: 0xD3, offset: 0x2D85D, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider23orgCallPlatformFunctionE, symObjAddr: 0x81B20, symBinAddr: 0xE380, symSize: 0x0 }
  - { offsetInCU: 0xEB, offset: 0x2D875, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider20orgStopWatchdogTimerE, symObjAddr: 0x81B28, symBinAddr: 0xE388, symSize: 0x0 }
  - { offsetInCU: 0x103, offset: 0x2D88D, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider15firstGenerationE, symObjAddr: 0x81B30, symBinAddr: 0xE390, symSize: 0x0 }
  - { offsetInCU: 0x12E, offset: 0x2D8B8, size: 0x8, addend: 0x0, symName: __ZL12kextAppleSmc, symObjAddr: 0xA910, symBinAddr: 0xE2D8, symSize: 0x0 }
  - { offsetInCU: 0x14C, offset: 0x2D8D6, size: 0x8, addend: 0x0, symName: __ZL8kextPath, symObjAddr: 0xA938, symBinAddr: 0xE300, symSize: 0x0 }
  - { offsetInCU: 0x853, offset: 0x2DFDD, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider4initEv, symObjAddr: 0x80E0, symBinAddr: 0x9220, symSize: 0x4C0 }
  - { offsetInCU: 0xE46, offset: 0x2E5D0, size: 0x8, addend: 0x0, symName: '__ZZN18VirtualSMCProvider4initEvEN3$_08__invokeEPvR13KernelPatcher', symObjAddr: 0x85A0, symBinAddr: 0x96E0, symSize: 0x210 }
  - { offsetInCU: 0x132A, offset: 0x2EAB4, size: 0x8, addend: 0x0, symName: '__ZZN18VirtualSMCProvider4initEvEN3$_18__invokeEPvR13KernelPatchermym', symObjAddr: 0x87B0, symBinAddr: 0x98F0, symSize: 0x250 }
  - { offsetInCU: 0x1332, offset: 0x2EABC, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider26filterCallPlatformFunctionEPvPK8OSSymbolbS0_S0_S0_S0_, symObjAddr: 0x8A00, symBinAddr: 0x9B40, symSize: 0x90 }
  - { offsetInCU: 0x16C7, offset: 0x2EE51, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider26filterCallPlatformFunctionEPvPK8OSSymbolbS0_S0_S0_S0_, symObjAddr: 0x8A00, symBinAddr: 0x9B40, symSize: 0x90 }
  - { offsetInCU: 0x17CB, offset: 0x2EF55, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider23filterStopWatchdogTimerEPv, symObjAddr: 0x8A90, symBinAddr: 0x9BD0, symSize: 0x70 }
  - { offsetInCU: 0x186F, offset: 0x2EFF9, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider10kernelTrapI21x86_saved_state_109_tEEvPT_Pm, symObjAddr: 0x8B00, symBinAddr: 0x9C40, symSize: 0x350 }
  - { offsetInCU: 0x1ACA, offset: 0x2F254, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider10kernelTrapI21x86_saved_state_108_tEEvPT_Pm, symObjAddr: 0x8E50, symBinAddr: 0x9F90, symSize: 0x350 }
  - { offsetInCU: 0x1D25, offset: 0x2F4AF, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider10kernelTrapI22x86_saved_state_1010_tEEvPT_Pm, symObjAddr: 0x91A0, symBinAddr: 0xA2E0, symSize: 0x350 }
  - { offsetInCU: 0x1FAE, offset: 0x2F738, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCProvider15ioProcessResultEh, symObjAddr: 0x94F0, symBinAddr: 0xA630, symSize: 0x3C0 }
  - { offsetInCU: 0x27, offset: 0x2FC56, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCKeystore13mergeProviderEPK12OSDictionaryPKci, symObjAddr: 0x98B0, symBinAddr: 0xA9F0, symSize: 0x150 }
  - { offsetInCU: 0xC9A, offset: 0x308C9, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCKeystore13mergeProviderEPK12OSDictionaryPKci, symObjAddr: 0x98B0, symBinAddr: 0xA9F0, symSize: 0x150 }
  - { offsetInCU: 0xCA2, offset: 0x308D1, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCKeyValue7compareEPKvS1_, symObjAddr: 0x9A00, symBinAddr: 0xAB40, symSize: 0x30 }
  - { offsetInCU: 0xF1D, offset: 0x30B4C, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCKeyValue7compareEPKvS1_, symObjAddr: 0x9A00, symBinAddr: 0xAB40, symSize: 0x30 }
  - { offsetInCU: 0xFD8, offset: 0x30C07, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCKeystore5mergeEPK7OSArray, symObjAddr: 0x9A30, symBinAddr: 0xAB70, symSize: 0x250 }
  - { offsetInCU: 0x120F, offset: 0x30E3E, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCKeystore6addKeyEjP15VirtualSMCValueb, symObjAddr: 0x9C80, symBinAddr: 0xADC0, symSize: 0xE0 }
  - { offsetInCU: 0x130C, offset: 0x30F3B, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCKeystore15readValueByNameEjRPK15VirtualSMCValue, symObjAddr: 0x9D60, symBinAddr: 0xAEA0, symSize: 0x2D0 }
  - { offsetInCU: 0x19D0, offset: 0x315FF, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCKeystore15readNameByIndexEjRj, symObjAddr: 0xA030, symBinAddr: 0xB170, symSize: 0x1F0 }
  - { offsetInCU: 0x1AC9, offset: 0x316F8, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCKeystore16writeValueByNameEjPKh, symObjAddr: 0xA220, symBinAddr: 0xB360, symSize: 0x2F0 }
  - { offsetInCU: 0x2113, offset: 0x31D42, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCKeystore13getInfoByNameEjRhRjS0_, symObjAddr: 0xA510, symBinAddr: 0xB650, symSize: 0x2BA }
...
