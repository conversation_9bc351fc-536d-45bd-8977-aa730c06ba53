---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/work/VirtualSMC/VirtualSMC/build/Release/SMCBatteryManager.kext/Contents/MacOS/SMCBatteryManager'
relocations:
  - { offsetInCU: 0x35, offset: 0x35, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0x6410, symBinAddr: 0x9BC8, symSize: 0x0 }
  - { offsetInCU: 0x206, offset: 0x206, size: 0x8, addend: 0x0, symName: __realmain, symObjAddr: 0x64D8, symBinAddr: 0x9C90, symSize: 0x0 }
  - { offsetInCU: 0x21C, offset: 0x21C, size: 0x8, addend: 0x0, symName: __antimain, symObjAddr: 0x64E0, symBinAddr: 0x9C98, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x13717, size: 0x8, addend: 0x0, symName: __ZN11ACPIBattery18getNumberFromArrayEP7OSArrayj, symObjAddr: 0x0, symBinAddr: 0x1020, symSize: 0x40 }
  - { offsetInCU: 0x91C, offset: 0x1400C, size: 0x8, addend: 0x0, symName: __ZN11ACPIBattery18getNumberFromArrayEP7OSArrayj, symObjAddr: 0x0, symBinAddr: 0x1020, symSize: 0x40 }
  - { offsetInCU: 0x96D, offset: 0x1405D, size: 0x8, addend: 0x0, symName: __ZN11ACPIBattery18getStringFromArrayEP7OSArrayjPcj, symObjAddr: 0x40, symBinAddr: 0x1060, symSize: 0xE0 }
  - { offsetInCU: 0xA96, offset: 0x14186, size: 0x8, addend: 0x0, symName: __ZN11ACPIBattery14getBatteryInfoER11BatteryInfob, symObjAddr: 0x120, symBinAddr: 0x1140, symSize: 0x8C0 }
  - { offsetInCU: 0x27, offset: 0x14979, size: 0x8, addend: 0x0, symName: __ZN14BatteryManagerD1Ev, symObjAddr: 0x9E0, symBinAddr: 0x1A00, symSize: 0x10 }
  - { offsetInCU: 0x3F, offset: 0x14991, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager8instanceE, symObjAddr: 0x4D0E0, symBinAddr: 0x9D18, symSize: 0x0 }
  - { offsetInCU: 0xFC6, offset: 0x15918, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager10gMetaClassE, symObjAddr: 0x4D068, symBinAddr: 0x9CA0, symSize: 0x0 }
  - { offsetInCU: 0xFDD, offset: 0x1592F, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager9metaClassE, symObjAddr: 0x7008, symBinAddr: 0x80B8, symSize: 0x0 }
  - { offsetInCU: 0xFF4, offset: 0x15946, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager10superClassE, symObjAddr: 0x7010, symBinAddr: 0x80C0, symSize: 0x0 }
  - { offsetInCU: 0x1077, offset: 0x159C9, size: 0x8, addend: 0x0, symName: __ZN14BatteryManagerD1Ev, symObjAddr: 0x9E0, symBinAddr: 0x1A00, symSize: 0x10 }
  - { offsetInCU: 0x10EE, offset: 0x15A40, size: 0x8, addend: 0x0, symName: __ZN14BatteryManagerD0Ev, symObjAddr: 0x9F0, symBinAddr: 0x1A10, symSize: 0x30 }
  - { offsetInCU: 0x1177, offset: 0x15AC9, size: 0x8, addend: 0x0, symName: __ZNK14BatteryManager12getMetaClassEv, symObjAddr: 0xA20, symBinAddr: 0x1A40, symSize: 0x10 }
  - { offsetInCU: 0x11D2, offset: 0x15B24, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager9MetaClassD1Ev, symObjAddr: 0xA30, symBinAddr: 0x1A50, symSize: 0x10 }
  - { offsetInCU: 0x1249, offset: 0x15B9B, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager9MetaClassD0Ev, symObjAddr: 0xA40, symBinAddr: 0x1A60, symSize: 0x10 }
  - { offsetInCU: 0x12F4, offset: 0x15C46, size: 0x8, addend: 0x0, symName: __ZNK14BatteryManager9MetaClass5allocEv, symObjAddr: 0xA50, symBinAddr: 0x1A70, symSize: 0x30 }
  - { offsetInCU: 0x13DD, offset: 0x15D2F, size: 0x8, addend: 0x0, symName: __ZN14BatteryManagerC2Ev, symObjAddr: 0xA80, symBinAddr: 0x1AA0, symSize: 0x380 }
  - { offsetInCU: 0x14FD, offset: 0x15E4F, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_BatteryManager.cpp, symObjAddr: 0xE00, symBinAddr: 0x1E20, symSize: 0x40 }
  - { offsetInCU: 0x1589, offset: 0x15EDB, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0xE40, symBinAddr: 0x1E60, symSize: 0x20 }
  - { offsetInCU: 0x15F7, offset: 0x15F49, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager9MetaClassC1Ev, symObjAddr: 0xE60, symBinAddr: 0x1E80, symSize: 0x40 }
  - { offsetInCU: 0x164F, offset: 0x15FA1, size: 0x8, addend: 0x0, symName: __ZN14BatteryManagerC2EPK11OSMetaClass, symObjAddr: 0xEA0, symBinAddr: 0x1EC0, symSize: 0x370 }
  - { offsetInCU: 0x1733, offset: 0x16085, size: 0x8, addend: 0x0, symName: __ZN14BatteryManagerC1EPK11OSMetaClass, symObjAddr: 0x1210, symBinAddr: 0x2230, symSize: 0x10 }
  - { offsetInCU: 0x177C, offset: 0x160CE, size: 0x8, addend: 0x0, symName: __ZN14BatteryManagerD2Ev, symObjAddr: 0x1220, symBinAddr: 0x2240, symSize: 0x10 }
  - { offsetInCU: 0x17A7, offset: 0x160F9, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager9MetaClassC2Ev, symObjAddr: 0x1230, symBinAddr: 0x2250, symSize: 0x40 }
  - { offsetInCU: 0x17D4, offset: 0x16126, size: 0x8, addend: 0x0, symName: __ZN14BatteryManagerC1Ev, symObjAddr: 0x1270, symBinAddr: 0x2290, symSize: 0x10 }
  - { offsetInCU: 0x1893, offset: 0x161E5, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager12checkDevicesEv, symObjAddr: 0x1280, symBinAddr: 0x22A0, symSize: 0x1440 }
  - { offsetInCU: 0x1F3E, offset: 0x16890, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager19externalPowerNotifyEb, symObjAddr: 0x26C0, symBinAddr: 0x36E0, symSize: 0x30 }
  - { offsetInCU: 0x1F81, offset: 0x168D3, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager18decrementQuickPollEv, symObjAddr: 0x26F0, symBinAddr: 0x3710, symSize: 0x30 }
  - { offsetInCU: 0x1FCC, offset: 0x1691E, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager13findBatteriesEv, symObjAddr: 0x2720, symBinAddr: 0x3740, symSize: 0x1C0 }
  - { offsetInCU: 0x2063, offset: 0x169B5, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager16acpiNotificationEPvS0_jP9IOServiceS0_m, symObjAddr: 0x28E0, symBinAddr: 0x3900, symSize: 0x90 }
  - { offsetInCU: 0x2139, offset: 0x16A8B, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager14findACAdaptersEv, symObjAddr: 0x2970, symBinAddr: 0x3990, symSize: 0x1A0 }
  - { offsetInCU: 0x21C4, offset: 0x16B16, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager9subscribeEPFiPvES0_, symObjAddr: 0x2B10, symBinAddr: 0x3B30, symSize: 0x20 }
  - { offsetInCU: 0x2205, offset: 0x16B57, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager4wakeEv, symObjAddr: 0x2B30, symBinAddr: 0x3B50, symSize: 0x60 }
  - { offsetInCU: 0x2240, offset: 0x16B92, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager5sleepEv, symObjAddr: 0x2B90, symBinAddr: 0x3BB0, symSize: 0x20 }
  - { offsetInCU: 0x2274, offset: 0x16BC6, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager5probeEv, symObjAddr: 0x2BB0, symBinAddr: 0x3BD0, symSize: 0x180 }
  - { offsetInCU: 0x227C, offset: 0x16BCE, size: 0x8, addend: 0x0, symName: '__ZZN14BatteryManager5probeEvEN3$_08__invokeEP8OSObjectP18IOTimerEventSource', symObjAddr: 0x2D30, symBinAddr: 0x3D50, symSize: 0x50 }
  - { offsetInCU: 0x23A3, offset: 0x16CF5, size: 0x8, addend: 0x0, symName: '__ZZN14BatteryManager5probeEvEN3$_08__invokeEP8OSObjectP18IOTimerEventSource', symObjAddr: 0x2D30, symBinAddr: 0x3D50, symSize: 0x50 }
  - { offsetInCU: 0x241F, offset: 0x16D71, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager5startEv, symObjAddr: 0x2D80, symBinAddr: 0x3DA0, symSize: 0x40 }
  - { offsetInCU: 0x244C, offset: 0x16D9E, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager18batteriesConnectedEv, symObjAddr: 0x2DC0, symBinAddr: 0x3DE0, symSize: 0x60 }
  - { offsetInCU: 0x248C, offset: 0x16DDE, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager17adaptersConnectedEv, symObjAddr: 0x2E20, symBinAddr: 0x3E40, symSize: 0x90 }
  - { offsetInCU: 0x24F5, offset: 0x16E47, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager16batteriesAreFullEv, symObjAddr: 0x2EB0, symBinAddr: 0x3ED0, symSize: 0x60 }
  - { offsetInCU: 0x2542, offset: 0x16E94, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager22externalPowerConnectedEv, symObjAddr: 0x2F10, symBinAddr: 0x3F30, symSize: 0x130 }
  - { offsetInCU: 0x25CE, offset: 0x16F20, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager22calculateBatteryStatusEm, symObjAddr: 0x3040, symBinAddr: 0x4060, symSize: 0x90 }
  - { offsetInCU: 0x265D, offset: 0x16FAF, size: 0x8, addend: 0x0, symName: __ZN14BatteryManager12createSharedEv, symObjAddr: 0x30D0, symBinAddr: 0x40F0, symSize: 0xC0 }
  - { offsetInCU: 0x27, offset: 0x1763A, size: 0x8, addend: 0x0, symName: __ZN4ACID10readAccessEv, symObjAddr: 0x3190, symBinAddr: 0x41B0, symSize: 0x70 }
  - { offsetInCU: 0x40C, offset: 0x17A1F, size: 0x8, addend: 0x0, symName: __ZN4BSIn10readAccessEv, symObjAddr: 0x3900, symBinAddr: 0x4920, symSize: 0xA0 }
  - { offsetInCU: 0x58B, offset: 0x17B9E, size: 0x8, addend: 0x0, symName: __ZN4ACID10readAccessEv, symObjAddr: 0x3190, symBinAddr: 0x41B0, symSize: 0x70 }
  - { offsetInCU: 0x60C, offset: 0x17C1F, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValue11writeAccessEv, symObjAddr: 0x3200, symBinAddr: 0x4220, symSize: 0x10 }
  - { offsetInCU: 0x63B, offset: 0x17C4E, size: 0x8, addend: 0x0, symName: __ZN4ACIDD1Ev, symObjAddr: 0x3210, symBinAddr: 0x4230, symSize: 0x10 }
  - { offsetInCU: 0x670, offset: 0x17C83, size: 0x8, addend: 0x0, symName: __ZN4ACIDD0Ev, symObjAddr: 0x3220, symBinAddr: 0x4240, symSize: 0x10 }
  - { offsetInCU: 0x71F, offset: 0x17D32, size: 0x8, addend: 0x0, symName: __ZN4ACIN10readAccessEv, symObjAddr: 0x3230, symBinAddr: 0x4250, symSize: 0x40 }
  - { offsetInCU: 0x79C, offset: 0x17DAF, size: 0x8, addend: 0x0, symName: __ZN4ACIND1Ev, symObjAddr: 0x3270, symBinAddr: 0x4290, symSize: 0x10 }
  - { offsetInCU: 0x7D1, offset: 0x17DE4, size: 0x8, addend: 0x0, symName: __ZN4ACIND0Ev, symObjAddr: 0x3280, symBinAddr: 0x42A0, symSize: 0x10 }
  - { offsetInCU: 0x901, offset: 0x17F14, size: 0x8, addend: 0x0, symName: __ZN4B0AC10readAccessEv, symObjAddr: 0x3290, symBinAddr: 0x42B0, symSize: 0x60 }
  - { offsetInCU: 0x9A6, offset: 0x17FB9, size: 0x8, addend: 0x0, symName: __ZN4B0ACD1Ev, symObjAddr: 0x32F0, symBinAddr: 0x4310, symSize: 0x10 }
  - { offsetInCU: 0x9DB, offset: 0x17FEE, size: 0x8, addend: 0x0, symName: __ZN4B0ACD0Ev, symObjAddr: 0x3300, symBinAddr: 0x4320, symSize: 0x10 }
  - { offsetInCU: 0xA8A, offset: 0x1809D, size: 0x8, addend: 0x0, symName: __ZN4B0AV10readAccessEv, symObjAddr: 0x3310, symBinAddr: 0x4330, symSize: 0x60 }
  - { offsetInCU: 0xB2F, offset: 0x18142, size: 0x8, addend: 0x0, symName: __ZN4B0AVD1Ev, symObjAddr: 0x3370, symBinAddr: 0x4390, symSize: 0x10 }
  - { offsetInCU: 0xB64, offset: 0x18177, size: 0x8, addend: 0x0, symName: __ZN4B0AVD0Ev, symObjAddr: 0x3380, symBinAddr: 0x43A0, symSize: 0x10 }
  - { offsetInCU: 0xC13, offset: 0x18226, size: 0x8, addend: 0x0, symName: __ZN4B0BI10readAccessEv, symObjAddr: 0x3390, symBinAddr: 0x43B0, symSize: 0x50 }
  - { offsetInCU: 0xC81, offset: 0x18294, size: 0x8, addend: 0x0, symName: __ZN4B0BID1Ev, symObjAddr: 0x33E0, symBinAddr: 0x4400, symSize: 0x10 }
  - { offsetInCU: 0xCB6, offset: 0x182C9, size: 0x8, addend: 0x0, symName: __ZN4B0BID0Ev, symObjAddr: 0x33F0, symBinAddr: 0x4410, symSize: 0x10 }
  - { offsetInCU: 0xD65, offset: 0x18378, size: 0x8, addend: 0x0, symName: __ZN4B0CT10readAccessEv, symObjAddr: 0x3400, symBinAddr: 0x4420, symSize: 0x60 }
  - { offsetInCU: 0xE0A, offset: 0x1841D, size: 0x8, addend: 0x0, symName: __ZN4B0CTD1Ev, symObjAddr: 0x3460, symBinAddr: 0x4480, symSize: 0x10 }
  - { offsetInCU: 0xE3F, offset: 0x18452, size: 0x8, addend: 0x0, symName: __ZN4B0CTD0Ev, symObjAddr: 0x3470, symBinAddr: 0x4490, symSize: 0x10 }
  - { offsetInCU: 0xEEE, offset: 0x18501, size: 0x8, addend: 0x0, symName: __ZN4B0FC10readAccessEv, symObjAddr: 0x3480, symBinAddr: 0x44A0, symSize: 0x60 }
  - { offsetInCU: 0xF93, offset: 0x185A6, size: 0x8, addend: 0x0, symName: __ZN4B0FCD1Ev, symObjAddr: 0x34E0, symBinAddr: 0x4500, symSize: 0x10 }
  - { offsetInCU: 0xFC8, offset: 0x185DB, size: 0x8, addend: 0x0, symName: __ZN4B0FCD0Ev, symObjAddr: 0x34F0, symBinAddr: 0x4510, symSize: 0x10 }
  - { offsetInCU: 0x1077, offset: 0x1868A, size: 0x8, addend: 0x0, symName: __ZN4B0PS10readAccessEv, symObjAddr: 0x3500, symBinAddr: 0x4520, symSize: 0x10 }
  - { offsetInCU: 0x10AA, offset: 0x186BD, size: 0x8, addend: 0x0, symName: __ZN4B0PSD1Ev, symObjAddr: 0x3510, symBinAddr: 0x4530, symSize: 0x10 }
  - { offsetInCU: 0x10DF, offset: 0x186F2, size: 0x8, addend: 0x0, symName: __ZN4B0PSD0Ev, symObjAddr: 0x3520, symBinAddr: 0x4540, symSize: 0x10 }
  - { offsetInCU: 0x118E, offset: 0x187A1, size: 0x8, addend: 0x0, symName: __ZN4B0RM10readAccessEv, symObjAddr: 0x3530, symBinAddr: 0x4550, symSize: 0x60 }
  - { offsetInCU: 0x1233, offset: 0x18846, size: 0x8, addend: 0x0, symName: __ZN4B0RMD1Ev, symObjAddr: 0x3590, symBinAddr: 0x45B0, symSize: 0x10 }
  - { offsetInCU: 0x1268, offset: 0x1887B, size: 0x8, addend: 0x0, symName: __ZN4B0RMD0Ev, symObjAddr: 0x35A0, symBinAddr: 0x45C0, symSize: 0x10 }
  - { offsetInCU: 0x1317, offset: 0x1892A, size: 0x8, addend: 0x0, symName: __ZN4B0St10readAccessEv, symObjAddr: 0x35B0, symBinAddr: 0x45D0, symSize: 0xD0 }
  - { offsetInCU: 0x1449, offset: 0x18A5C, size: 0x8, addend: 0x0, symName: __ZN4B0StD1Ev, symObjAddr: 0x3680, symBinAddr: 0x46A0, symSize: 0x10 }
  - { offsetInCU: 0x147E, offset: 0x18A91, size: 0x8, addend: 0x0, symName: __ZN4B0StD0Ev, symObjAddr: 0x3690, symBinAddr: 0x46B0, symSize: 0x10 }
  - { offsetInCU: 0x152D, offset: 0x18B40, size: 0x8, addend: 0x0, symName: __ZN4B0TF10readAccessEv, symObjAddr: 0x36A0, symBinAddr: 0x46C0, symSize: 0x70 }
  - { offsetInCU: 0x15E9, offset: 0x18BFC, size: 0x8, addend: 0x0, symName: __ZN4B0TFD1Ev, symObjAddr: 0x3710, symBinAddr: 0x4730, symSize: 0x10 }
  - { offsetInCU: 0x161E, offset: 0x18C31, size: 0x8, addend: 0x0, symName: __ZN4B0TFD0Ev, symObjAddr: 0x3720, symBinAddr: 0x4740, symSize: 0x10 }
  - { offsetInCU: 0x16CD, offset: 0x18CE0, size: 0x8, addend: 0x0, symName: __ZN4BATP10readAccessEv, symObjAddr: 0x3730, symBinAddr: 0x4750, symSize: 0x40 }
  - { offsetInCU: 0x174A, offset: 0x18D5D, size: 0x8, addend: 0x0, symName: __ZN4BATPD1Ev, symObjAddr: 0x3770, symBinAddr: 0x4790, symSize: 0x10 }
  - { offsetInCU: 0x177F, offset: 0x18D92, size: 0x8, addend: 0x0, symName: __ZN4BATPD0Ev, symObjAddr: 0x3780, symBinAddr: 0x47A0, symSize: 0x10 }
  - { offsetInCU: 0x182E, offset: 0x18E41, size: 0x8, addend: 0x0, symName: __ZN4BBAD10readAccessEv, symObjAddr: 0x3790, symBinAddr: 0x47B0, symSize: 0x40 }
  - { offsetInCU: 0x18AB, offset: 0x18EBE, size: 0x8, addend: 0x0, symName: __ZN4BBADD1Ev, symObjAddr: 0x37D0, symBinAddr: 0x47F0, symSize: 0x10 }
  - { offsetInCU: 0x18E0, offset: 0x18EF3, size: 0x8, addend: 0x0, symName: __ZN4BBADD0Ev, symObjAddr: 0x37E0, symBinAddr: 0x4800, symSize: 0x10 }
  - { offsetInCU: 0x198F, offset: 0x18FA2, size: 0x8, addend: 0x0, symName: __ZN4BBIN10readAccessEv, symObjAddr: 0x37F0, symBinAddr: 0x4810, symSize: 0x80 }
  - { offsetInCU: 0x1A57, offset: 0x1906A, size: 0x8, addend: 0x0, symName: __ZN4BBIND1Ev, symObjAddr: 0x3870, symBinAddr: 0x4890, symSize: 0x10 }
  - { offsetInCU: 0x1A8C, offset: 0x1909F, size: 0x8, addend: 0x0, symName: __ZN4BBIND0Ev, symObjAddr: 0x3880, symBinAddr: 0x48A0, symSize: 0x10 }
  - { offsetInCU: 0x1B3B, offset: 0x1914E, size: 0x8, addend: 0x0, symName: __ZN4BFCL10readAccessEv, symObjAddr: 0x3890, symBinAddr: 0x48B0, symSize: 0x10 }
  - { offsetInCU: 0x1B6E, offset: 0x19181, size: 0x8, addend: 0x0, symName: __ZN4BFCLD1Ev, symObjAddr: 0x38A0, symBinAddr: 0x48C0, symSize: 0x10 }
  - { offsetInCU: 0x1BA3, offset: 0x191B6, size: 0x8, addend: 0x0, symName: __ZN4BFCLD0Ev, symObjAddr: 0x38B0, symBinAddr: 0x48D0, symSize: 0x10 }
  - { offsetInCU: 0x1C52, offset: 0x19265, size: 0x8, addend: 0x0, symName: __ZN4BNum10readAccessEv, symObjAddr: 0x38C0, symBinAddr: 0x48E0, symSize: 0x20 }
  - { offsetInCU: 0x1CA2, offset: 0x192B5, size: 0x8, addend: 0x0, symName: __ZN4BNumD1Ev, symObjAddr: 0x38E0, symBinAddr: 0x4900, symSize: 0x10 }
  - { offsetInCU: 0x1CD7, offset: 0x192EA, size: 0x8, addend: 0x0, symName: __ZN4BNumD0Ev, symObjAddr: 0x38F0, symBinAddr: 0x4910, symSize: 0x10 }
  - { offsetInCU: 0x1D0F, offset: 0x19322, size: 0x8, addend: 0x0, symName: __ZN4BSInD1Ev, symObjAddr: 0x39A0, symBinAddr: 0x49C0, symSize: 0x10 }
  - { offsetInCU: 0x1D44, offset: 0x19357, size: 0x8, addend: 0x0, symName: __ZN4BSInD0Ev, symObjAddr: 0x39B0, symBinAddr: 0x49D0, symSize: 0x10 }
  - { offsetInCU: 0x1DF3, offset: 0x19406, size: 0x8, addend: 0x0, symName: __ZN4BRSC10readAccessEv, symObjAddr: 0x39C0, symBinAddr: 0x49E0, symSize: 0x70 }
  - { offsetInCU: 0x1E61, offset: 0x19474, size: 0x8, addend: 0x0, symName: __ZN4BRSCD1Ev, symObjAddr: 0x3A30, symBinAddr: 0x4A50, symSize: 0x10 }
  - { offsetInCU: 0x1E96, offset: 0x194A9, size: 0x8, addend: 0x0, symName: __ZN4BRSCD0Ev, symObjAddr: 0x3A40, symBinAddr: 0x4A60, symSize: 0x10 }
  - { offsetInCU: 0x1F45, offset: 0x19558, size: 0x8, addend: 0x0, symName: __ZN4CHBI10readAccessEv, symObjAddr: 0x3A50, symBinAddr: 0x4A70, symSize: 0x40 }
  - { offsetInCU: 0x1FEA, offset: 0x195FD, size: 0x8, addend: 0x0, symName: __ZN4CHBID1Ev, symObjAddr: 0x3A90, symBinAddr: 0x4AB0, symSize: 0x10 }
  - { offsetInCU: 0x201F, offset: 0x19632, size: 0x8, addend: 0x0, symName: __ZN4CHBID0Ev, symObjAddr: 0x3AA0, symBinAddr: 0x4AC0, symSize: 0x10 }
  - { offsetInCU: 0x20CE, offset: 0x196E1, size: 0x8, addend: 0x0, symName: __ZN4CHBV10readAccessEv, symObjAddr: 0x3AB0, symBinAddr: 0x4AD0, symSize: 0x40 }
  - { offsetInCU: 0x2173, offset: 0x19786, size: 0x8, addend: 0x0, symName: __ZN4CHBVD1Ev, symObjAddr: 0x3AF0, symBinAddr: 0x4B10, symSize: 0x10 }
  - { offsetInCU: 0x21A8, offset: 0x197BB, size: 0x8, addend: 0x0, symName: __ZN4CHBVD0Ev, symObjAddr: 0x3B00, symBinAddr: 0x4B20, symSize: 0x10 }
  - { offsetInCU: 0x2257, offset: 0x1986A, size: 0x8, addend: 0x0, symName: __ZN4CHLC10readAccessEv, symObjAddr: 0x3B10, symBinAddr: 0x4B30, symSize: 0x80 }
  - { offsetInCU: 0x22C5, offset: 0x198D8, size: 0x8, addend: 0x0, symName: __ZN4CHLCD1Ev, symObjAddr: 0x3B90, symBinAddr: 0x4BB0, symSize: 0x10 }
  - { offsetInCU: 0x22FA, offset: 0x1990D, size: 0x8, addend: 0x0, symName: __ZN4CHLCD0Ev, symObjAddr: 0x3BA0, symBinAddr: 0x4BC0, symSize: 0x10 }
  - { offsetInCU: 0x23A9, offset: 0x199BC, size: 0x8, addend: 0x0, symName: __ZN4TB0T10readAccessEv, symObjAddr: 0x3BB0, symBinAddr: 0x4BD0, symSize: 0x70 }
  - { offsetInCU: 0x2443, offset: 0x19A56, size: 0x8, addend: 0x0, symName: __ZN4TB0TD1Ev, symObjAddr: 0x3C20, symBinAddr: 0x4C40, symSize: 0x10 }
  - { offsetInCU: 0x2478, offset: 0x19A8B, size: 0x8, addend: 0x0, symName: __ZN4TB0TD0Ev, symObjAddr: 0x3C30, symBinAddr: 0x4C50, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x19BB8, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusControllerD1Ev, symObjAddr: 0x3C40, symBinAddr: 0x4C60, symSize: 0x10 }
  - { offsetInCU: 0x3F, offset: 0x19BD0, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController10gMetaClassE, symObjAddr: 0x4D090, symBinAddr: 0x9CC8, symSize: 0x0 }
  - { offsetInCU: 0xFBE, offset: 0x1AB4F, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController9metaClassE, symObjAddr: 0x7230, symBinAddr: 0x82E0, symSize: 0x0 }
  - { offsetInCU: 0xFD5, offset: 0x1AB66, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController10superClassE, symObjAddr: 0x7238, symBinAddr: 0x82E8, symSize: 0x0 }
  - { offsetInCU: 0x1051, offset: 0x1ABE2, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusControllerD1Ev, symObjAddr: 0x3C40, symBinAddr: 0x4C60, symSize: 0x10 }
  - { offsetInCU: 0x10C8, offset: 0x1AC59, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusControllerD0Ev, symObjAddr: 0x3C50, symBinAddr: 0x4C70, symSize: 0x30 }
  - { offsetInCU: 0x1151, offset: 0x1ACE2, size: 0x8, addend: 0x0, symName: __ZNK18SMCSMBusController12getMetaClassEv, symObjAddr: 0x3C80, symBinAddr: 0x4CA0, symSize: 0x10 }
  - { offsetInCU: 0x1181, offset: 0x1AD12, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController4initEP12OSDictionary, symObjAddr: 0x3C90, symBinAddr: 0x4CB0, symSize: 0x40 }
  - { offsetInCU: 0x11D3, offset: 0x1AD64, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController5probeEP9IOServicePi, symObjAddr: 0x3CD0, symBinAddr: 0x4CF0, symSize: 0x60 }
  - { offsetInCU: 0x124A, offset: 0x1ADDB, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController5startEP9IOService, symObjAddr: 0x3D30, symBinAddr: 0x4D50, symSize: 0x270 }
  - { offsetInCU: 0x1380, offset: 0x1AF11, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController4stopEP9IOService, symObjAddr: 0x3FA0, symBinAddr: 0x4FC0, symSize: 0x20 }
  - { offsetInCU: 0x1388, offset: 0x1AF19, size: 0x8, addend: 0x0, symName: __ZNK18SMCSMBusController11getWorkLoopEv, symObjAddr: 0x3FC0, symBinAddr: 0x4FE0, symSize: 0x10 }
  - { offsetInCU: 0x13BC, offset: 0x1AF4D, size: 0x8, addend: 0x0, symName: __ZNK18SMCSMBusController11getWorkLoopEv, symObjAddr: 0x3FC0, symBinAddr: 0x4FE0, symSize: 0x10 }
  - { offsetInCU: 0x13EE, offset: 0x1AF7F, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController13setPowerStateEmP9IOService, symObjAddr: 0x3FD0, symBinAddr: 0x4FF0, symSize: 0x80 }
  - { offsetInCU: 0x14DB, offset: 0x1B06C, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController12startRequestEP14IOSMBusRequest, symObjAddr: 0x4050, symBinAddr: 0x5070, symSize: 0x850 }
  - { offsetInCU: 0x1CC8, offset: 0x1B859, size: 0x8, addend: 0x0, symName: '__ZZN18SMCSMBusController5startEP9IOServiceEN3$_08__invokeEP8OSObjectP18IOTimerEventSource', symObjAddr: 0x48A0, symBinAddr: 0x58C0, symSize: 0xF0 }
  - { offsetInCU: 0x1D78, offset: 0x1B909, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController22handleACPINotificationEPv, symObjAddr: 0x4990, symBinAddr: 0x59B0, symSize: 0x140 }
  - { offsetInCU: 0x1EF2, offset: 0x1BA83, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController9MetaClassD1Ev, symObjAddr: 0x4AD0, symBinAddr: 0x5AF0, symSize: 0x10 }
  - { offsetInCU: 0x1F69, offset: 0x1BAFA, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController9MetaClassD0Ev, symObjAddr: 0x4AE0, symBinAddr: 0x5B00, symSize: 0x10 }
  - { offsetInCU: 0x2037, offset: 0x1BBC8, size: 0x8, addend: 0x0, symName: __ZNK18SMCSMBusController9MetaClass5allocEv, symObjAddr: 0x4AF0, symBinAddr: 0x5B10, symSize: 0xE0 }
  - { offsetInCU: 0x2106, offset: 0x1BC97, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_SMCSMBusController.cpp, symObjAddr: 0x4BD0, symBinAddr: 0x5BF0, symSize: 0x40 }
  - { offsetInCU: 0x2192, offset: 0x1BD23, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a.70, symObjAddr: 0x4C10, symBinAddr: 0x5C30, symSize: 0x20 }
  - { offsetInCU: 0x2200, offset: 0x1BD91, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController9MetaClassC1Ev, symObjAddr: 0x4C30, symBinAddr: 0x5C50, symSize: 0x40 }
  - { offsetInCU: 0x2257, offset: 0x1BDE8, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusControllerC2EPK11OSMetaClass, symObjAddr: 0x4C70, symBinAddr: 0x5C90, symSize: 0xC0 }
  - { offsetInCU: 0x22C4, offset: 0x1BE55, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusControllerC1EPK11OSMetaClass, symObjAddr: 0x4D30, symBinAddr: 0x5D50, symSize: 0xC0 }
  - { offsetInCU: 0x2346, offset: 0x1BED7, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusControllerD2Ev, symObjAddr: 0x4DF0, symBinAddr: 0x5E10, symSize: 0x10 }
  - { offsetInCU: 0x2371, offset: 0x1BF02, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController9MetaClassC2Ev, symObjAddr: 0x4E00, symBinAddr: 0x5E20, symSize: 0x40 }
  - { offsetInCU: 0x239E, offset: 0x1BF2F, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusControllerC1Ev, symObjAddr: 0x4E40, symBinAddr: 0x5E60, symSize: 0xD0 }
  - { offsetInCU: 0x23F5, offset: 0x1BF86, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusControllerC2Ev, symObjAddr: 0x4F10, symBinAddr: 0x5F30, symSize: 0xD0 }
  - { offsetInCU: 0x2423, offset: 0x1BFB4, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController14setReceiveDataEP18IOSMBusTransactiont, symObjAddr: 0x4FE0, symBinAddr: 0x6000, symSize: 0x20 }
  - { offsetInCU: 0x2474, offset: 0x1C005, size: 0x8, addend: 0x0, symName: __ZN18SMCSMBusController26handleBatteryCommandsEventEv, symObjAddr: 0x5000, symBinAddr: 0x6020, symSize: 0xC0 }
  - { offsetInCU: 0x27, offset: 0x1C093, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManagerD1Ev, symObjAddr: 0x50C0, symBinAddr: 0x60E0, symSize: 0x10 }
  - { offsetInCU: 0x3F, offset: 0x1C0AB, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManager10gMetaClassE, symObjAddr: 0x4D0B8, symBinAddr: 0x9CF0, symSize: 0x0 }
  - { offsetInCU: 0xD3E, offset: 0x1CDAA, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManager9metaClassE, symObjAddr: 0x7C38, symBinAddr: 0x8CE8, symSize: 0x0 }
  - { offsetInCU: 0xD55, offset: 0x1CDC1, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManager10superClassE, symObjAddr: 0x7C40, symBinAddr: 0x8CF0, symSize: 0x0 }
  - { offsetInCU: 0xD77, offset: 0x1CDE3, size: 0x8, addend: 0x0, symName: _SMCBatteryManager_debugPrintDelay, symObjAddr: 0x4D0E8, symBinAddr: 0x9D20, symSize: 0x0 }
  - { offsetInCU: 0xD92, offset: 0x1CDFE, size: 0x8, addend: 0x0, symName: _smc_battery_manager_started, symObjAddr: 0x4D0EC, symBinAddr: 0x9D24, symSize: 0x0 }
  - { offsetInCU: 0xDF9, offset: 0x1CE65, size: 0x8, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0x8BE0, symBinAddr: 0x74F0, symSize: 0x0 }
  - { offsetInCU: 0xEA9, offset: 0x1CF15, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManagerD1Ev, symObjAddr: 0x50C0, symBinAddr: 0x60E0, symSize: 0x10 }
  - { offsetInCU: 0xF20, offset: 0x1CF8C, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManagerD0Ev, symObjAddr: 0x50D0, symBinAddr: 0x60F0, symSize: 0x30 }
  - { offsetInCU: 0xFA9, offset: 0x1D015, size: 0x8, addend: 0x0, symName: __ZNK17SMCBatteryManager12getMetaClassEv, symObjAddr: 0x5100, symBinAddr: 0x6120, symSize: 0x10 }
  - { offsetInCU: 0xFE4, offset: 0x1D050, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManager5probeEP9IOServicePi, symObjAddr: 0x5110, symBinAddr: 0x6130, symSize: 0x60 }
  - { offsetInCU: 0x2348, offset: 0x1E3B4, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManager5startEP9IOService, symObjAddr: 0x5170, symBinAddr: 0x6190, symSize: 0xB50 }
  - { offsetInCU: 0x3BB2, offset: 0x1FC1E, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManager4stopEP9IOService, symObjAddr: 0x5CC0, symBinAddr: 0x6CE0, symSize: 0x20 }
  - { offsetInCU: 0x3BBA, offset: 0x1FC26, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValueC2Ev, symObjAddr: 0x5CE0, symBinAddr: 0x6D00, symSize: 0xB0 }
  - { offsetInCU: 0x3BF1, offset: 0x1FC5D, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValueC2Ev, symObjAddr: 0x5CE0, symBinAddr: 0x6D00, symSize: 0xB0 }
  - { offsetInCU: 0x3C53, offset: 0x1FCBF, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCKeyValue7compareEPKvS1_, symObjAddr: 0x5D90, symBinAddr: 0x6DB0, symSize: 0x30 }
  - { offsetInCU: 0x3CCB, offset: 0x1FD37, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManager23vsmcNotificationHandlerEPvS0_P9IOServiceP10IONotifier, symObjAddr: 0x5DC0, symBinAddr: 0x6DE0, symSize: 0xA0 }
  - { offsetInCU: 0x3D91, offset: 0x1FDFD, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManager9MetaClassD1Ev, symObjAddr: 0x5E60, symBinAddr: 0x6E80, symSize: 0x10 }
  - { offsetInCU: 0x3E08, offset: 0x1FE74, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManager9MetaClassD0Ev, symObjAddr: 0x5E70, symBinAddr: 0x6E90, symSize: 0x10 }
  - { offsetInCU: 0x3F5D, offset: 0x1FFC9, size: 0x8, addend: 0x0, symName: __ZNK17SMCBatteryManager9MetaClass5allocEv, symObjAddr: 0x5E80, symBinAddr: 0x6EA0, symSize: 0xC0 }
  - { offsetInCU: 0x40A2, offset: 0x2010E, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_SMCBatteryManager.cpp, symObjAddr: 0x5F40, symBinAddr: 0x6F60, symSize: 0x40 }
  - { offsetInCU: 0x412E, offset: 0x2019A, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a.88, symObjAddr: 0x5F80, symBinAddr: 0x6FA0, symSize: 0x20 }
  - { offsetInCU: 0x419C, offset: 0x20208, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManager9MetaClassC1Ev, symObjAddr: 0x5FA0, symBinAddr: 0x6FC0, symSize: 0x40 }
  - { offsetInCU: 0x41F3, offset: 0x2025F, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManagerC2EPK11OSMetaClass, symObjAddr: 0x5FE0, symBinAddr: 0x7000, symSize: 0x90 }
  - { offsetInCU: 0x41FB, offset: 0x20267, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManagerC1EPK11OSMetaClass, symObjAddr: 0x6070, symBinAddr: 0x7090, symSize: 0x90 }
  - { offsetInCU: 0x42D6, offset: 0x20342, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManagerC1EPK11OSMetaClass, symObjAddr: 0x6070, symBinAddr: 0x7090, symSize: 0x90 }
  - { offsetInCU: 0x42DE, offset: 0x2034A, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManagerD2Ev, symObjAddr: 0x6100, symBinAddr: 0x7120, symSize: 0x10 }
  - { offsetInCU: 0x43CE, offset: 0x2043A, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManagerD2Ev, symObjAddr: 0x6100, symBinAddr: 0x7120, symSize: 0x10 }
  - { offsetInCU: 0x43F9, offset: 0x20465, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManager9MetaClassC2Ev, symObjAddr: 0x6110, symBinAddr: 0x7130, symSize: 0x40 }
  - { offsetInCU: 0x4426, offset: 0x20492, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManagerC1Ev, symObjAddr: 0x6150, symBinAddr: 0x7170, symSize: 0xA0 }
  - { offsetInCU: 0x442E, offset: 0x2049A, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManagerC2Ev, symObjAddr: 0x61F0, symBinAddr: 0x7210, symSize: 0xA0 }
  - { offsetInCU: 0x445F, offset: 0x204CB, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManagerC2Ev, symObjAddr: 0x61F0, symBinAddr: 0x7210, symSize: 0xA0 }
  - { offsetInCU: 0x44F3, offset: 0x2055F, size: 0x8, addend: 0x0, symName: __ZN17SMCBatteryManagerC2Ev, symObjAddr: 0x61F0, symBinAddr: 0x7210, symSize: 0xA0 }
  - { offsetInCU: 0x44FB, offset: 0x20567, size: 0x8, addend: 0x0, symName: _SMCBatteryManager_kern_start, symObjAddr: 0x6290, symBinAddr: 0x72B0, symSize: 0x160 }
  - { offsetInCU: 0x45E3, offset: 0x2064F, size: 0x8, addend: 0x0, symName: _SMCBatteryManager_kern_start, symObjAddr: 0x6290, symBinAddr: 0x72B0, symSize: 0x160 }
  - { offsetInCU: 0x46C8, offset: 0x20734, size: 0x8, addend: 0x0, symName: _SMCBatteryManager_kern_stop, symObjAddr: 0x63F0, symBinAddr: 0x7410, symSize: 0xB }
...
