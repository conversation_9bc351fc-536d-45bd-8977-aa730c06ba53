---
triple:          'i386-apple-darwin'
binary-path:     '/Users/<USER>/work/VirtualSMC/VirtualSMC/build/Release/SMCBatteryManager.kext/Contents/MacOS/SMCBatteryManager'
relocations:
  - { offsetInCU: 0x35, offset: 0x35, size: 0x4, addend: 0x0, symName: _kmod_info, symObjAddr: 0x0, symBinAddr: 0x8128, symSize: 0x0 }
  - { offsetInCU: 0x206, offset: 0x206, size: 0x4, addend: 0x0, symName: __realmain, symObjAddr: 0xA8, symBinAddr: 0x81D0, symSize: 0x0 }
  - { offsetInCU: 0x218, offset: 0x218, size: 0x4, addend: 0x0, symName: __antimain, symObjAddr: 0xAC, symBinAddr: 0x81D4, symSize: 0x0 }
  - { offsetInCU: 0x22A, offset: 0x22A, size: 0x4, addend: 0x0, symName: __kext_apple_cc, symObjAddr: 0xB0, symBinAddr: 0x81D8, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x256, size: 0x4, addend: 0x0, symName: __ZN13ACPIACAdapter12updateStatusEv, symObjAddr: 0x0, symBinAddr: 0x0, symSize: 0x6A }
  - { offsetInCU: 0xE32E, offset: 0xE55D, size: 0x4, addend: 0x0, symName: __ZN13ACPIACAdapter12updateStatusEv, symObjAddr: 0x0, symBinAddr: 0x0, symSize: 0x6A }
  - { offsetInCU: 0x27, offset: 0xE5CA, size: 0x4, addend: 0x0, symName: __ZN11ACPIBattery18getNumberFromArrayEP7OSArrayj, symObjAddr: 0x0, symBinAddr: 0x70, symSize: 0x50 }
  - { offsetInCU: 0x74C7, offset: 0x15A6A, size: 0x4, addend: 0x0, symName: __ZN11ACPIBattery18getNumberFromArrayEP7OSArrayj, symObjAddr: 0x0, symBinAddr: 0x70, symSize: 0x50 }
  - { offsetInCU: 0x74F9, offset: 0x15A9C, size: 0x4, addend: 0x0, symName: __ZN11ACPIBattery18getStringFromArrayEP7OSArrayjPcj, symObjAddr: 0x50, symBinAddr: 0xC0, symSize: 0xF0 }
  - { offsetInCU: 0x75F4, offset: 0x15B97, size: 0x4, addend: 0x0, symName: __ZN11ACPIBattery14getBatteryInfoER11BatteryInfob, symObjAddr: 0x140, symBinAddr: 0x1B0, symSize: 0x9E0 }
  - { offsetInCU: 0x7ABF, offset: 0x16062, size: 0x4, addend: 0x0, symName: __ZN11ACPIBattery20updateRealTimeStatusEb, symObjAddr: 0xB20, symBinAddr: 0xB90, symSize: 0xB10 }
  - { offsetInCU: 0x7E05, offset: 0x163A8, size: 0x4, addend: 0x0, symName: __ZN11ACPIBattery18updateStaticStatusEPb, symObjAddr: 0x1630, symBinAddr: 0x16A0, symSize: 0x5F0 }
  - { offsetInCU: 0x7EA5, offset: 0x16448, size: 0x4, addend: 0x0, symName: __ZN11ACPIBattery22calculateBatteryStatusEv, symObjAddr: 0x1C20, symBinAddr: 0x1C90, symSize: 0x8D }
  - { offsetInCU: 0x27, offset: 0x164BD, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x1D20, symSize: 0x30 }
  - { offsetInCU: 0x36, offset: 0x164CC, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager8instanceE, symObjAddr: 0x2ADE4, symBinAddr: 0x81DC, symSize: 0x0 }
  - { offsetInCU: 0x7901, offset: 0x1DD97, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager10gMetaClassE, symObjAddr: 0x2ADE8, symBinAddr: 0x81E0, symSize: 0x0 }
  - { offsetInCU: 0x7910, offset: 0x1DDA6, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager9metaClassE, symObjAddr: 0x1748, symBinAddr: 0x71F0, symSize: 0x0 }
  - { offsetInCU: 0x791F, offset: 0x1DDB5, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager10superClassE, symObjAddr: 0x174C, symBinAddr: 0x71F4, symSize: 0x0 }
  - { offsetInCU: 0x7948, offset: 0x1DDDE, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x1D20, symSize: 0x30 }
  - { offsetInCU: 0x7998, offset: 0x1DE2E, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager9MetaClassD1Ev, symObjAddr: 0x30, symBinAddr: 0x1D50, symSize: 0x10 }
  - { offsetInCU: 0x7A3F, offset: 0x1DED5, size: 0x4, addend: 0x0, symName: __ZN14BatteryManagerC2EPK11OSMetaClass, symObjAddr: 0x40, symBinAddr: 0x1D60, symSize: 0x430 }
  - { offsetInCU: 0x7ACD, offset: 0x1DF63, size: 0x4, addend: 0x0, symName: __ZN14BatteryManagerC1EPK11OSMetaClass, symObjAddr: 0x470, symBinAddr: 0x2190, symSize: 0x10 }
  - { offsetInCU: 0x7B03, offset: 0x1DF99, size: 0x4, addend: 0x0, symName: __ZN14BatteryManagerD2Ev, symObjAddr: 0x480, symBinAddr: 0x21A0, symSize: 0x10 }
  - { offsetInCU: 0x7B3A, offset: 0x1DFD0, size: 0x4, addend: 0x0, symName: __ZN14BatteryManagerD1Ev, symObjAddr: 0x490, symBinAddr: 0x21B0, symSize: 0x10 }
  - { offsetInCU: 0x7B8A, offset: 0x1E020, size: 0x4, addend: 0x0, symName: __ZN14BatteryManagerD0Ev, symObjAddr: 0x4A0, symBinAddr: 0x21C0, symSize: 0x30 }
  - { offsetInCU: 0x7BE4, offset: 0x1E07A, size: 0x4, addend: 0x0, symName: __ZNK14BatteryManager12getMetaClassEv, symObjAddr: 0x4D0, symBinAddr: 0x21F0, symSize: 0x10 }
  - { offsetInCU: 0x7C04, offset: 0x1E09A, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager9MetaClassC2Ev, symObjAddr: 0x4E0, symBinAddr: 0x2200, symSize: 0x30 }
  - { offsetInCU: 0x7C3B, offset: 0x1E0D1, size: 0x4, addend: 0x0, symName: __ZNK14BatteryManager9MetaClass5allocEv, symObjAddr: 0x510, symBinAddr: 0x2230, symSize: 0x30 }
  - { offsetInCU: 0x7C73, offset: 0x1E109, size: 0x4, addend: 0x0, symName: __ZN14BatteryManagerC1Ev, symObjAddr: 0x540, symBinAddr: 0x2260, symSize: 0x10 }
  - { offsetInCU: 0x7C8F, offset: 0x1E125, size: 0x4, addend: 0x0, symName: __ZN14BatteryManagerC2Ev, symObjAddr: 0x550, symBinAddr: 0x2270, symSize: 0x450 }
  - { offsetInCU: 0x7D0F, offset: 0x1E1A5, size: 0x4, addend: 0x0, symName: __ZN11BatteryInfo12validateDataEi, symObjAddr: 0x9A0, symBinAddr: 0x26C0, symSize: 0xE0 }
  - { offsetInCU: 0x7DDD, offset: 0x1E273, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager12checkDevicesEv, symObjAddr: 0xA80, symBinAddr: 0x27A0, symSize: 0x240 }
  - { offsetInCU: 0x7F1D, offset: 0x1E3B3, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager19externalPowerNotifyEb, symObjAddr: 0xCC0, symBinAddr: 0x29E0, symSize: 0x30 }
  - { offsetInCU: 0x7F44, offset: 0x1E3DA, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager18decrementQuickPollEv, symObjAddr: 0xCF0, symBinAddr: 0x2A10, symSize: 0x30 }
  - { offsetInCU: 0x7F73, offset: 0x1E409, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager13findBatteriesEv, symObjAddr: 0xD20, symBinAddr: 0x2A40, symSize: 0x1A0 }
  - { offsetInCU: 0x7F77, offset: 0x1E40D, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager16acpiNotificationEPvS0_mP9IOServiceS0_j, symObjAddr: 0xEC0, symBinAddr: 0x2BE0, symSize: 0x90 }
  - { offsetInCU: 0x7FE5, offset: 0x1E47B, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager16acpiNotificationEPvS0_mP9IOServiceS0_j, symObjAddr: 0xEC0, symBinAddr: 0x2BE0, symSize: 0x90 }
  - { offsetInCU: 0x8079, offset: 0x1E50F, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager14findACAdaptersEv, symObjAddr: 0xF50, symBinAddr: 0x2C70, symSize: 0x180 }
  - { offsetInCU: 0x80E4, offset: 0x1E57A, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager9subscribeEPFiPvES0_, symObjAddr: 0x10D0, symBinAddr: 0x2DF0, symSize: 0x20 }
  - { offsetInCU: 0x8123, offset: 0x1E5B9, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager4wakeEv, symObjAddr: 0x10F0, symBinAddr: 0x2E10, symSize: 0x60 }
  - { offsetInCU: 0x8155, offset: 0x1E5EB, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager5sleepEv, symObjAddr: 0x1150, symBinAddr: 0x2E70, symSize: 0x20 }
  - { offsetInCU: 0x8178, offset: 0x1E60E, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager5probeEv, symObjAddr: 0x1170, symBinAddr: 0x2E90, symSize: 0x190 }
  - { offsetInCU: 0x8210, offset: 0x1E6A6, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager5startEv, symObjAddr: 0x1300, symBinAddr: 0x3020, symSize: 0x50 }
  - { offsetInCU: 0x8234, offset: 0x1E6CA, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager18batteriesConnectedEv, symObjAddr: 0x1350, symBinAddr: 0x3070, symSize: 0x50 }
  - { offsetInCU: 0x8272, offset: 0x1E708, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager17adaptersConnectedEv, symObjAddr: 0x13A0, symBinAddr: 0x30C0, symSize: 0x80 }
  - { offsetInCU: 0x82CA, offset: 0x1E760, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager16batteriesAreFullEv, symObjAddr: 0x1420, symBinAddr: 0x3140, symSize: 0x40 }
  - { offsetInCU: 0x82CE, offset: 0x1E764, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager22externalPowerConnectedEv, symObjAddr: 0x1460, symBinAddr: 0x3180, symSize: 0x140 }
  - { offsetInCU: 0x8308, offset: 0x1E79E, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager22externalPowerConnectedEv, symObjAddr: 0x1460, symBinAddr: 0x3180, symSize: 0x140 }
  - { offsetInCU: 0x8368, offset: 0x1E7FE, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager22calculateBatteryStatusEm, symObjAddr: 0x15A0, symBinAddr: 0x32C0, symSize: 0x30 }
  - { offsetInCU: 0x839B, offset: 0x1E831, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager12createSharedEv, symObjAddr: 0x15D0, symBinAddr: 0x32F0, symSize: 0xC0 }
  - { offsetInCU: 0x83E3, offset: 0x1E879, size: 0x4, addend: 0x0, symName: __ZN14BatteryManager9MetaClassD0Ev, symObjAddr: 0x1690, symBinAddr: 0x33B0, symSize: 0x10 }
  - { offsetInCU: 0x8492, offset: 0x1E928, size: 0x4, addend: 0x0, symName: '__ZZN14BatteryManager5probeEvEN3$_08__invokeEP8OSObjectP18IOTimerEventSource', symObjAddr: 0x16A0, symBinAddr: 0x33C0, symSize: 0x50 }
  - { offsetInCU: 0x8516, offset: 0x1E9AC, size: 0x4, addend: 0x0, symName: __GLOBAL__sub_I_BatteryManager.cpp, symObjAddr: 0x16F0, symBinAddr: 0x3410, symSize: 0x40 }
  - { offsetInCU: 0x8566, offset: 0x1E9FC, size: 0x4, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x1730, symBinAddr: 0x3450, symSize: 0x17 }
  - { offsetInCU: 0x27, offset: 0x1EDF3, size: 0x4, addend: 0x0, symName: __ZN4ACID10readAccessEv, symObjAddr: 0x0, symBinAddr: 0x3470, symSize: 0x80 }
  - { offsetInCU: 0x34C, offset: 0x1F118, size: 0x4, addend: 0x0, symName: __ZN4BSIn10readAccessEv, symObjAddr: 0x490, symBinAddr: 0x3900, symSize: 0x60 }
  - { offsetInCU: 0x463, offset: 0x1F22F, size: 0x4, addend: 0x0, symName: __ZN4ACID10readAccessEv, symObjAddr: 0x0, symBinAddr: 0x3470, symSize: 0x80 }
  - { offsetInCU: 0x51B, offset: 0x1F2E7, size: 0x4, addend: 0x0, symName: __ZN4ACIN10readAccessEv, symObjAddr: 0x80, symBinAddr: 0x34F0, symSize: 0x40 }
  - { offsetInCU: 0x51F, offset: 0x1F2EB, size: 0x4, addend: 0x0, symName: __ZN4AC_N10readAccessEv, symObjAddr: 0xC0, symBinAddr: 0x3530, symSize: 0x20 }
  - { offsetInCU: 0x5D3, offset: 0x1F39F, size: 0x4, addend: 0x0, symName: __ZN4AC_N10readAccessEv, symObjAddr: 0xC0, symBinAddr: 0x3530, symSize: 0x20 }
  - { offsetInCU: 0x6BE, offset: 0x1F48A, size: 0x4, addend: 0x0, symName: __ZN4B0AC10readAccessEv, symObjAddr: 0xE0, symBinAddr: 0x3550, symSize: 0x50 }
  - { offsetInCU: 0x77D, offset: 0x1F549, size: 0x4, addend: 0x0, symName: __ZN4B0AV10readAccessEv, symObjAddr: 0x130, symBinAddr: 0x35A0, symSize: 0x50 }
  - { offsetInCU: 0x83C, offset: 0x1F608, size: 0x4, addend: 0x0, symName: __ZN4B0BI10readAccessEv, symObjAddr: 0x180, symBinAddr: 0x35F0, symSize: 0x50 }
  - { offsetInCU: 0x8D8, offset: 0x1F6A4, size: 0x4, addend: 0x0, symName: __ZN4B0CT10readAccessEv, symObjAddr: 0x1D0, symBinAddr: 0x3640, symSize: 0x50 }
  - { offsetInCU: 0x997, offset: 0x1F763, size: 0x4, addend: 0x0, symName: __ZN4B0FC10readAccessEv, symObjAddr: 0x220, symBinAddr: 0x3690, symSize: 0x50 }
  - { offsetInCU: 0xA56, offset: 0x1F822, size: 0x4, addend: 0x0, symName: __ZN4B0PS10readAccessEv, symObjAddr: 0x270, symBinAddr: 0x36E0, symSize: 0x10 }
  - { offsetInCU: 0xA5A, offset: 0x1F826, size: 0x4, addend: 0x0, symName: __ZN4B0RM10readAccessEv, symObjAddr: 0x280, symBinAddr: 0x36F0, symSize: 0x50 }
  - { offsetInCU: 0xAD0, offset: 0x1F89C, size: 0x4, addend: 0x0, symName: __ZN4B0RM10readAccessEv, symObjAddr: 0x280, symBinAddr: 0x36F0, symSize: 0x50 }
  - { offsetInCU: 0xB8F, offset: 0x1F95B, size: 0x4, addend: 0x0, symName: __ZN4B0St10readAccessEv, symObjAddr: 0x2D0, symBinAddr: 0x3740, symSize: 0x60 }
  - { offsetInCU: 0xC5F, offset: 0x1FA2B, size: 0x4, addend: 0x0, symName: __ZN4B0TF10readAccessEv, symObjAddr: 0x330, symBinAddr: 0x37A0, symSize: 0x60 }
  - { offsetInCU: 0xD2D, offset: 0x1FAF9, size: 0x4, addend: 0x0, symName: __ZN4BATP10readAccessEv, symObjAddr: 0x390, symBinAddr: 0x3800, symSize: 0x50 }
  - { offsetInCU: 0xDE5, offset: 0x1FBB1, size: 0x4, addend: 0x0, symName: __ZN4BBAD10readAccessEv, symObjAddr: 0x3E0, symBinAddr: 0x3850, symSize: 0x40 }
  - { offsetInCU: 0xE8C, offset: 0x1FC58, size: 0x4, addend: 0x0, symName: __ZN4BBIN10readAccessEv, symObjAddr: 0x420, symBinAddr: 0x3890, symSize: 0x40 }
  - { offsetInCU: 0xE90, offset: 0x1FC5C, size: 0x4, addend: 0x0, symName: __ZN4BFCL10readAccessEv, symObjAddr: 0x460, symBinAddr: 0x38D0, symSize: 0x10 }
  - { offsetInCU: 0xF44, offset: 0x1FD10, size: 0x4, addend: 0x0, symName: __ZN4BFCL10readAccessEv, symObjAddr: 0x460, symBinAddr: 0x38D0, symSize: 0x10 }
  - { offsetInCU: 0xFBE, offset: 0x1FD8A, size: 0x4, addend: 0x0, symName: __ZN4BNum10readAccessEv, symObjAddr: 0x470, symBinAddr: 0x38E0, symSize: 0x20 }
  - { offsetInCU: 0x1049, offset: 0x1FE15, size: 0x4, addend: 0x0, symName: __ZN4BRSC10readAccessEv, symObjAddr: 0x4F0, symBinAddr: 0x3960, symSize: 0x80 }
  - { offsetInCU: 0x10E5, offset: 0x1FEB1, size: 0x4, addend: 0x0, symName: __ZN4CHBI10readAccessEv, symObjAddr: 0x570, symBinAddr: 0x39E0, symSize: 0x40 }
  - { offsetInCU: 0x11A4, offset: 0x1FF70, size: 0x4, addend: 0x0, symName: __ZN4CHBV10readAccessEv, symObjAddr: 0x5B0, symBinAddr: 0x3A20, symSize: 0x40 }
  - { offsetInCU: 0x1263, offset: 0x2002F, size: 0x4, addend: 0x0, symName: __ZN4CHLC10readAccessEv, symObjAddr: 0x5F0, symBinAddr: 0x3A60, symSize: 0x80 }
  - { offsetInCU: 0x12FF, offset: 0x200CB, size: 0x4, addend: 0x0, symName: __ZN4TB0T10readAccessEv, symObjAddr: 0x670, symBinAddr: 0x3AE0, symSize: 0x60 }
  - { offsetInCU: 0x1360, offset: 0x2012C, size: 0x4, addend: 0x0, symName: __ZN15VirtualSMCValue11writeAccessEv, symObjAddr: 0x6D0, symBinAddr: 0x3B40, symSize: 0x10 }
  - { offsetInCU: 0x1382, offset: 0x2014E, size: 0x4, addend: 0x0, symName: __ZN4ACIDD1Ev, symObjAddr: 0x6E0, symBinAddr: 0x3B50, symSize: 0x10 }
  - { offsetInCU: 0x13AA, offset: 0x20176, size: 0x4, addend: 0x0, symName: __ZN4ACIDD0Ev, symObjAddr: 0x6F0, symBinAddr: 0x3B60, symSize: 0x10 }
  - { offsetInCU: 0x13D2, offset: 0x2019E, size: 0x4, addend: 0x0, symName: __ZN4ACIND1Ev, symObjAddr: 0x700, symBinAddr: 0x3B70, symSize: 0x10 }
  - { offsetInCU: 0x13FA, offset: 0x201C6, size: 0x4, addend: 0x0, symName: __ZN4ACIND0Ev, symObjAddr: 0x710, symBinAddr: 0x3B80, symSize: 0x10 }
  - { offsetInCU: 0x1422, offset: 0x201EE, size: 0x4, addend: 0x0, symName: __ZN4AC_ND1Ev, symObjAddr: 0x720, symBinAddr: 0x3B90, symSize: 0x10 }
  - { offsetInCU: 0x144A, offset: 0x20216, size: 0x4, addend: 0x0, symName: __ZN4AC_ND0Ev, symObjAddr: 0x730, symBinAddr: 0x3BA0, symSize: 0x10 }
  - { offsetInCU: 0x1472, offset: 0x2023E, size: 0x4, addend: 0x0, symName: __ZN4B0ACD1Ev, symObjAddr: 0x740, symBinAddr: 0x3BB0, symSize: 0x10 }
  - { offsetInCU: 0x149A, offset: 0x20266, size: 0x4, addend: 0x0, symName: __ZN4B0ACD0Ev, symObjAddr: 0x750, symBinAddr: 0x3BC0, symSize: 0x10 }
  - { offsetInCU: 0x14C2, offset: 0x2028E, size: 0x4, addend: 0x0, symName: __ZN4B0AVD1Ev, symObjAddr: 0x760, symBinAddr: 0x3BD0, symSize: 0x10 }
  - { offsetInCU: 0x14EA, offset: 0x202B6, size: 0x4, addend: 0x0, symName: __ZN4B0AVD0Ev, symObjAddr: 0x770, symBinAddr: 0x3BE0, symSize: 0x10 }
  - { offsetInCU: 0x1512, offset: 0x202DE, size: 0x4, addend: 0x0, symName: __ZN4B0BID1Ev, symObjAddr: 0x780, symBinAddr: 0x3BF0, symSize: 0x10 }
  - { offsetInCU: 0x153A, offset: 0x20306, size: 0x4, addend: 0x0, symName: __ZN4B0BID0Ev, symObjAddr: 0x790, symBinAddr: 0x3C00, symSize: 0x10 }
  - { offsetInCU: 0x1562, offset: 0x2032E, size: 0x4, addend: 0x0, symName: __ZN4B0CTD1Ev, symObjAddr: 0x7A0, symBinAddr: 0x3C10, symSize: 0x10 }
  - { offsetInCU: 0x158A, offset: 0x20356, size: 0x4, addend: 0x0, symName: __ZN4B0CTD0Ev, symObjAddr: 0x7B0, symBinAddr: 0x3C20, symSize: 0x10 }
  - { offsetInCU: 0x15B2, offset: 0x2037E, size: 0x4, addend: 0x0, symName: __ZN4B0FCD1Ev, symObjAddr: 0x7C0, symBinAddr: 0x3C30, symSize: 0x10 }
  - { offsetInCU: 0x15DA, offset: 0x203A6, size: 0x4, addend: 0x0, symName: __ZN4B0FCD0Ev, symObjAddr: 0x7D0, symBinAddr: 0x3C40, symSize: 0x10 }
  - { offsetInCU: 0x1602, offset: 0x203CE, size: 0x4, addend: 0x0, symName: __ZN4B0PSD1Ev, symObjAddr: 0x7E0, symBinAddr: 0x3C50, symSize: 0x10 }
  - { offsetInCU: 0x162A, offset: 0x203F6, size: 0x4, addend: 0x0, symName: __ZN4B0PSD0Ev, symObjAddr: 0x7F0, symBinAddr: 0x3C60, symSize: 0x10 }
  - { offsetInCU: 0x1652, offset: 0x2041E, size: 0x4, addend: 0x0, symName: __ZN4B0RMD1Ev, symObjAddr: 0x800, symBinAddr: 0x3C70, symSize: 0x10 }
  - { offsetInCU: 0x167A, offset: 0x20446, size: 0x4, addend: 0x0, symName: __ZN4B0RMD0Ev, symObjAddr: 0x810, symBinAddr: 0x3C80, symSize: 0x10 }
  - { offsetInCU: 0x16A2, offset: 0x2046E, size: 0x4, addend: 0x0, symName: __ZN4B0StD1Ev, symObjAddr: 0x820, symBinAddr: 0x3C90, symSize: 0x10 }
  - { offsetInCU: 0x16CA, offset: 0x20496, size: 0x4, addend: 0x0, symName: __ZN4B0StD0Ev, symObjAddr: 0x830, symBinAddr: 0x3CA0, symSize: 0x10 }
  - { offsetInCU: 0x16F2, offset: 0x204BE, size: 0x4, addend: 0x0, symName: __ZN4B0TFD1Ev, symObjAddr: 0x840, symBinAddr: 0x3CB0, symSize: 0x10 }
  - { offsetInCU: 0x171A, offset: 0x204E6, size: 0x4, addend: 0x0, symName: __ZN4B0TFD0Ev, symObjAddr: 0x850, symBinAddr: 0x3CC0, symSize: 0x10 }
  - { offsetInCU: 0x1742, offset: 0x2050E, size: 0x4, addend: 0x0, symName: __ZN4BATPD1Ev, symObjAddr: 0x860, symBinAddr: 0x3CD0, symSize: 0x10 }
  - { offsetInCU: 0x176A, offset: 0x20536, size: 0x4, addend: 0x0, symName: __ZN4BATPD0Ev, symObjAddr: 0x870, symBinAddr: 0x3CE0, symSize: 0x10 }
  - { offsetInCU: 0x1792, offset: 0x2055E, size: 0x4, addend: 0x0, symName: __ZN4BBADD1Ev, symObjAddr: 0x880, symBinAddr: 0x3CF0, symSize: 0x10 }
  - { offsetInCU: 0x17BA, offset: 0x20586, size: 0x4, addend: 0x0, symName: __ZN4BBADD0Ev, symObjAddr: 0x890, symBinAddr: 0x3D00, symSize: 0x10 }
  - { offsetInCU: 0x17E2, offset: 0x205AE, size: 0x4, addend: 0x0, symName: __ZN4BBIND1Ev, symObjAddr: 0x8A0, symBinAddr: 0x3D10, symSize: 0x10 }
  - { offsetInCU: 0x180A, offset: 0x205D6, size: 0x4, addend: 0x0, symName: __ZN4BBIND0Ev, symObjAddr: 0x8B0, symBinAddr: 0x3D20, symSize: 0x10 }
  - { offsetInCU: 0x1832, offset: 0x205FE, size: 0x4, addend: 0x0, symName: __ZN4BFCLD1Ev, symObjAddr: 0x8C0, symBinAddr: 0x3D30, symSize: 0x10 }
  - { offsetInCU: 0x185A, offset: 0x20626, size: 0x4, addend: 0x0, symName: __ZN4BFCLD0Ev, symObjAddr: 0x8D0, symBinAddr: 0x3D40, symSize: 0x10 }
  - { offsetInCU: 0x1882, offset: 0x2064E, size: 0x4, addend: 0x0, symName: __ZN4BNumD1Ev, symObjAddr: 0x8E0, symBinAddr: 0x3D50, symSize: 0x10 }
  - { offsetInCU: 0x18AA, offset: 0x20676, size: 0x4, addend: 0x0, symName: __ZN4BNumD0Ev, symObjAddr: 0x8F0, symBinAddr: 0x3D60, symSize: 0x10 }
  - { offsetInCU: 0x18D2, offset: 0x2069E, size: 0x4, addend: 0x0, symName: __ZN4BSInD1Ev, symObjAddr: 0x900, symBinAddr: 0x3D70, symSize: 0x10 }
  - { offsetInCU: 0x18FA, offset: 0x206C6, size: 0x4, addend: 0x0, symName: __ZN4BSInD0Ev, symObjAddr: 0x910, symBinAddr: 0x3D80, symSize: 0x10 }
  - { offsetInCU: 0x1922, offset: 0x206EE, size: 0x4, addend: 0x0, symName: __ZN4BRSCD1Ev, symObjAddr: 0x920, symBinAddr: 0x3D90, symSize: 0x10 }
  - { offsetInCU: 0x194A, offset: 0x20716, size: 0x4, addend: 0x0, symName: __ZN4BRSCD0Ev, symObjAddr: 0x930, symBinAddr: 0x3DA0, symSize: 0x10 }
  - { offsetInCU: 0x1972, offset: 0x2073E, size: 0x4, addend: 0x0, symName: __ZN4CHBID1Ev, symObjAddr: 0x940, symBinAddr: 0x3DB0, symSize: 0x10 }
  - { offsetInCU: 0x199A, offset: 0x20766, size: 0x4, addend: 0x0, symName: __ZN4CHBID0Ev, symObjAddr: 0x950, symBinAddr: 0x3DC0, symSize: 0x10 }
  - { offsetInCU: 0x19C2, offset: 0x2078E, size: 0x4, addend: 0x0, symName: __ZN4CHBVD1Ev, symObjAddr: 0x960, symBinAddr: 0x3DD0, symSize: 0x10 }
  - { offsetInCU: 0x19EA, offset: 0x207B6, size: 0x4, addend: 0x0, symName: __ZN4CHBVD0Ev, symObjAddr: 0x970, symBinAddr: 0x3DE0, symSize: 0x10 }
  - { offsetInCU: 0x1A12, offset: 0x207DE, size: 0x4, addend: 0x0, symName: __ZN4CHLCD1Ev, symObjAddr: 0x980, symBinAddr: 0x3DF0, symSize: 0x10 }
  - { offsetInCU: 0x1A3A, offset: 0x20806, size: 0x4, addend: 0x0, symName: __ZN4CHLCD0Ev, symObjAddr: 0x990, symBinAddr: 0x3E00, symSize: 0x10 }
  - { offsetInCU: 0x1A62, offset: 0x2082E, size: 0x4, addend: 0x0, symName: __ZN4TB0TD1Ev, symObjAddr: 0x9A0, symBinAddr: 0x3E10, symSize: 0x10 }
  - { offsetInCU: 0x1A8A, offset: 0x20856, size: 0x4, addend: 0x0, symName: __ZN4TB0TD0Ev, symObjAddr: 0x9B0, symBinAddr: 0x3E20, symSize: 0x9 }
  - { offsetInCU: 0x27, offset: 0x20918, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x3E30, symSize: 0x30 }
  - { offsetInCU: 0x36, offset: 0x20927, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController10gMetaClassE, symObjAddr: 0x2CFB0, symBinAddr: 0x81F8, symSize: 0x0 }
  - { offsetInCU: 0x735E, offset: 0x27C4F, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController9metaClassE, symObjAddr: 0x11A8, symBinAddr: 0x75E4, symSize: 0x0 }
  - { offsetInCU: 0x736D, offset: 0x27C5E, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController10superClassE, symObjAddr: 0x11AC, symBinAddr: 0x75E8, symSize: 0x0 }
  - { offsetInCU: 0x8384, offset: 0x28C75, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x3E30, symSize: 0x30 }
  - { offsetInCU: 0x83D4, offset: 0x28CC5, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController9MetaClassD1Ev, symObjAddr: 0x30, symBinAddr: 0x3E60, symSize: 0x10 }
  - { offsetInCU: 0x8409, offset: 0x28CFA, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusControllerC2EPK11OSMetaClass, symObjAddr: 0x40, symBinAddr: 0x3E70, symSize: 0xB0 }
  - { offsetInCU: 0x8453, offset: 0x28D44, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusControllerC1EPK11OSMetaClass, symObjAddr: 0xF0, symBinAddr: 0x3F20, symSize: 0xB0 }
  - { offsetInCU: 0x84AA, offset: 0x28D9B, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusControllerD2Ev, symObjAddr: 0x1A0, symBinAddr: 0x3FD0, symSize: 0x10 }
  - { offsetInCU: 0x84E1, offset: 0x28DD2, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusControllerD1Ev, symObjAddr: 0x1B0, symBinAddr: 0x3FE0, symSize: 0x10 }
  - { offsetInCU: 0x8531, offset: 0x28E22, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusControllerD0Ev, symObjAddr: 0x1C0, symBinAddr: 0x3FF0, symSize: 0x30 }
  - { offsetInCU: 0x858B, offset: 0x28E7C, size: 0x4, addend: 0x0, symName: __ZNK18SMCSMBusController12getMetaClassEv, symObjAddr: 0x1F0, symBinAddr: 0x4020, symSize: 0x10 }
  - { offsetInCU: 0x85AB, offset: 0x28E9C, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController9MetaClassC2Ev, symObjAddr: 0x200, symBinAddr: 0x4030, symSize: 0x30 }
  - { offsetInCU: 0x85FD, offset: 0x28EEE, size: 0x4, addend: 0x0, symName: __ZNK18SMCSMBusController9MetaClass5allocEv, symObjAddr: 0x230, symBinAddr: 0x4060, symSize: 0xD0 }
  - { offsetInCU: 0x864D, offset: 0x28F3E, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusControllerC1Ev, symObjAddr: 0x300, symBinAddr: 0x4130, symSize: 0xC0 }
  - { offsetInCU: 0x8682, offset: 0x28F73, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusControllerC2Ev, symObjAddr: 0x3C0, symBinAddr: 0x41F0, symSize: 0xC0 }
  - { offsetInCU: 0x869E, offset: 0x28F8F, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController4initEP12OSDictionary, symObjAddr: 0x480, symBinAddr: 0x42B0, symSize: 0x40 }
  - { offsetInCU: 0x86CF, offset: 0x28FC0, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController5probeEP9IOServicePl, symObjAddr: 0x4C0, symBinAddr: 0x42F0, symSize: 0x60 }
  - { offsetInCU: 0x870E, offset: 0x28FFF, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController5startEP9IOService, symObjAddr: 0x520, symBinAddr: 0x4350, symSize: 0x230 }
  - { offsetInCU: 0x8C78, offset: 0x29569, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController22handleACPINotificationEPv, symObjAddr: 0x750, symBinAddr: 0x4580, symSize: 0x90 }
  - { offsetInCU: 0x8D06, offset: 0x295F7, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController4stopEP9IOService, symObjAddr: 0x7E0, symBinAddr: 0x4610, symSize: 0x20 }
  - { offsetInCU: 0x8D2E, offset: 0x2961F, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController14setReceiveDataEP18IOSMBusTransactiont, symObjAddr: 0x800, symBinAddr: 0x4630, symSize: 0x20 }
  - { offsetInCU: 0x8DC6, offset: 0x296B7, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController12startRequestEP14IOSMBusRequest, symObjAddr: 0x820, symBinAddr: 0x4650, symSize: 0x700 }
  - { offsetInCU: 0x934B, offset: 0x29C3C, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController26handleBatteryCommandsEventEv, symObjAddr: 0xF20, symBinAddr: 0x4D50, symSize: 0xD0 }
  - { offsetInCU: 0x937B, offset: 0x29C6C, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController13setPowerStateEmP9IOService, symObjAddr: 0xFF0, symBinAddr: 0x4E20, symSize: 0x50 }
  - { offsetInCU: 0x93F6, offset: 0x29CE7, size: 0x4, addend: 0x0, symName: __ZN18SMCSMBusController9MetaClassD0Ev, symObjAddr: 0x1040, symBinAddr: 0x4E70, symSize: 0x10 }
  - { offsetInCU: 0x9451, offset: 0x29D42, size: 0x4, addend: 0x0, symName: __ZNK18SMCSMBusController11getWorkLoopEv, symObjAddr: 0x1050, symBinAddr: 0x4E80, symSize: 0x10 }
  - { offsetInCU: 0x94E8, offset: 0x29DD9, size: 0x4, addend: 0x0, symName: '__ZZN18SMCSMBusController5startEP9IOServiceEN3$_08__invokeEP8OSObjectP18IOTimerEventSource', symObjAddr: 0x1060, symBinAddr: 0x4E90, symSize: 0xF0 }
  - { offsetInCU: 0x9590, offset: 0x29E81, size: 0x4, addend: 0x0, symName: __GLOBAL__sub_I_SMCSMBusController.cpp, symObjAddr: 0x1150, symBinAddr: 0x4F80, symSize: 0x40 }
  - { offsetInCU: 0x95E0, offset: 0x29ED1, size: 0x4, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x1190, symBinAddr: 0x4FC0, symSize: 0x17 }
  - { offsetInCU: 0x27, offset: 0x29F45, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManager9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x4FE0, symSize: 0x30 }
  - { offsetInCU: 0x36, offset: 0x29F54, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManager10gMetaClassE, symObjAddr: 0x2F960, symBinAddr: 0x8210, symSize: 0x0 }
  - { offsetInCU: 0x6896, offset: 0x307B4, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManager9metaClassE, symObjAddr: 0x10D8, symBinAddr: 0x7BBC, symSize: 0x0 }
  - { offsetInCU: 0x68A5, offset: 0x307C3, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManager10superClassE, symObjAddr: 0x10DC, symBinAddr: 0x7BC0, symSize: 0x0 }
  - { offsetInCU: 0x68BB, offset: 0x307D9, size: 0x4, addend: 0x0, symName: _SMCBatteryManager_debugEnabled, symObjAddr: 0x2F978, symBinAddr: 0x8228, symSize: 0x0 }
  - { offsetInCU: 0x68CE, offset: 0x307EC, size: 0x4, addend: 0x0, symName: _SMCBatteryManager_debugPrintDelay, symObjAddr: 0x2F97C, symBinAddr: 0x822C, symSize: 0x0 }
  - { offsetInCU: 0x68E1, offset: 0x307FF, size: 0x4, addend: 0x0, symName: _smc_battery_manager_started, symObjAddr: 0x2F980, symBinAddr: 0x8230, symSize: 0x0 }
  - { offsetInCU: 0x692E, offset: 0x3084C, size: 0x4, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0x15FC, symBinAddr: 0x80E0, symSize: 0x0 }
  - { offsetInCU: 0x6969, offset: 0x30887, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManager9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x4FE0, symSize: 0x30 }
  - { offsetInCU: 0x69B9, offset: 0x308D7, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManager9MetaClassD1Ev, symObjAddr: 0x30, symBinAddr: 0x5010, symSize: 0x10 }
  - { offsetInCU: 0x6A51, offset: 0x3096F, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManagerC2EPK11OSMetaClass, symObjAddr: 0x40, symBinAddr: 0x5020, symSize: 0x70 }
  - { offsetInCU: 0x6B23, offset: 0x30A41, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManagerC1EPK11OSMetaClass, symObjAddr: 0xB0, symBinAddr: 0x5090, symSize: 0x70 }
  - { offsetInCU: 0x6C02, offset: 0x30B20, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManagerD2Ev, symObjAddr: 0x120, symBinAddr: 0x5100, symSize: 0x10 }
  - { offsetInCU: 0x6C39, offset: 0x30B57, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManagerD1Ev, symObjAddr: 0x130, symBinAddr: 0x5110, symSize: 0x10 }
  - { offsetInCU: 0x6C89, offset: 0x30BA7, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManagerD0Ev, symObjAddr: 0x140, symBinAddr: 0x5120, symSize: 0x30 }
  - { offsetInCU: 0x6CE3, offset: 0x30C01, size: 0x4, addend: 0x0, symName: __ZNK17SMCBatteryManager12getMetaClassEv, symObjAddr: 0x170, symBinAddr: 0x5150, symSize: 0x10 }
  - { offsetInCU: 0x6D03, offset: 0x30C21, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManager9MetaClassC2Ev, symObjAddr: 0x180, symBinAddr: 0x5160, symSize: 0x30 }
  - { offsetInCU: 0x6D55, offset: 0x30C73, size: 0x4, addend: 0x0, symName: __ZNK17SMCBatteryManager9MetaClass5allocEv, symObjAddr: 0x1B0, symBinAddr: 0x5190, symSize: 0x90 }
  - { offsetInCU: 0x6E2D, offset: 0x30D4B, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManagerC1Ev, symObjAddr: 0x240, symBinAddr: 0x5220, symSize: 0x80 }
  - { offsetInCU: 0x6EEA, offset: 0x30E08, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManagerC2Ev, symObjAddr: 0x2C0, symBinAddr: 0x52A0, symSize: 0x80 }
  - { offsetInCU: 0x6F8E, offset: 0x30EAC, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManager5probeEP9IOServicePl, symObjAddr: 0x340, symBinAddr: 0x5320, symSize: 0x70 }
  - { offsetInCU: 0x7DD6, offset: 0x31CF4, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManager5startEP9IOService, symObjAddr: 0x3B0, symBinAddr: 0x5390, symSize: 0xA70 }
  - { offsetInCU: 0x8D3A, offset: 0x32C58, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeyValue7compareEPKvS1_, symObjAddr: 0xE20, symBinAddr: 0x5E00, symSize: 0x30 }
  - { offsetInCU: 0x8D8A, offset: 0x32CA8, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManager23vsmcNotificationHandlerEPvS0_P9IOServiceP10IONotifier, symObjAddr: 0xE50, symBinAddr: 0x5E30, symSize: 0x80 }
  - { offsetInCU: 0x8DF2, offset: 0x32D10, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManager4stopEP9IOService, symObjAddr: 0xED0, symBinAddr: 0x5EB0, symSize: 0x20 }
  - { offsetInCU: 0x8E92, offset: 0x32DB0, size: 0x4, addend: 0x0, symName: _SMCBatteryManager_kern_start, symObjAddr: 0xEF0, symBinAddr: 0x5ED0, symSize: 0x80 }
  - { offsetInCU: 0x8F95, offset: 0x32EB3, size: 0x4, addend: 0x0, symName: _SMCBatteryManager_kern_stop, symObjAddr: 0xF70, symBinAddr: 0x5F50, symSize: 0x10 }
  - { offsetInCU: 0x8FDE, offset: 0x32EFC, size: 0x4, addend: 0x0, symName: __ZN17SMCBatteryManager9MetaClassD0Ev, symObjAddr: 0xF80, symBinAddr: 0x5F60, symSize: 0x10 }
  - { offsetInCU: 0x9038, offset: 0x32F56, size: 0x4, addend: 0x0, symName: __ZN15VirtualSMCValueC2Ev, symObjAddr: 0xF90, symBinAddr: 0x5F70, symSize: 0x100 }
  - { offsetInCU: 0x9084, offset: 0x32FA2, size: 0x4, addend: 0x0, symName: __GLOBAL__sub_I_SMCBatteryManager.cpp, symObjAddr: 0x1090, symBinAddr: 0x6070, symSize: 0x30 }
  - { offsetInCU: 0x90D4, offset: 0x32FF2, size: 0x4, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x10C0, symBinAddr: 0x60A0, symSize: 0x17 }
...
