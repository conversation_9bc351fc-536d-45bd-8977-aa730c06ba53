---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/work/VirtualSMC/VirtualSMC/build/Release/SMCSuperIO.kext/Contents/MacOS/SMCSuperIO'
relocations:
  - { offsetInCU: 0x35, offset: 0x35, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0xE8F0, symBinAddr: 0x15A48, symSize: 0x0 }
  - { offsetInCU: 0x206, offset: 0x206, size: 0x8, addend: 0x0, symName: __realmain, symObjAddr: 0xE9B8, symBinAddr: 0x15B10, symSize: 0x0 }
  - { offsetInCU: 0x21C, offset: 0x21C, size: 0x8, addend: 0x0, symName: __antimain, symObjAddr: 0xE9C0, symBinAddr: 0x15B18, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x24C, size: 0x8, addend: 0x0, symName: __ZN3ITE9ITEDevice9setupKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0x0, symBinAddr: 0x860, symSize: 0x930 }
  - { offsetInCU: 0x4A, offset: 0x26F, size: 0x8, addend: 0x0, symName: __ZN3ITE9_pwmCurveE, symObjAddr: 0x964D0, symBinAddr: 0x15B50, symSize: 0x0 }
  - { offsetInCU: 0x68, offset: 0x28D, size: 0x8, addend: 0x0, symName: __ZN3ITE16_fanControlIndexE, symObjAddr: 0x970D0, symBinAddr: 0x16750, symSize: 0x0 }
  - { offsetInCU: 0x86, offset: 0x2AB, size: 0x8, addend: 0x0, symName: __ZN3ITE21_initialFanPwmControlE, symObjAddr: 0x970D6, symBinAddr: 0x16756, symSize: 0x0 }
  - { offsetInCU: 0xA4, offset: 0x2C9, size: 0x8, addend: 0x0, symName: __ZN3ITE28_initialFanOutputModeEnabledE, symObjAddr: 0x970DC, symBinAddr: 0x1675C, symSize: 0x0 }
  - { offsetInCU: 0xC2, offset: 0x2E7, size: 0x8, addend: 0x0, symName: __ZN3ITE24_initialFanPwmControlExtE, symObjAddr: 0x970E2, symBinAddr: 0x16762, symSize: 0x0 }
  - { offsetInCU: 0xE0, offset: 0x305, size: 0x8, addend: 0x0, symName: __ZN3ITE36_restoreDefaultFanPwmControlRequiredE, symObjAddr: 0x970E8, symBinAddr: 0x16768, symSize: 0x0 }
  - { offsetInCU: 0xFE, offset: 0x323, size: 0x8, addend: 0x0, symName: __ZN3ITE10_hasExtRegE, symObjAddr: 0x970EE, symBinAddr: 0x1676E, symSize: 0x0 }
  - { offsetInCU: 0x1C1, offset: 0x3E6, size: 0x8, addend: 0x0, symName: __ZN3ITEL22ITE_FAN_TACHOMETER_REGE, symObjAddr: 0xE9D0, symBinAddr: 0xF0E0, symSize: 0x0 }
  - { offsetInCU: 0x1DE, offset: 0x403, size: 0x8, addend: 0x0, symName: __ZN3ITEL26ITE_FAN_TACHOMETER_EXT_REGE, symObjAddr: 0xE9D6, symBinAddr: 0xF0E6, symSize: 0x0 }
  - { offsetInCU: 0x1FB, offset: 0x420, size: 0x8, addend: 0x0, symName: __ZN3ITEL16FAN_PWM_CTRL_REGE, symObjAddr: 0xE9C8, symBinAddr: 0x15B20, symSize: 0x0 }
  - { offsetInCU: 0x218, offset: 0x43D, size: 0x8, addend: 0x0, symName: __ZN3ITEL20FAN_PWM_CTRL_EXT_REGE, symObjAddr: 0xE9DC, symBinAddr: 0xF0EC, symSize: 0x0 }
  - { offsetInCU: 0x235, offset: 0x45A, size: 0x8, addend: 0x0, symName: __ZN3ITEL25ITE_EC_FAN_TACHOMETER_REGE, symObjAddr: 0xE9E2, symBinAddr: 0xF0F2, symSize: 0x0 }
  - { offsetInCU: 0x252, offset: 0x477, size: 0x8, addend: 0x0, symName: __ZN3ITEL29ITE_EC_FAN_TACHOMETER_EXT_REGE, symObjAddr: 0xE9E6, symBinAddr: 0xF0F6, symSize: 0x0 }
  - { offsetInCU: 0x26F, offset: 0x494, size: 0x8, addend: 0x0, symName: __ZN6FintekL18FINTEK_VOLTAGE_REGE, symObjAddr: 0xE9F4, symBinAddr: 0xF104, symSize: 0x0 }
  - { offsetInCU: 0x28C, offset: 0x4B1, size: 0x8, addend: 0x0, symName: __ZN3ITEL20FAN_PWM_CTRL_REG_ALTE, symObjAddr: 0xE9EA, symBinAddr: 0xF0FA, symSize: 0x0 }
  - { offsetInCU: 0x11170, offset: 0x11395, size: 0x8, addend: 0x0, symName: __ZN3ITE9ITEDevice9setupKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0x0, symBinAddr: 0x860, symSize: 0x930 }
  - { offsetInCU: 0x11D90, offset: 0x11FB5, size: 0x8, addend: 0x0, symName: __ZN3ITE9ITEDevice13updateTargetsEv, symObjAddr: 0x930, symBinAddr: 0x1190, symSize: 0x420 }
  - { offsetInCU: 0x1284F, offset: 0x12A74, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValueC2Ev, symObjAddr: 0xD50, symBinAddr: 0x15B0, symSize: 0xB0 }
  - { offsetInCU: 0x12997, offset: 0x12BBC, size: 0x8, addend: 0x0, symName: __ZN3ITE9ITEDevice9probePortEtP10SMCSuperIO, symObjAddr: 0xE00, symBinAddr: 0x1660, symSize: 0x1280 }
  - { offsetInCU: 0x27, offset: 0x14DB1, size: 0x8, addend: 0x0, symName: __ZN6Fintek12FintekDevice9setupKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0x2080, symBinAddr: 0x28E0, symSize: 0x1E0 }
  - { offsetInCU: 0x69, offset: 0x14DF3, size: 0x8, addend: 0x0, symName: __ZN6FintekL25FINTEK_FAN_TACHOMETER_REGE, symObjAddr: 0xE9F0, symBinAddr: 0xF100, symSize: 0x0 }
  - { offsetInCU: 0x8B, offset: 0x14E15, size: 0x8, addend: 0x0, symName: __ZN6FintekL18FINTEK_VOLTAGE_REGE, symObjAddr: 0xE9F4, symBinAddr: 0xF104, symSize: 0x0 }
  - { offsetInCU: 0x482, offset: 0x1520C, size: 0x8, addend: 0x0, symName: __ZN6Fintek12FintekDevice9setupKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0x2080, symBinAddr: 0x28E0, symSize: 0x1E0 }
  - { offsetInCU: 0x27, offset: 0x155B0, size: 0x8, addend: 0x0, symName: __ZN2EC11ECDeviceNUC21readBigWordMMIOCachedEj, symObjAddr: 0x2260, symBinAddr: 0x2AC0, symSize: 0x2B0 }
  - { offsetInCU: 0x707, offset: 0x15C90, size: 0x8, addend: 0x0, symName: __ZN2EC11ECDeviceNUC21readBigWordMMIOCachedEj, symObjAddr: 0x2260, symBinAddr: 0x2AC0, symSize: 0x2B0 }
  - { offsetInCU: 0x27, offset: 0x15EB7, size: 0x8, addend: 0x0, symName: __ZN7Winbond13WinbondDevice17updateTachometersEv, symObjAddr: 0x2510, symBinAddr: 0x2D70, symSize: 0x380 }
  - { offsetInCU: 0x8A, offset: 0x15F1A, size: 0x8, addend: 0x0, symName: __ZN7WinbondL27WINBOND_TACHOMETER_DIVISOR2E, symObjAddr: 0xE9FD, symBinAddr: 0xF10D, symSize: 0x0 }
  - { offsetInCU: 0xA7, offset: 0x15F37, size: 0x8, addend: 0x0, symName: __ZN7WinbondL27WINBOND_TACHOMETER_DIVISOR1E, symObjAddr: 0xEA02, symBinAddr: 0xF112, symSize: 0x0 }
  - { offsetInCU: 0xC4, offset: 0x15F54, size: 0x8, addend: 0x0, symName: __ZN7WinbondL27WINBOND_TACHOMETER_DIVISOR0E, symObjAddr: 0xEA07, symBinAddr: 0xF117, symSize: 0x0 }
  - { offsetInCU: 0xE1, offset: 0x15F71, size: 0x8, addend: 0x0, symName: __ZN7WinbondL18WINBOND_TACHOMETERE, symObjAddr: 0xEA0C, symBinAddr: 0xF11C, symSize: 0x0 }
  - { offsetInCU: 0xFE, offset: 0x15F8E, size: 0x8, addend: 0x0, symName: __ZN7NuvotonL25NUVOTON_VOLTAGE_6775_REGSE, symObjAddr: 0xEA90, symBinAddr: 0xF1A0, symSize: 0x0 }
  - { offsetInCU: 0x11B, offset: 0x15FAB, size: 0x8, addend: 0x0, symName: __ZN7WinbondL20WINBOND_VOLTAGE_REG1E, symObjAddr: 0xEA20, symBinAddr: 0xF130, symSize: 0x0 }
  - { offsetInCU: 0x48B, offset: 0x1631B, size: 0x8, addend: 0x0, symName: __ZN7Winbond13WinbondDevice17updateTachometersEv, symObjAddr: 0x2510, symBinAddr: 0x2D70, symSize: 0x380 }
  - { offsetInCU: 0xB9C, offset: 0x16A2C, size: 0x8, addend: 0x0, symName: __ZN7Winbond13WinbondDevice9setupKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0x2890, symBinAddr: 0x30F0, symSize: 0x1E0 }
  - { offsetInCU: 0xDB1, offset: 0x16C41, size: 0x8, addend: 0x0, symName: __ZN7Winbond13WinbondDevice11voltageReadEh, symObjAddr: 0x2A70, symBinAddr: 0x32D0, symSize: 0xA0 }
  - { offsetInCU: 0x27, offset: 0x171C7, size: 0x8, addend: 0x0, symName: __ZN2EC15ECDeviceGeneric18getTachometerCountEv, symObjAddr: 0x2B10, symBinAddr: 0x3370, symSize: 0x10 }
  - { offsetInCU: 0x35D, offset: 0x174FD, size: 0x8, addend: 0x0, symName: __ZN2EC15ECDeviceGeneric18getTachometerCountEv, symObjAddr: 0x2B10, symBinAddr: 0x3370, symSize: 0x10 }
  - { offsetInCU: 0x391, offset: 0x17531, size: 0x8, addend: 0x0, symName: __ZN2EC15ECDeviceGeneric17getTachometerNameEh, symObjAddr: 0x2B20, symBinAddr: 0x3380, symSize: 0x30 }
  - { offsetInCU: 0x4D6, offset: 0x17676, size: 0x8, addend: 0x0, symName: __ZN2EC15ECDeviceGeneric16updateTachometerEh, symObjAddr: 0x2B50, symBinAddr: 0x33B0, symSize: 0x140 }
  - { offsetInCU: 0x631, offset: 0x177D1, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice18setTachometerValueEht.71, symObjAddr: 0x2C90, symBinAddr: 0x34F0, symSize: 0x40 }
  - { offsetInCU: 0x68C, offset: 0x1782C, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice17updateTachometersEv.72, symObjAddr: 0x2CD0, symBinAddr: 0x3530, symSize: 0x60 }
  - { offsetInCU: 0x70B, offset: 0x178AB, size: 0x8, addend: 0x0, symName: __ZN2EC15ECDeviceGeneric15getVoltageCountEv, symObjAddr: 0x2D30, symBinAddr: 0x3590, symSize: 0x10 }
  - { offsetInCU: 0x73C, offset: 0x178DC, size: 0x8, addend: 0x0, symName: __ZN2EC15ECDeviceGeneric14getVoltageNameEh, symObjAddr: 0x2D40, symBinAddr: 0x35A0, symSize: 0x10 }
  - { offsetInCU: 0x77C, offset: 0x1791C, size: 0x8, addend: 0x0, symName: __ZN2EC15ECDeviceGeneric13updateVoltageEh, symObjAddr: 0x2D50, symBinAddr: 0x35B0, symSize: 0x10 }
  - { offsetInCU: 0x7BC, offset: 0x1795C, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice15setVoltageValueEhf.73, symObjAddr: 0x2D60, symBinAddr: 0x35C0, symSize: 0x40 }
  - { offsetInCU: 0x817, offset: 0x179B7, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice14updateVoltagesEv.74, symObjAddr: 0x2DA0, symBinAddr: 0x3600, symSize: 0x60 }
  - { offsetInCU: 0x896, offset: 0x17A36, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice19getTemperatureCountEv.75, symObjAddr: 0x2E00, symBinAddr: 0x3660, symSize: 0x10 }
  - { offsetInCU: 0x8C5, offset: 0x17A65, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice18getTemperatureNameEh.76, symObjAddr: 0x2E10, symBinAddr: 0x3670, symSize: 0x10 }
  - { offsetInCU: 0x903, offset: 0x17AA3, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice17updateTemperatureEh.77, symObjAddr: 0x2E20, symBinAddr: 0x3680, symSize: 0x10 }
  - { offsetInCU: 0x941, offset: 0x17AE1, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice19setTemperatureValueEhf.78, symObjAddr: 0x2E30, symBinAddr: 0x3690, symSize: 0x50 }
  - { offsetInCU: 0x99C, offset: 0x17B3C, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice18updateTemperaturesEv.79, symObjAddr: 0x2E80, symBinAddr: 0x36E0, symSize: 0x60 }
  - { offsetInCU: 0xA1B, offset: 0x17BBB, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice9onPowerOnEv.80, symObjAddr: 0x2EE0, symBinAddr: 0x3740, symSize: 0x10 }
  - { offsetInCU: 0xA4A, offset: 0x17BEA, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice10initializeEttP10SMCSuperIO.81, symObjAddr: 0x2EF0, symBinAddr: 0x3750, symSize: 0x20 }
  - { offsetInCU: 0xAB2, offset: 0x17C52, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice13updateTargetsEv.82, symObjAddr: 0x2F10, symBinAddr: 0x3770, symSize: 0x10 }
  - { offsetInCU: 0xAE1, offset: 0x17C81, size: 0x8, addend: 0x0, symName: __ZN2EC15ECDeviceGeneric12getModelNameEv, symObjAddr: 0x2F20, symBinAddr: 0x3780, symSize: 0x10 }
  - { offsetInCU: 0xB15, offset: 0x17CB5, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice6getLdnEv.83, symObjAddr: 0x2F30, symBinAddr: 0x3790, symSize: 0x10 }
  - { offsetInCU: 0xB44, offset: 0x17CE4, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice14setTargetValueEht.84, symObjAddr: 0x2F40, symBinAddr: 0x37A0, symSize: 0x40 }
  - { offsetInCU: 0xB9F, offset: 0x17D3F, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice14setManualValueEhh.85, symObjAddr: 0x2F80, symBinAddr: 0x37E0, symSize: 0x40 }
  - { offsetInCU: 0xBFA, offset: 0x17D9A, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice11setMaxValueEht.86, symObjAddr: 0x2FC0, symBinAddr: 0x3820, symSize: 0x40 }
  - { offsetInCU: 0xC55, offset: 0x17DF5, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice11setMinValueEht.87, symObjAddr: 0x3000, symBinAddr: 0x3860, symSize: 0x40 }
  - { offsetInCU: 0xCB0, offset: 0x17E50, size: 0x8, addend: 0x0, symName: __ZN2EC15ECDeviceGenericD1Ev, symObjAddr: 0x3040, symBinAddr: 0x38A0, symSize: 0x10 }
  - { offsetInCU: 0xCE5, offset: 0x17E85, size: 0x8, addend: 0x0, symName: __ZN2EC15ECDeviceGenericD0Ev, symObjAddr: 0x3050, symBinAddr: 0x38B0, symSize: 0x10 }
  - { offsetInCU: 0xD1D, offset: 0x17EBD, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice16setupVoltageKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0x3060, symBinAddr: 0x38C0, symSize: 0x10 }
  - { offsetInCU: 0xD5B, offset: 0x17EFB, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice20setupTemperatureKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0x3070, symBinAddr: 0x38D0, symSize: 0x10 }
  - { offsetInCU: 0xD99, offset: 0x17F39, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice14tachometerReadEh, symObjAddr: 0x3080, symBinAddr: 0x38E0, symSize: 0x10 }
  - { offsetInCU: 0xDD7, offset: 0x17F77, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice11voltageReadEh, symObjAddr: 0x3090, symBinAddr: 0x38F0, symSize: 0x10 }
  - { offsetInCU: 0xE15, offset: 0x17FB5, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice15temperatureReadEh, symObjAddr: 0x30A0, symBinAddr: 0x3900, symSize: 0x10 }
  - { offsetInCU: 0xE53, offset: 0x17FF3, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice14setupExtraKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0x30B0, symBinAddr: 0x3910, symSize: 0x30 }
  - { offsetInCU: 0x27, offset: 0x1823D, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice18setTachometerValueEht.103, symObjAddr: 0x30E0, symBinAddr: 0x3940, symSize: 0x40 }
  - { offsetInCU: 0x15D, offset: 0x18373, size: 0x8, addend: 0x0, symName: __ZN7NuvotonL16NUVOTON_FAN_REGSE, symObjAddr: 0xEA34, symBinAddr: 0xF144, symSize: 0x0 }
  - { offsetInCU: 0x17F, offset: 0x18395, size: 0x8, addend: 0x0, symName: __ZN7NuvotonL21NUVOTON_FAN_6776_REGSE, symObjAddr: 0xEA42, symBinAddr: 0xF152, symSize: 0x0 }
  - { offsetInCU: 0x197, offset: 0x183AD, size: 0x8, addend: 0x0, symName: __ZN7NuvotonL21NUVOTON_6683_FAN_REGSE, symObjAddr: 0xEA50, symBinAddr: 0xF160, symSize: 0x0 }
  - { offsetInCU: 0x1B4, offset: 0x183CA, size: 0x8, addend: 0x0, symName: __ZN7NuvotonL20NUVOTON_VOLTAGE_REGSE, symObjAddr: 0xEA70, symBinAddr: 0xF180, symSize: 0x0 }
  - { offsetInCU: 0x1D6, offset: 0x183EC, size: 0x8, addend: 0x0, symName: __ZN7NuvotonL25NUVOTON_VOLTAGE_6775_REGSE, symObjAddr: 0xEA90, symBinAddr: 0xF1A0, symSize: 0x0 }
  - { offsetInCU: 0x4B1, offset: 0x186C7, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice18setTachometerValueEht.103, symObjAddr: 0x30E0, symBinAddr: 0x3940, symSize: 0x40 }
  - { offsetInCU: 0x50C, offset: 0x18722, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice17updateTachometersEv.104, symObjAddr: 0x3120, symBinAddr: 0x3980, symSize: 0x60 }
  - { offsetInCU: 0x58B, offset: 0x187A1, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice15setVoltageValueEhf.105, symObjAddr: 0x3180, symBinAddr: 0x39E0, symSize: 0x40 }
  - { offsetInCU: 0x5E6, offset: 0x187FC, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice14updateVoltagesEv.106, symObjAddr: 0x31C0, symBinAddr: 0x3A20, symSize: 0x60 }
  - { offsetInCU: 0x665, offset: 0x1887B, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice19getTemperatureCountEv.107, symObjAddr: 0x3220, symBinAddr: 0x3A80, symSize: 0x10 }
  - { offsetInCU: 0x694, offset: 0x188AA, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice18getTemperatureNameEh.108, symObjAddr: 0x3230, symBinAddr: 0x3A90, symSize: 0x10 }
  - { offsetInCU: 0x6D2, offset: 0x188E8, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice17updateTemperatureEh.109, symObjAddr: 0x3240, symBinAddr: 0x3AA0, symSize: 0x10 }
  - { offsetInCU: 0x710, offset: 0x18926, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice19setTemperatureValueEhf.110, symObjAddr: 0x3250, symBinAddr: 0x3AB0, symSize: 0x50 }
  - { offsetInCU: 0x76B, offset: 0x18981, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice18updateTemperaturesEv.111, symObjAddr: 0x32A0, symBinAddr: 0x3B00, symSize: 0x60 }
  - { offsetInCU: 0x7EA, offset: 0x18A00, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice9onPowerOnEv.112, symObjAddr: 0x3300, symBinAddr: 0x3B60, symSize: 0x10 }
  - { offsetInCU: 0x819, offset: 0x18A2F, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice10initializeEttP10SMCSuperIO.113, symObjAddr: 0x3310, symBinAddr: 0x3B70, symSize: 0x20 }
  - { offsetInCU: 0xA4D, offset: 0x18C63, size: 0x8, addend: 0x0, symName: __ZN7Nuvoton13NuvotonDevice9setupKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0x3330, symBinAddr: 0x3B90, symSize: 0x1E0 }
  - { offsetInCU: 0xC62, offset: 0x18E78, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice13updateTargetsEv.114, symObjAddr: 0x3510, symBinAddr: 0x3D70, symSize: 0x10 }
  - { offsetInCU: 0xC91, offset: 0x18EA7, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice6getLdnEv.115, symObjAddr: 0x3520, symBinAddr: 0x3D80, symSize: 0x10 }
  - { offsetInCU: 0xCC0, offset: 0x18ED6, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice14setTargetValueEht.116, symObjAddr: 0x3530, symBinAddr: 0x3D90, symSize: 0x40 }
  - { offsetInCU: 0xD1B, offset: 0x18F31, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice14setManualValueEhh.117, symObjAddr: 0x3570, symBinAddr: 0x3DD0, symSize: 0x40 }
  - { offsetInCU: 0xD76, offset: 0x18F8C, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice11setMaxValueEht.118, symObjAddr: 0x35B0, symBinAddr: 0x3E10, symSize: 0x40 }
  - { offsetInCU: 0xDD1, offset: 0x18FE7, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice11setMinValueEht.119, symObjAddr: 0x35F0, symBinAddr: 0x3E50, symSize: 0x40 }
  - { offsetInCU: 0xE2C, offset: 0x19042, size: 0x8, addend: 0x0, symName: __ZN7Nuvoton13NuvotonDeviceD1Ev, symObjAddr: 0x3630, symBinAddr: 0x3E90, symSize: 0x10 }
  - { offsetInCU: 0xE61, offset: 0x19077, size: 0x8, addend: 0x0, symName: __ZN7Nuvoton13NuvotonDeviceD0Ev, symObjAddr: 0x3640, symBinAddr: 0x3EA0, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x1950F, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice9setupKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0x3650, symBinAddr: 0x3EB0, symSize: 0x200 }
  - { offsetInCU: 0x76A, offset: 0x19C52, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice9setupKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0x3650, symBinAddr: 0x3EB0, symSize: 0x200 }
  - { offsetInCU: 0x9A8, offset: 0x19E90, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCKeyValue7compareEPKvS1_, symObjAddr: 0x3850, symBinAddr: 0x40B0, symSize: 0x30 }
  - { offsetInCU: 0xB6B, offset: 0x1A053, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice12readBytePMIOEh, symObjAddr: 0x3880, symBinAddr: 0x40E0, symSize: 0x120 }
  - { offsetInCU: 0x27, offset: 0x1A4DF, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIOD1Ev, symObjAddr: 0x39A0, symBinAddr: 0x4200, symSize: 0x10 }
  - { offsetInCU: 0x43, offset: 0x1A4FB, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO10gMetaClassE, symObjAddr: 0x970F8, symBinAddr: 0x15B28, symSize: 0x0 }
  - { offsetInCU: 0x5B, offset: 0x1A513, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO9metaClassE, symObjAddr: 0xFB10, symBinAddr: 0x10278, symSize: 0x0 }
  - { offsetInCU: 0x73, offset: 0x1A52B, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO10superClassE, symObjAddr: 0xFB18, symBinAddr: 0x10280, symSize: 0x0 }
  - { offsetInCU: 0xA8, offset: 0x1A560, size: 0x8, addend: 0x0, symName: _SMCSuperIO_debugPrintDelay, symObjAddr: 0x970F0, symBinAddr: 0x16770, symSize: 0x0 }
  - { offsetInCU: 0xC4, offset: 0x1A57C, size: 0x8, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0xEB70, symBinAddr: 0xF280, symSize: 0x0 }
  - { offsetInCU: 0x10E, offset: 0x1A5C6, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIOD1Ev, symObjAddr: 0x39A0, symBinAddr: 0x4200, symSize: 0x10 }
  - { offsetInCU: 0x186, offset: 0x1A63E, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIOD0Ev, symObjAddr: 0x39B0, symBinAddr: 0x4210, symSize: 0x30 }
  - { offsetInCU: 0x20F, offset: 0x1A6C7, size: 0x8, addend: 0x0, symName: __ZNK10SMCSuperIO12getMetaClassEv, symObjAddr: 0x39E0, symBinAddr: 0x4240, symSize: 0x10 }
  - { offsetInCU: 0x240, offset: 0x1A6F8, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO5probeEP9IOServicePi, symObjAddr: 0x39F0, symBinAddr: 0x4250, symSize: 0x20 }
  - { offsetInCU: 0x298, offset: 0x1A750, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO5startEP9IOService, symObjAddr: 0x3A10, symBinAddr: 0x4270, symSize: 0x280 }
  - { offsetInCU: 0x38A, offset: 0x1A842, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO4stopEP9IOService, symObjAddr: 0x3C90, symBinAddr: 0x44F0, symSize: 0x10 }
  - { offsetInCU: 0x3FA, offset: 0x1A8B2, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO13setPowerStateEmP9IOService, symObjAddr: 0x3CA0, symBinAddr: 0x4500, symSize: 0x20 }
  - { offsetInCU: 0x402, offset: 0x1A8BA, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO12detectDeviceEv, symObjAddr: 0x3CC0, symBinAddr: 0x4520, symSize: 0x1170 }
  - { offsetInCU: 0x5ED, offset: 0x1AAA5, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO12detectDeviceEv, symObjAddr: 0x3CC0, symBinAddr: 0x4520, symSize: 0x1170 }
  - { offsetInCU: 0x1EBD, offset: 0x1C375, size: 0x8, addend: 0x0, symName: '__ZZN10SMCSuperIO5startEP9IOServiceEN3$_08__invokeEP8OSObjectP18IOTimerEventSource', symObjAddr: 0x4E30, symBinAddr: 0x5690, symSize: 0x50 }
  - { offsetInCU: 0x1F61, offset: 0x1C419, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO23vsmcNotificationHandlerEPvS0_P9IOServiceP10IONotifier, symObjAddr: 0x4E80, symBinAddr: 0x56E0, symSize: 0xA0 }
  - { offsetInCU: 0x202D, offset: 0x1C4E5, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO9MetaClassD1Ev, symObjAddr: 0x4F20, symBinAddr: 0x5780, symSize: 0x10 }
  - { offsetInCU: 0x20A5, offset: 0x1C55D, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO9MetaClassD0Ev, symObjAddr: 0x4F30, symBinAddr: 0x5790, symSize: 0x10 }
  - { offsetInCU: 0x2151, offset: 0x1C609, size: 0x8, addend: 0x0, symName: __ZNK10SMCSuperIO9MetaClass5allocEv, symObjAddr: 0x4F40, symBinAddr: 0x57A0, symSize: 0x30 }
  - { offsetInCU: 0x2227, offset: 0x1C6DF, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIOC2Ev, symObjAddr: 0x4F70, symBinAddr: 0x57D0, symSize: 0x150 }
  - { offsetInCU: 0x2325, offset: 0x1C7DD, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_SMCSuperIO.cpp, symObjAddr: 0x50C0, symBinAddr: 0x5920, symSize: 0x40 }
  - { offsetInCU: 0x23B3, offset: 0x1C86B, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x5100, symBinAddr: 0x5960, symSize: 0x20 }
  - { offsetInCU: 0x2424, offset: 0x1C8DC, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO9MetaClassC1Ev, symObjAddr: 0x5120, symBinAddr: 0x5980, symSize: 0x40 }
  - { offsetInCU: 0x247E, offset: 0x1C936, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIOC2EPK11OSMetaClass, symObjAddr: 0x5160, symBinAddr: 0x59C0, symSize: 0x140 }
  - { offsetInCU: 0x2562, offset: 0x1CA1A, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIOC1EPK11OSMetaClass, symObjAddr: 0x52A0, symBinAddr: 0x5B00, symSize: 0x140 }
  - { offsetInCU: 0x265C, offset: 0x1CB14, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIOD2Ev, symObjAddr: 0x53E0, symBinAddr: 0x5C40, symSize: 0x10 }
  - { offsetInCU: 0x2688, offset: 0x1CB40, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO9MetaClassC2Ev, symObjAddr: 0x53F0, symBinAddr: 0x5C50, symSize: 0x40 }
  - { offsetInCU: 0x26B7, offset: 0x1CB6F, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIOC1Ev, symObjAddr: 0x5430, symBinAddr: 0x5C90, symSize: 0x10 }
  - { offsetInCU: 0x26E3, offset: 0x1CB9B, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO13timerCallbackEv, symObjAddr: 0x5440, symBinAddr: 0x5CA0, symSize: 0x40 }
  - { offsetInCU: 0x2712, offset: 0x1CBCA, size: 0x8, addend: 0x0, symName: __ZN10SMCSuperIO15quickRescheduleEv, symObjAddr: 0x5480, symBinAddr: 0x5CE0, symSize: 0x30 }
  - { offsetInCU: 0x278C, offset: 0x1CC44, size: 0x8, addend: 0x0, symName: _SMCSuperIO_kern_start, symObjAddr: 0x54B0, symBinAddr: 0x5D10, symSize: 0x160 }
  - { offsetInCU: 0x2874, offset: 0x1CD2C, size: 0x8, addend: 0x0, symName: _SMCSuperIO_kern_stop, symObjAddr: 0x5610, symBinAddr: 0x5E70, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x1CF5D, size: 0x8, addend: 0x0, symName: __ZN13TachometerKey10readAccessEv, symObjAddr: 0x5620, symBinAddr: 0x5E80, symSize: 0x80 }
  - { offsetInCU: 0x150, offset: 0x1D086, size: 0x8, addend: 0x0, symName: __ZN13TachometerKey10readAccessEv, symObjAddr: 0x5620, symBinAddr: 0x5E80, symSize: 0x80 }
  - { offsetInCU: 0x1EC, offset: 0x1D122, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValue11writeAccessEv, symObjAddr: 0x56A0, symBinAddr: 0x5F00, symSize: 0x10 }
  - { offsetInCU: 0x21B, offset: 0x1D151, size: 0x8, addend: 0x0, symName: __ZN13TachometerKeyD1Ev, symObjAddr: 0x56B0, symBinAddr: 0x5F10, symSize: 0x10 }
  - { offsetInCU: 0x251, offset: 0x1D187, size: 0x8, addend: 0x0, symName: __ZN13TachometerKeyD0Ev, symObjAddr: 0x56C0, symBinAddr: 0x5F20, symSize: 0x10 }
  - { offsetInCU: 0x3A2, offset: 0x1D2D8, size: 0x8, addend: 0x0, symName: __ZN6MinKey10readAccessEv, symObjAddr: 0x56D0, symBinAddr: 0x5F30, symSize: 0x80 }
  - { offsetInCU: 0x43E, offset: 0x1D374, size: 0x8, addend: 0x0, symName: __ZN6MinKeyD1Ev, symObjAddr: 0x5750, symBinAddr: 0x5FB0, symSize: 0x10 }
  - { offsetInCU: 0x474, offset: 0x1D3AA, size: 0x8, addend: 0x0, symName: __ZN6MinKeyD0Ev, symObjAddr: 0x5760, symBinAddr: 0x5FC0, symSize: 0x10 }
  - { offsetInCU: 0x5C5, offset: 0x1D4FB, size: 0x8, addend: 0x0, symName: __ZN6MaxKey10readAccessEv, symObjAddr: 0x5770, symBinAddr: 0x5FD0, symSize: 0x80 }
  - { offsetInCU: 0x661, offset: 0x1D597, size: 0x8, addend: 0x0, symName: __ZN6MaxKeyD1Ev, symObjAddr: 0x57F0, symBinAddr: 0x6050, symSize: 0x10 }
  - { offsetInCU: 0x697, offset: 0x1D5CD, size: 0x8, addend: 0x0, symName: __ZN6MaxKeyD0Ev, symObjAddr: 0x5800, symBinAddr: 0x6060, symSize: 0x10 }
  - { offsetInCU: 0x820, offset: 0x1D756, size: 0x8, addend: 0x0, symName: __ZN9ManualKey10readAccessEv, symObjAddr: 0x5810, symBinAddr: 0x6070, symSize: 0x80 }
  - { offsetInCU: 0x8BC, offset: 0x1D7F2, size: 0x8, addend: 0x0, symName: __ZN9ManualKey6updateEPKh, symObjAddr: 0x5890, symBinAddr: 0x60F0, symSize: 0x50 }
  - { offsetInCU: 0x919, offset: 0x1D84F, size: 0x8, addend: 0x0, symName: __ZN9ManualKeyD1Ev, symObjAddr: 0x58E0, symBinAddr: 0x6140, symSize: 0x10 }
  - { offsetInCU: 0x94F, offset: 0x1D885, size: 0x8, addend: 0x0, symName: __ZN9ManualKeyD0Ev, symObjAddr: 0x58F0, symBinAddr: 0x6150, symSize: 0x10 }
  - { offsetInCU: 0xAD8, offset: 0x1DA0E, size: 0x8, addend: 0x0, symName: __ZN9TargetKey10readAccessEv, symObjAddr: 0x5900, symBinAddr: 0x6160, symSize: 0x80 }
  - { offsetInCU: 0xB74, offset: 0x1DAAA, size: 0x8, addend: 0x0, symName: __ZN9TargetKey6updateEPKh, symObjAddr: 0x5980, symBinAddr: 0x61E0, symSize: 0x60 }
  - { offsetInCU: 0xBD1, offset: 0x1DB07, size: 0x8, addend: 0x0, symName: __ZN9TargetKeyD1Ev, symObjAddr: 0x59E0, symBinAddr: 0x6240, symSize: 0x10 }
  - { offsetInCU: 0xC07, offset: 0x1DB3D, size: 0x8, addend: 0x0, symName: __ZN9TargetKeyD0Ev, symObjAddr: 0x59F0, symBinAddr: 0x6250, symSize: 0x10 }
  - { offsetInCU: 0xD58, offset: 0x1DC8E, size: 0x8, addend: 0x0, symName: __ZN10VoltageKey10readAccessEv, symObjAddr: 0x5A00, symBinAddr: 0x6260, symSize: 0x80 }
  - { offsetInCU: 0xDF4, offset: 0x1DD2A, size: 0x8, addend: 0x0, symName: __ZN10VoltageKeyD1Ev, symObjAddr: 0x5A80, symBinAddr: 0x62E0, symSize: 0x10 }
  - { offsetInCU: 0xE2A, offset: 0x1DD60, size: 0x8, addend: 0x0, symName: __ZN10VoltageKeyD0Ev, symObjAddr: 0x5A90, symBinAddr: 0x62F0, symSize: 0x10 }
  - { offsetInCU: 0xF7B, offset: 0x1DEB1, size: 0x8, addend: 0x0, symName: __ZN14TemperatureKey10readAccessEv, symObjAddr: 0x5AA0, symBinAddr: 0x6300, symSize: 0x90 }
  - { offsetInCU: 0x1017, offset: 0x1DF4D, size: 0x8, addend: 0x0, symName: __ZN14TemperatureKeyD1Ev, symObjAddr: 0x5B30, symBinAddr: 0x6390, symSize: 0x10 }
  - { offsetInCU: 0x104D, offset: 0x1DF83, size: 0x8, addend: 0x0, symName: __ZN14TemperatureKeyD0Ev, symObjAddr: 0x5B40, symBinAddr: 0x63A0, symSize: 0x10 }
  - { offsetInCU: 0x17C7, offset: 0x1E6FD, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice6updateEv, symObjAddr: 0x5B50, symBinAddr: 0x63B0, symSize: 0x290 }
  - { offsetInCU: 0x27, offset: 0x1E985, size: 0x8, addend: 0x0, symName: __Z12createDevicet, symObjAddr: 0x5DE0, symBinAddr: 0x6640, symSize: 0xDD0 }
  - { offsetInCU: 0x796B, offset: 0x262C9, size: 0x8, addend: 0x0, symName: __Z12createDevicet, symObjAddr: 0x5DE0, symBinAddr: 0x6640, symSize: 0xDD0 }
  - { offsetInCU: 0x7973, offset: 0x262D1, size: 0x8, addend: 0x0, symName: __ZN7Nuvoton13NuvotonDeviceC2Ev, symObjAddr: 0x6BB0, symBinAddr: 0x7410, symSize: 0x130 }
  - { offsetInCU: 0x918A, offset: 0x27AE8, size: 0x8, addend: 0x0, symName: __ZN7Nuvoton13NuvotonDeviceC2Ev, symObjAddr: 0x6BB0, symBinAddr: 0x7410, symSize: 0x130 }
  - { offsetInCU: 0x9552, offset: 0x27EB0, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42312createDeviceEt, symObjAddr: 0x6CE0, symBinAddr: 0x7540, symSize: 0xD0 }
  - { offsetInCU: 0x9732, offset: 0x28090, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD45112createDeviceEt, symObjAddr: 0x6DB0, symBinAddr: 0x7610, symSize: 0xD0 }
  - { offsetInCU: 0x9912, offset: 0x28270, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42812createDeviceEt, symObjAddr: 0x6E80, symBinAddr: 0x76E0, symSize: 0xD0 }
  - { offsetInCU: 0x9AF2, offset: 0x28450, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD80212createDeviceEt, symObjAddr: 0x6F50, symBinAddr: 0x77B0, symSize: 0xD0 }
  - { offsetInCU: 0x9CD2, offset: 0x28630, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42A12createDeviceEt, symObjAddr: 0x7020, symBinAddr: 0x7880, symSize: 0xD0 }
  - { offsetInCU: 0x9EB2, offset: 0x28810, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42B12createDeviceEt, symObjAddr: 0x70F0, symBinAddr: 0x7950, symSize: 0xD0 }
  - { offsetInCU: 0xA2A0, offset: 0x28BFE, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC73012createDeviceEt, symObjAddr: 0x71C0, symBinAddr: 0x7A20, symSize: 0xB0 }
  - { offsetInCU: 0xA480, offset: 0x28DDE, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD44012createDeviceEt, symObjAddr: 0x7270, symBinAddr: 0x7AD0, symSize: 0xB0 }
  - { offsetInCU: 0xA660, offset: 0x28FBE, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD59012createDeviceEt, symObjAddr: 0x7320, symBinAddr: 0x7B80, symSize: 0xB0 }
  - { offsetInCU: 0xAA14, offset: 0x29372, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC33012createDeviceEt, symObjAddr: 0x73D0, symBinAddr: 0x7C30, symSize: 0xB0 }
  - { offsetInCU: 0xADEE, offset: 0x2974C, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC80312createDeviceEt, symObjAddr: 0x7480, symBinAddr: 0x7CE0, symSize: 0xC0 }
  - { offsetInCU: 0xAFCE, offset: 0x2992C, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC91112createDeviceEt, symObjAddr: 0x7540, symBinAddr: 0x7DA0, symSize: 0xC0 }
  - { offsetInCU: 0xB1AE, offset: 0x29B0C, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD12112createDeviceEt, symObjAddr: 0x7600, symBinAddr: 0x7E60, symSize: 0xC0 }
  - { offsetInCU: 0xB38E, offset: 0x29CEC, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD35212createDeviceEt, symObjAddr: 0x76C0, symBinAddr: 0x7F20, symSize: 0xC0 }
  - { offsetInCU: 0xB43A, offset: 0x29D98, size: 0x8, addend: 0x0, symName: __ZN25GeneratedWinbondDevice_2718getTachometerCountEv, symObjAddr: 0x7780, symBinAddr: 0x7FE0, symSize: 0x10 }
  - { offsetInCU: 0xB469, offset: 0x29DC7, size: 0x8, addend: 0x0, symName: __ZN25GeneratedWinbondDevice_2717getTachometerNameEh, symObjAddr: 0x7790, symBinAddr: 0x7FF0, symSize: 0x30 }
  - { offsetInCU: 0xB4B0, offset: 0x29E0E, size: 0x8, addend: 0x0, symName: __ZN25GeneratedWinbondDevice_2716updateTachometerEh, symObjAddr: 0x77C0, symBinAddr: 0x8020, symSize: 0x10 }
  - { offsetInCU: 0xB4EF, offset: 0x29E4D, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice18setTachometerValueEht.225, symObjAddr: 0x77D0, symBinAddr: 0x8030, symSize: 0x40 }
  - { offsetInCU: 0xB54A, offset: 0x29EA8, size: 0x8, addend: 0x0, symName: __ZN25GeneratedWinbondDevice_2715getVoltageCountEv, symObjAddr: 0x7810, symBinAddr: 0x8070, symSize: 0x10 }
  - { offsetInCU: 0xB579, offset: 0x29ED7, size: 0x8, addend: 0x0, symName: __ZN25GeneratedWinbondDevice_2714getVoltageNameEh, symObjAddr: 0x7820, symBinAddr: 0x8080, symSize: 0x30 }
  - { offsetInCU: 0xB5C0, offset: 0x29F1E, size: 0x8, addend: 0x0, symName: __ZN25GeneratedWinbondDevice_2713updateVoltageEh, symObjAddr: 0x7850, symBinAddr: 0x80B0, symSize: 0x10 }
  - { offsetInCU: 0xB605, offset: 0x29F63, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice15setVoltageValueEhf.226, symObjAddr: 0x7860, symBinAddr: 0x80C0, symSize: 0x40 }
  - { offsetInCU: 0xB660, offset: 0x29FBE, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice14updateVoltagesEv.227, symObjAddr: 0x78A0, symBinAddr: 0x8100, symSize: 0x60 }
  - { offsetInCU: 0xB6DF, offset: 0x2A03D, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice19getTemperatureCountEv.228, symObjAddr: 0x7900, symBinAddr: 0x8160, symSize: 0x10 }
  - { offsetInCU: 0xB70E, offset: 0x2A06C, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice18getTemperatureNameEh.229, symObjAddr: 0x7910, symBinAddr: 0x8170, symSize: 0x10 }
  - { offsetInCU: 0xB74C, offset: 0x2A0AA, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice17updateTemperatureEh.230, symObjAddr: 0x7920, symBinAddr: 0x8180, symSize: 0x10 }
  - { offsetInCU: 0xB78A, offset: 0x2A0E8, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice19setTemperatureValueEhf.231, symObjAddr: 0x7930, symBinAddr: 0x8190, symSize: 0x50 }
  - { offsetInCU: 0xB7E5, offset: 0x2A143, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice18updateTemperaturesEv.232, symObjAddr: 0x7980, symBinAddr: 0x81E0, symSize: 0x60 }
  - { offsetInCU: 0xB864, offset: 0x2A1C2, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice9onPowerOnEv.233, symObjAddr: 0x79E0, symBinAddr: 0x8240, symSize: 0x10 }
  - { offsetInCU: 0xB893, offset: 0x2A1F1, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice10initializeEttP10SMCSuperIO.234, symObjAddr: 0x79F0, symBinAddr: 0x8250, symSize: 0x20 }
  - { offsetInCU: 0xB8FB, offset: 0x2A259, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice13updateTargetsEv.235, symObjAddr: 0x7A10, symBinAddr: 0x8270, symSize: 0x10 }
  - { offsetInCU: 0xB92A, offset: 0x2A288, size: 0x8, addend: 0x0, symName: __ZN13Device_0x885012getModelNameEv, symObjAddr: 0x7A20, symBinAddr: 0x8280, symSize: 0x10 }
  - { offsetInCU: 0xB959, offset: 0x2A2B7, size: 0x8, addend: 0x0, symName: __ZN13Device_0x88506getLdnEv, symObjAddr: 0x7A30, symBinAddr: 0x8290, symSize: 0x10 }
  - { offsetInCU: 0xB988, offset: 0x2A2E6, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice14setTargetValueEht.236, symObjAddr: 0x7A40, symBinAddr: 0x82A0, symSize: 0x40 }
  - { offsetInCU: 0xB9E3, offset: 0x2A341, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice14setManualValueEhh.237, symObjAddr: 0x7A80, symBinAddr: 0x82E0, symSize: 0x40 }
  - { offsetInCU: 0xBA3E, offset: 0x2A39C, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice11setMaxValueEht.238, symObjAddr: 0x7AC0, symBinAddr: 0x8320, symSize: 0x40 }
  - { offsetInCU: 0xBA99, offset: 0x2A3F7, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice11setMinValueEht.239, symObjAddr: 0x7B00, symBinAddr: 0x8360, symSize: 0x40 }
  - { offsetInCU: 0xBAF4, offset: 0x2A452, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8850D1Ev, symObjAddr: 0x7B40, symBinAddr: 0x83A0, symSize: 0x10 }
  - { offsetInCU: 0xBB2A, offset: 0x2A488, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8850D0Ev, symObjAddr: 0x7B50, symBinAddr: 0x83B0, symSize: 0x10 }
  - { offsetInCU: 0xBB63, offset: 0x2A4C1, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_2418getTachometerCountEv, symObjAddr: 0x7B60, symBinAddr: 0x83C0, symSize: 0x10 }
  - { offsetInCU: 0xBB92, offset: 0x2A4F0, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_2417getTachometerNameEh, symObjAddr: 0x7B70, symBinAddr: 0x83D0, symSize: 0x30 }
  - { offsetInCU: 0xBBD9, offset: 0x2A537, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_2416updateTachometerEh, symObjAddr: 0x7BA0, symBinAddr: 0x8400, symSize: 0x70 }
  - { offsetInCU: 0xBDB8, offset: 0x2A716, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice17updateTachometersEv.248, symObjAddr: 0x7C10, symBinAddr: 0x8470, symSize: 0x60 }
  - { offsetInCU: 0xBE37, offset: 0x2A795, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_2415getVoltageCountEv, symObjAddr: 0x7C70, symBinAddr: 0x84D0, symSize: 0x10 }
  - { offsetInCU: 0xBE66, offset: 0x2A7C4, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_2414getVoltageNameEh, symObjAddr: 0x7C80, symBinAddr: 0x84E0, symSize: 0x30 }
  - { offsetInCU: 0xBEAD, offset: 0x2A80B, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_2413updateVoltageEh, symObjAddr: 0x7CB0, symBinAddr: 0x8510, symSize: 0x30 }
  - { offsetInCU: 0xBEB5, offset: 0x2A813, size: 0x8, addend: 0x0, symName: __ZN13Device_0x050712getModelNameEv, symObjAddr: 0x7CE0, symBinAddr: 0x8540, symSize: 0x10 }
  - { offsetInCU: 0xBFDE, offset: 0x2A93C, size: 0x8, addend: 0x0, symName: __ZN13Device_0x050712getModelNameEv, symObjAddr: 0x7CE0, symBinAddr: 0x8540, symSize: 0x10 }
  - { offsetInCU: 0xC00D, offset: 0x2A96B, size: 0x8, addend: 0x0, symName: __ZN13Device_0x05076getLdnEv, symObjAddr: 0x7CF0, symBinAddr: 0x8550, symSize: 0x10 }
  - { offsetInCU: 0xC03C, offset: 0x2A99A, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0507D1Ev, symObjAddr: 0x7D00, symBinAddr: 0x8560, symSize: 0x10 }
  - { offsetInCU: 0xC072, offset: 0x2A9D0, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0507D0Ev, symObjAddr: 0x7D10, symBinAddr: 0x8570, symSize: 0x10 }
  - { offsetInCU: 0xC0AB, offset: 0x2AA09, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_2318getTachometerCountEv, symObjAddr: 0x7D20, symBinAddr: 0x8580, symSize: 0x10 }
  - { offsetInCU: 0xC0DA, offset: 0x2AA38, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_2317getTachometerNameEh, symObjAddr: 0x7D30, symBinAddr: 0x8590, symSize: 0x30 }
  - { offsetInCU: 0xC121, offset: 0x2AA7F, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_2316updateTachometerEh, symObjAddr: 0x7D60, symBinAddr: 0x85C0, symSize: 0x80 }
  - { offsetInCU: 0xC406, offset: 0x2AD64, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_2315getVoltageCountEv, symObjAddr: 0x7DE0, symBinAddr: 0x8640, symSize: 0x10 }
  - { offsetInCU: 0xC435, offset: 0x2AD93, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_2314getVoltageNameEh, symObjAddr: 0x7DF0, symBinAddr: 0x8650, symSize: 0x30 }
  - { offsetInCU: 0xC47C, offset: 0x2ADDA, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_2313updateVoltageEh, symObjAddr: 0x7E20, symBinAddr: 0x8680, symSize: 0x80 }
  - { offsetInCU: 0xC751, offset: 0x2B0AF, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_239onPowerOnEv, symObjAddr: 0x7EA0, symBinAddr: 0x8700, symSize: 0x60 }
  - { offsetInCU: 0xCBE9, offset: 0x2B547, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD35212getModelNameEv, symObjAddr: 0x7F00, symBinAddr: 0x8760, symSize: 0x10 }
  - { offsetInCU: 0xCC18, offset: 0x2B576, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD3526getLdnEv, symObjAddr: 0x7F10, symBinAddr: 0x8770, symSize: 0x10 }
  - { offsetInCU: 0xCC47, offset: 0x2B5A5, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD352D1Ev, symObjAddr: 0x7F20, symBinAddr: 0x8780, symSize: 0x10 }
  - { offsetInCU: 0xCC7D, offset: 0x2B5DB, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD352D0Ev, symObjAddr: 0x7F30, symBinAddr: 0x8790, symSize: 0x10 }
  - { offsetInCU: 0xCCB6, offset: 0x2B614, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD12112getModelNameEv, symObjAddr: 0x7F40, symBinAddr: 0x87A0, symSize: 0x10 }
  - { offsetInCU: 0xCCE5, offset: 0x2B643, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD1216getLdnEv, symObjAddr: 0x7F50, symBinAddr: 0x87B0, symSize: 0x10 }
  - { offsetInCU: 0xCD14, offset: 0x2B672, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD121D1Ev, symObjAddr: 0x7F60, symBinAddr: 0x87C0, symSize: 0x10 }
  - { offsetInCU: 0xCD4A, offset: 0x2B6A8, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD121D0Ev, symObjAddr: 0x7F70, symBinAddr: 0x87D0, symSize: 0x10 }
  - { offsetInCU: 0xCD83, offset: 0x2B6E1, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC91112getModelNameEv, symObjAddr: 0x7F80, symBinAddr: 0x87E0, symSize: 0x10 }
  - { offsetInCU: 0xCDB2, offset: 0x2B710, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC9116getLdnEv, symObjAddr: 0x7F90, symBinAddr: 0x87F0, symSize: 0x10 }
  - { offsetInCU: 0xCDE1, offset: 0x2B73F, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC911D1Ev, symObjAddr: 0x7FA0, symBinAddr: 0x8800, symSize: 0x10 }
  - { offsetInCU: 0xCE17, offset: 0x2B775, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC911D0Ev, symObjAddr: 0x7FB0, symBinAddr: 0x8810, symSize: 0x10 }
  - { offsetInCU: 0xCE50, offset: 0x2B7AE, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC80312getModelNameEv, symObjAddr: 0x7FC0, symBinAddr: 0x8820, symSize: 0x10 }
  - { offsetInCU: 0xCE7F, offset: 0x2B7DD, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC8036getLdnEv, symObjAddr: 0x7FD0, symBinAddr: 0x8830, symSize: 0x10 }
  - { offsetInCU: 0xCEAE, offset: 0x2B80C, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC803D1Ev, symObjAddr: 0x7FE0, symBinAddr: 0x8840, symSize: 0x10 }
  - { offsetInCU: 0xCEE4, offset: 0x2B842, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC803D0Ev, symObjAddr: 0x7FF0, symBinAddr: 0x8850, symSize: 0x10 }
  - { offsetInCU: 0xCF1D, offset: 0x2B87B, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_2118getTachometerCountEv, symObjAddr: 0x8000, symBinAddr: 0x8860, symSize: 0x10 }
  - { offsetInCU: 0xCF4C, offset: 0x2B8AA, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_2117getTachometerNameEh, symObjAddr: 0x8010, symBinAddr: 0x8870, symSize: 0x30 }
  - { offsetInCU: 0xCF93, offset: 0x2B8F1, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_2116updateTachometerEh, symObjAddr: 0x8040, symBinAddr: 0x88A0, symSize: 0x80 }
  - { offsetInCU: 0xD278, offset: 0x2BBD6, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_2115getVoltageCountEv, symObjAddr: 0x80C0, symBinAddr: 0x8920, symSize: 0x10 }
  - { offsetInCU: 0xD2A7, offset: 0x2BC05, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_2114getVoltageNameEh, symObjAddr: 0x80D0, symBinAddr: 0x8930, symSize: 0x30 }
  - { offsetInCU: 0xD2EE, offset: 0x2BC4C, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_2113updateVoltageEh, symObjAddr: 0x8100, symBinAddr: 0x8960, symSize: 0x80 }
  - { offsetInCU: 0xD5C3, offset: 0x2BF21, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC33012getModelNameEv, symObjAddr: 0x8180, symBinAddr: 0x89E0, symSize: 0x10 }
  - { offsetInCU: 0xD5F2, offset: 0x2BF50, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC3306getLdnEv, symObjAddr: 0x8190, symBinAddr: 0x89F0, symSize: 0x10 }
  - { offsetInCU: 0xD621, offset: 0x2BF7F, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC330D1Ev, symObjAddr: 0x81A0, symBinAddr: 0x8A00, symSize: 0x10 }
  - { offsetInCU: 0xD657, offset: 0x2BFB5, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC330D0Ev, symObjAddr: 0x81B0, symBinAddr: 0x8A10, symSize: 0x10 }
  - { offsetInCU: 0xD690, offset: 0x2BFEE, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_1818getTachometerCountEv, symObjAddr: 0x81C0, symBinAddr: 0x8A20, symSize: 0x10 }
  - { offsetInCU: 0xD6BF, offset: 0x2C01D, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_1817getTachometerNameEh, symObjAddr: 0x81D0, symBinAddr: 0x8A30, symSize: 0x30 }
  - { offsetInCU: 0xD706, offset: 0x2C064, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_1816updateTachometerEh, symObjAddr: 0x8200, symBinAddr: 0x8A60, symSize: 0x70 }
  - { offsetInCU: 0xD8E5, offset: 0x2C243, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_1815getVoltageCountEv, symObjAddr: 0x8270, symBinAddr: 0x8AD0, symSize: 0x10 }
  - { offsetInCU: 0xD914, offset: 0x2C272, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_1814getVoltageNameEh, symObjAddr: 0x8280, symBinAddr: 0x8AE0, symSize: 0x30 }
  - { offsetInCU: 0xD95B, offset: 0x2C2B9, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_1813updateVoltageEh, symObjAddr: 0x82B0, symBinAddr: 0x8B10, symSize: 0x30 }
  - { offsetInCU: 0xD963, offset: 0x2C2C1, size: 0x8, addend: 0x0, symName: __ZN13Device_0x054112getModelNameEv, symObjAddr: 0x82E0, symBinAddr: 0x8B40, symSize: 0x10 }
  - { offsetInCU: 0xDA8C, offset: 0x2C3EA, size: 0x8, addend: 0x0, symName: __ZN13Device_0x054112getModelNameEv, symObjAddr: 0x82E0, symBinAddr: 0x8B40, symSize: 0x10 }
  - { offsetInCU: 0xDABB, offset: 0x2C419, size: 0x8, addend: 0x0, symName: __ZN13Device_0x05416getLdnEv, symObjAddr: 0x82F0, symBinAddr: 0x8B50, symSize: 0x10 }
  - { offsetInCU: 0xDAEA, offset: 0x2C448, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0541D1Ev, symObjAddr: 0x8300, symBinAddr: 0x8B60, symSize: 0x10 }
  - { offsetInCU: 0xDB20, offset: 0x2C47E, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0541D0Ev, symObjAddr: 0x8310, symBinAddr: 0x8B70, symSize: 0x10 }
  - { offsetInCU: 0xDB59, offset: 0x2C4B7, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_1518getTachometerCountEv, symObjAddr: 0x8320, symBinAddr: 0x8B80, symSize: 0x10 }
  - { offsetInCU: 0xDB88, offset: 0x2C4E6, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_1517getTachometerNameEh, symObjAddr: 0x8330, symBinAddr: 0x8B90, symSize: 0x30 }
  - { offsetInCU: 0xDBCF, offset: 0x2C52D, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_1516updateTachometerEh, symObjAddr: 0x8360, symBinAddr: 0x8BC0, symSize: 0x70 }
  - { offsetInCU: 0xDDAE, offset: 0x2C70C, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_1515getVoltageCountEv, symObjAddr: 0x83D0, symBinAddr: 0x8C30, symSize: 0x10 }
  - { offsetInCU: 0xDDDD, offset: 0x2C73B, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_1514getVoltageNameEh, symObjAddr: 0x83E0, symBinAddr: 0x8C40, symSize: 0x30 }
  - { offsetInCU: 0xDE24, offset: 0x2C782, size: 0x8, addend: 0x0, symName: __ZN24GeneratedFintekDevice_1513updateVoltageEh, symObjAddr: 0x8410, symBinAddr: 0x8C70, symSize: 0x30 }
  - { offsetInCU: 0xDE2C, offset: 0x2C78A, size: 0x8, addend: 0x0, symName: __ZN13Device_0x072312getModelNameEv, symObjAddr: 0x8440, symBinAddr: 0x8CA0, symSize: 0x10 }
  - { offsetInCU: 0xDF55, offset: 0x2C8B3, size: 0x8, addend: 0x0, symName: __ZN13Device_0x072312getModelNameEv, symObjAddr: 0x8440, symBinAddr: 0x8CA0, symSize: 0x10 }
  - { offsetInCU: 0xDF84, offset: 0x2C8E2, size: 0x8, addend: 0x0, symName: __ZN13Device_0x07236getLdnEv, symObjAddr: 0x8450, symBinAddr: 0x8CB0, symSize: 0x10 }
  - { offsetInCU: 0xDFB3, offset: 0x2C911, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0723D1Ev, symObjAddr: 0x8460, symBinAddr: 0x8CC0, symSize: 0x10 }
  - { offsetInCU: 0xDFE9, offset: 0x2C947, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0723D0Ev, symObjAddr: 0x8470, symBinAddr: 0x8CD0, symSize: 0x10 }
  - { offsetInCU: 0xE022, offset: 0x2C980, size: 0x8, addend: 0x0, symName: __ZN13Device_0x090912getModelNameEv, symObjAddr: 0x8480, symBinAddr: 0x8CE0, symSize: 0x10 }
  - { offsetInCU: 0xE051, offset: 0x2C9AF, size: 0x8, addend: 0x0, symName: __ZN13Device_0x09096getLdnEv, symObjAddr: 0x8490, symBinAddr: 0x8CF0, symSize: 0x10 }
  - { offsetInCU: 0xE080, offset: 0x2C9DE, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0909D1Ev, symObjAddr: 0x84A0, symBinAddr: 0x8D00, symSize: 0x10 }
  - { offsetInCU: 0xE0B6, offset: 0x2CA14, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0909D0Ev, symObjAddr: 0x84B0, symBinAddr: 0x8D10, symSize: 0x10 }
  - { offsetInCU: 0xE0EF, offset: 0x2CA4D, size: 0x8, addend: 0x0, symName: __ZN13Device_0x100512getModelNameEv, symObjAddr: 0x84C0, symBinAddr: 0x8D20, symSize: 0x10 }
  - { offsetInCU: 0xE11E, offset: 0x2CA7C, size: 0x8, addend: 0x0, symName: __ZN13Device_0x10056getLdnEv, symObjAddr: 0x84D0, symBinAddr: 0x8D30, symSize: 0x10 }
  - { offsetInCU: 0xE14D, offset: 0x2CAAB, size: 0x8, addend: 0x0, symName: __ZN13Device_0x1005D1Ev, symObjAddr: 0x84E0, symBinAddr: 0x8D40, symSize: 0x10 }
  - { offsetInCU: 0xE183, offset: 0x2CAE1, size: 0x8, addend: 0x0, symName: __ZN13Device_0x1005D0Ev, symObjAddr: 0x84F0, symBinAddr: 0x8D50, symSize: 0x10 }
  - { offsetInCU: 0xE1BC, offset: 0x2CB1A, size: 0x8, addend: 0x0, symName: __ZN13Device_0x100712getModelNameEv, symObjAddr: 0x8500, symBinAddr: 0x8D60, symSize: 0x10 }
  - { offsetInCU: 0xE1EB, offset: 0x2CB49, size: 0x8, addend: 0x0, symName: __ZN13Device_0x10076getLdnEv, symObjAddr: 0x8510, symBinAddr: 0x8D70, symSize: 0x10 }
  - { offsetInCU: 0xE21A, offset: 0x2CB78, size: 0x8, addend: 0x0, symName: __ZN13Device_0x1007D1Ev, symObjAddr: 0x8520, symBinAddr: 0x8D80, symSize: 0x10 }
  - { offsetInCU: 0xE250, offset: 0x2CBAE, size: 0x8, addend: 0x0, symName: __ZN13Device_0x1007D0Ev, symObjAddr: 0x8530, symBinAddr: 0x8D90, symSize: 0x10 }
  - { offsetInCU: 0xE289, offset: 0x2CBE7, size: 0x8, addend: 0x0, symName: __ZN13Device_0x081412getModelNameEv, symObjAddr: 0x8540, symBinAddr: 0x8DA0, symSize: 0x10 }
  - { offsetInCU: 0xE2B8, offset: 0x2CC16, size: 0x8, addend: 0x0, symName: __ZN13Device_0x08146getLdnEv, symObjAddr: 0x8550, symBinAddr: 0x8DB0, symSize: 0x10 }
  - { offsetInCU: 0xE2E7, offset: 0x2CC45, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0814D1Ev, symObjAddr: 0x8560, symBinAddr: 0x8DC0, symSize: 0x10 }
  - { offsetInCU: 0xE31D, offset: 0x2CC7B, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0814D0Ev, symObjAddr: 0x8570, symBinAddr: 0x8DD0, symSize: 0x10 }
  - { offsetInCU: 0xE356, offset: 0x2CCB4, size: 0x8, addend: 0x0, symName: __ZN13Device_0x110612getModelNameEv, symObjAddr: 0x8580, symBinAddr: 0x8DE0, symSize: 0x10 }
  - { offsetInCU: 0xE385, offset: 0x2CCE3, size: 0x8, addend: 0x0, symName: __ZN13Device_0x11066getLdnEv, symObjAddr: 0x8590, symBinAddr: 0x8DF0, symSize: 0x10 }
  - { offsetInCU: 0xE3B4, offset: 0x2CD12, size: 0x8, addend: 0x0, symName: __ZN13Device_0x1106D1Ev, symObjAddr: 0x85A0, symBinAddr: 0x8E00, symSize: 0x10 }
  - { offsetInCU: 0xE3EA, offset: 0x2CD48, size: 0x8, addend: 0x0, symName: __ZN13Device_0x1106D0Ev, symObjAddr: 0x85B0, symBinAddr: 0x8E10, symSize: 0x10 }
  - { offsetInCU: 0xE423, offset: 0x2CD81, size: 0x8, addend: 0x0, symName: __ZN13Device_0x060112getModelNameEv, symObjAddr: 0x85C0, symBinAddr: 0x8E20, symSize: 0x10 }
  - { offsetInCU: 0xE452, offset: 0x2CDB0, size: 0x8, addend: 0x0, symName: __ZN13Device_0x06016getLdnEv, symObjAddr: 0x85D0, symBinAddr: 0x8E30, symSize: 0x10 }
  - { offsetInCU: 0xE481, offset: 0x2CDDF, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0601D1Ev, symObjAddr: 0x85E0, symBinAddr: 0x8E40, symSize: 0x10 }
  - { offsetInCU: 0xE4B7, offset: 0x2CE15, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0601D0Ev, symObjAddr: 0x85F0, symBinAddr: 0x8E50, symSize: 0x10 }
  - { offsetInCU: 0xE4F0, offset: 0x2CE4E, size: 0x8, addend: 0x0, symName: __ZN25GeneratedWinbondDevice_1418getTachometerCountEv, symObjAddr: 0x8600, symBinAddr: 0x8E60, symSize: 0x10 }
  - { offsetInCU: 0xE51F, offset: 0x2CE7D, size: 0x8, addend: 0x0, symName: __ZN25GeneratedWinbondDevice_1417getTachometerNameEh, symObjAddr: 0x8610, symBinAddr: 0x8E70, symSize: 0x30 }
  - { offsetInCU: 0xE566, offset: 0x2CEC4, size: 0x8, addend: 0x0, symName: __ZN25GeneratedWinbondDevice_1416updateTachometerEh, symObjAddr: 0x8640, symBinAddr: 0x8EA0, symSize: 0x10 }
  - { offsetInCU: 0xE5A5, offset: 0x2CF03, size: 0x8, addend: 0x0, symName: __ZN25GeneratedWinbondDevice_1415getVoltageCountEv, symObjAddr: 0x8650, symBinAddr: 0x8EB0, symSize: 0x10 }
  - { offsetInCU: 0xE5D4, offset: 0x2CF32, size: 0x8, addend: 0x0, symName: __ZN25GeneratedWinbondDevice_1414getVoltageNameEh, symObjAddr: 0x8660, symBinAddr: 0x8EC0, symSize: 0x30 }
  - { offsetInCU: 0xE61B, offset: 0x2CF79, size: 0x8, addend: 0x0, symName: __ZN25GeneratedWinbondDevice_1413updateVoltageEh, symObjAddr: 0x8690, symBinAddr: 0x8EF0, symSize: 0x10 }
  - { offsetInCU: 0xE660, offset: 0x2CFBE, size: 0x8, addend: 0x0, symName: __ZN13Device_0xB35012getModelNameEv, symObjAddr: 0x86A0, symBinAddr: 0x8F00, symSize: 0x10 }
  - { offsetInCU: 0xE68F, offset: 0x2CFED, size: 0x8, addend: 0x0, symName: __ZN13Device_0xB3506getLdnEv, symObjAddr: 0x86B0, symBinAddr: 0x8F10, symSize: 0x10 }
  - { offsetInCU: 0xE6BE, offset: 0x2D01C, size: 0x8, addend: 0x0, symName: __ZN13Device_0xB350D1Ev, symObjAddr: 0x86C0, symBinAddr: 0x8F20, symSize: 0x10 }
  - { offsetInCU: 0xE6F4, offset: 0x2D052, size: 0x8, addend: 0x0, symName: __ZN13Device_0xB350D0Ev, symObjAddr: 0x86D0, symBinAddr: 0x8F30, symSize: 0x10 }
  - { offsetInCU: 0xE72D, offset: 0x2D08B, size: 0x8, addend: 0x0, symName: __ZN13Device_0xA51012getModelNameEv, symObjAddr: 0x86E0, symBinAddr: 0x8F40, symSize: 0x10 }
  - { offsetInCU: 0xE75C, offset: 0x2D0BA, size: 0x8, addend: 0x0, symName: __ZN13Device_0xA5106getLdnEv, symObjAddr: 0x86F0, symBinAddr: 0x8F50, symSize: 0x10 }
  - { offsetInCU: 0xE78B, offset: 0x2D0E9, size: 0x8, addend: 0x0, symName: __ZN13Device_0xA510D1Ev, symObjAddr: 0x8700, symBinAddr: 0x8F60, symSize: 0x10 }
  - { offsetInCU: 0xE7C1, offset: 0x2D11F, size: 0x8, addend: 0x0, symName: __ZN13Device_0xA510D0Ev, symObjAddr: 0x8710, symBinAddr: 0x8F70, symSize: 0x10 }
  - { offsetInCU: 0xE7FA, offset: 0x2D158, size: 0x8, addend: 0x0, symName: __ZN13Device_0xB07012getModelNameEv, symObjAddr: 0x8720, symBinAddr: 0x8F80, symSize: 0x10 }
  - { offsetInCU: 0xE829, offset: 0x2D187, size: 0x8, addend: 0x0, symName: __ZN13Device_0xB0706getLdnEv, symObjAddr: 0x8730, symBinAddr: 0x8F90, symSize: 0x10 }
  - { offsetInCU: 0xE858, offset: 0x2D1B6, size: 0x8, addend: 0x0, symName: __ZN13Device_0xB070D1Ev, symObjAddr: 0x8740, symBinAddr: 0x8FA0, symSize: 0x10 }
  - { offsetInCU: 0xE88E, offset: 0x2D1EC, size: 0x8, addend: 0x0, symName: __ZN13Device_0xB070D0Ev, symObjAddr: 0x8750, symBinAddr: 0x8FB0, symSize: 0x10 }
  - { offsetInCU: 0xE8C7, offset: 0x2D225, size: 0x8, addend: 0x0, symName: __ZN13Device_0x886012getModelNameEv, symObjAddr: 0x8760, symBinAddr: 0x8FC0, symSize: 0x10 }
  - { offsetInCU: 0xE8F6, offset: 0x2D254, size: 0x8, addend: 0x0, symName: __ZN13Device_0x88606getLdnEv, symObjAddr: 0x8770, symBinAddr: 0x8FD0, symSize: 0x10 }
  - { offsetInCU: 0xE925, offset: 0x2D283, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8860D1Ev, symObjAddr: 0x8780, symBinAddr: 0x8FE0, symSize: 0x10 }
  - { offsetInCU: 0xE95B, offset: 0x2D2B9, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8860D0Ev, symObjAddr: 0x8790, symBinAddr: 0x8FF0, symSize: 0x10 }
  - { offsetInCU: 0xE994, offset: 0x2D2F2, size: 0x8, addend: 0x0, symName: __ZN13Device_0xA02012getModelNameEv, symObjAddr: 0x87A0, symBinAddr: 0x9000, symSize: 0x10 }
  - { offsetInCU: 0xE9C3, offset: 0x2D321, size: 0x8, addend: 0x0, symName: __ZN13Device_0xA0206getLdnEv, symObjAddr: 0x87B0, symBinAddr: 0x9010, symSize: 0x10 }
  - { offsetInCU: 0xE9F2, offset: 0x2D350, size: 0x8, addend: 0x0, symName: __ZN13Device_0xA020D1Ev, symObjAddr: 0x87C0, symBinAddr: 0x9020, symSize: 0x10 }
  - { offsetInCU: 0xEA28, offset: 0x2D386, size: 0x8, addend: 0x0, symName: __ZN13Device_0xA020D0Ev, symObjAddr: 0x87D0, symBinAddr: 0x9030, symSize: 0x10 }
  - { offsetInCU: 0xEA61, offset: 0x2D3BF, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_1318getTachometerCountEv, symObjAddr: 0x87E0, symBinAddr: 0x9040, symSize: 0x10 }
  - { offsetInCU: 0xEA90, offset: 0x2D3EE, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_1317getTachometerNameEh, symObjAddr: 0x87F0, symBinAddr: 0x9050, symSize: 0x30 }
  - { offsetInCU: 0xEAD7, offset: 0x2D435, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_1316updateTachometerEh, symObjAddr: 0x8820, symBinAddr: 0x9080, symSize: 0x80 }
  - { offsetInCU: 0xEDC3, offset: 0x2D721, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_1315getVoltageCountEv, symObjAddr: 0x88A0, symBinAddr: 0x9100, symSize: 0x10 }
  - { offsetInCU: 0xEDF2, offset: 0x2D750, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_1314getVoltageNameEh, symObjAddr: 0x88B0, symBinAddr: 0x9110, symSize: 0x30 }
  - { offsetInCU: 0xEE39, offset: 0x2D797, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_1313updateVoltageEh, symObjAddr: 0x88E0, symBinAddr: 0x9140, symSize: 0x50 }
  - { offsetInCU: 0xEFFC, offset: 0x2D95A, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_139onPowerOnEv, symObjAddr: 0x8930, symBinAddr: 0x9190, symSize: 0x80 }
  - { offsetInCU: 0xF1BA, offset: 0x2DB18, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD59012getModelNameEv, symObjAddr: 0x89B0, symBinAddr: 0x9210, symSize: 0x10 }
  - { offsetInCU: 0xF1E9, offset: 0x2DB47, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD5906getLdnEv, symObjAddr: 0x89C0, symBinAddr: 0x9220, symSize: 0x10 }
  - { offsetInCU: 0xF218, offset: 0x2DB76, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD590D1Ev, symObjAddr: 0x89D0, symBinAddr: 0x9230, symSize: 0x10 }
  - { offsetInCU: 0xF24E, offset: 0x2DBAC, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD590D0Ev, symObjAddr: 0x89E0, symBinAddr: 0x9240, symSize: 0x10 }
  - { offsetInCU: 0xF287, offset: 0x2DBE5, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD44012getModelNameEv, symObjAddr: 0x89F0, symBinAddr: 0x9250, symSize: 0x10 }
  - { offsetInCU: 0xF2B6, offset: 0x2DC14, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD4406getLdnEv, symObjAddr: 0x8A00, symBinAddr: 0x9260, symSize: 0x10 }
  - { offsetInCU: 0xF2E5, offset: 0x2DC43, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD440D1Ev, symObjAddr: 0x8A10, symBinAddr: 0x9270, symSize: 0x10 }
  - { offsetInCU: 0xF31B, offset: 0x2DC79, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD440D0Ev, symObjAddr: 0x8A20, symBinAddr: 0x9280, symSize: 0x10 }
  - { offsetInCU: 0xF354, offset: 0x2DCB2, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC73012getModelNameEv, symObjAddr: 0x8A30, symBinAddr: 0x9290, symSize: 0x10 }
  - { offsetInCU: 0xF383, offset: 0x2DCE1, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC7306getLdnEv, symObjAddr: 0x8A40, symBinAddr: 0x92A0, symSize: 0x10 }
  - { offsetInCU: 0xF3B2, offset: 0x2DD10, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC730D1Ev, symObjAddr: 0x8A50, symBinAddr: 0x92B0, symSize: 0x10 }
  - { offsetInCU: 0xF3E8, offset: 0x2DD46, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC730D0Ev, symObjAddr: 0x8A60, symBinAddr: 0x92C0, symSize: 0x10 }
  - { offsetInCU: 0xF421, offset: 0x2DD7F, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_1018getTachometerCountEv, symObjAddr: 0x8A70, symBinAddr: 0x92D0, symSize: 0x10 }
  - { offsetInCU: 0xF450, offset: 0x2DDAE, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_1017getTachometerNameEh, symObjAddr: 0x8A80, symBinAddr: 0x92E0, symSize: 0x30 }
  - { offsetInCU: 0xF497, offset: 0x2DDF5, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_1016updateTachometerEh, symObjAddr: 0x8AB0, symBinAddr: 0x9310, symSize: 0x80 }
  - { offsetInCU: 0xF77C, offset: 0x2E0DA, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_1015getVoltageCountEv, symObjAddr: 0x8B30, symBinAddr: 0x9390, symSize: 0x10 }
  - { offsetInCU: 0xF7AB, offset: 0x2E109, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_1014getVoltageNameEh, symObjAddr: 0x8B40, symBinAddr: 0x93A0, symSize: 0x30 }
  - { offsetInCU: 0xF7F2, offset: 0x2E150, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_1013updateVoltageEh, symObjAddr: 0x8B70, symBinAddr: 0x93D0, symSize: 0x80 }
  - { offsetInCU: 0xFAC7, offset: 0x2E425, size: 0x8, addend: 0x0, symName: __ZN25GeneratedNuvotonDevice_109onPowerOnEv, symObjAddr: 0x8BF0, symBinAddr: 0x9450, symSize: 0x60 }
  - { offsetInCU: 0xFF5F, offset: 0x2E8BD, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42B12getModelNameEv, symObjAddr: 0x8C50, symBinAddr: 0x94B0, symSize: 0x10 }
  - { offsetInCU: 0xFF8E, offset: 0x2E8EC, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42B6getLdnEv, symObjAddr: 0x8C60, symBinAddr: 0x94C0, symSize: 0x10 }
  - { offsetInCU: 0xFFBD, offset: 0x2E91B, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42BD1Ev, symObjAddr: 0x8C70, symBinAddr: 0x94D0, symSize: 0x10 }
  - { offsetInCU: 0xFFF3, offset: 0x2E951, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42BD0Ev, symObjAddr: 0x8C80, symBinAddr: 0x94E0, symSize: 0x10 }
  - { offsetInCU: 0x1002C, offset: 0x2E98A, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42A12getModelNameEv, symObjAddr: 0x8C90, symBinAddr: 0x94F0, symSize: 0x10 }
  - { offsetInCU: 0x1005B, offset: 0x2E9B9, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42A6getLdnEv, symObjAddr: 0x8CA0, symBinAddr: 0x9500, symSize: 0x10 }
  - { offsetInCU: 0x1008A, offset: 0x2E9E8, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42AD1Ev, symObjAddr: 0x8CB0, symBinAddr: 0x9510, symSize: 0x10 }
  - { offsetInCU: 0x100C0, offset: 0x2EA1E, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42AD0Ev, symObjAddr: 0x8CC0, symBinAddr: 0x9520, symSize: 0x10 }
  - { offsetInCU: 0x100F9, offset: 0x2EA57, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD80212getModelNameEv, symObjAddr: 0x8CD0, symBinAddr: 0x9530, symSize: 0x10 }
  - { offsetInCU: 0x10128, offset: 0x2EA86, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD8026getLdnEv, symObjAddr: 0x8CE0, symBinAddr: 0x9540, symSize: 0x10 }
  - { offsetInCU: 0x10157, offset: 0x2EAB5, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD802D1Ev, symObjAddr: 0x8CF0, symBinAddr: 0x9550, symSize: 0x10 }
  - { offsetInCU: 0x1018D, offset: 0x2EAEB, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD802D0Ev, symObjAddr: 0x8D00, symBinAddr: 0x9560, symSize: 0x10 }
  - { offsetInCU: 0x101C6, offset: 0x2EB24, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42812getModelNameEv, symObjAddr: 0x8D10, symBinAddr: 0x9570, symSize: 0x10 }
  - { offsetInCU: 0x101F5, offset: 0x2EB53, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD4286getLdnEv, symObjAddr: 0x8D20, symBinAddr: 0x9580, symSize: 0x10 }
  - { offsetInCU: 0x10224, offset: 0x2EB82, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD428D1Ev, symObjAddr: 0x8D30, symBinAddr: 0x9590, symSize: 0x10 }
  - { offsetInCU: 0x1025A, offset: 0x2EBB8, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD428D0Ev, symObjAddr: 0x8D40, symBinAddr: 0x95A0, symSize: 0x10 }
  - { offsetInCU: 0x10293, offset: 0x2EBF1, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD45112getModelNameEv, symObjAddr: 0x8D50, symBinAddr: 0x95B0, symSize: 0x10 }
  - { offsetInCU: 0x102C2, offset: 0x2EC20, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD4516getLdnEv, symObjAddr: 0x8D60, symBinAddr: 0x95C0, symSize: 0x10 }
  - { offsetInCU: 0x102F1, offset: 0x2EC4F, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD451D1Ev, symObjAddr: 0x8D70, symBinAddr: 0x95D0, symSize: 0x10 }
  - { offsetInCU: 0x10327, offset: 0x2EC85, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD451D0Ev, symObjAddr: 0x8D80, symBinAddr: 0x95E0, symSize: 0x10 }
  - { offsetInCU: 0x10360, offset: 0x2ECBE, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD42312getModelNameEv, symObjAddr: 0x8D90, symBinAddr: 0x95F0, symSize: 0x10 }
  - { offsetInCU: 0x1038F, offset: 0x2ECED, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD4236getLdnEv, symObjAddr: 0x8DA0, symBinAddr: 0x9600, symSize: 0x10 }
  - { offsetInCU: 0x103BE, offset: 0x2ED1C, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD423D1Ev, symObjAddr: 0x8DB0, symBinAddr: 0x9610, symSize: 0x10 }
  - { offsetInCU: 0x103F4, offset: 0x2ED52, size: 0x8, addend: 0x0, symName: __ZN13Device_0xD423D0Ev, symObjAddr: 0x8DC0, symBinAddr: 0x9620, symSize: 0x10 }
  - { offsetInCU: 0x1042D, offset: 0x2ED8B, size: 0x8, addend: 0x0, symName: __ZN24GeneratedNuvotonDevice_818getTachometerCountEv, symObjAddr: 0x8DD0, symBinAddr: 0x9630, symSize: 0x10 }
  - { offsetInCU: 0x1045C, offset: 0x2EDBA, size: 0x8, addend: 0x0, symName: __ZN24GeneratedNuvotonDevice_817getTachometerNameEh, symObjAddr: 0x8DE0, symBinAddr: 0x9640, symSize: 0x30 }
  - { offsetInCU: 0x104A3, offset: 0x2EE01, size: 0x8, addend: 0x0, symName: __ZN24GeneratedNuvotonDevice_816updateTachometerEh, symObjAddr: 0x8E10, symBinAddr: 0x9670, symSize: 0x80 }
  - { offsetInCU: 0x10788, offset: 0x2F0E6, size: 0x8, addend: 0x0, symName: __ZN24GeneratedNuvotonDevice_815getVoltageCountEv, symObjAddr: 0x8E90, symBinAddr: 0x96F0, symSize: 0x10 }
  - { offsetInCU: 0x107B7, offset: 0x2F115, size: 0x8, addend: 0x0, symName: __ZN24GeneratedNuvotonDevice_814getVoltageNameEh, symObjAddr: 0x8EA0, symBinAddr: 0x9700, symSize: 0x30 }
  - { offsetInCU: 0x107FE, offset: 0x2F15C, size: 0x8, addend: 0x0, symName: __ZN24GeneratedNuvotonDevice_813updateVoltageEh, symObjAddr: 0x8ED0, symBinAddr: 0x9730, symSize: 0x80 }
  - { offsetInCU: 0x10AD3, offset: 0x2F431, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC56012getModelNameEv, symObjAddr: 0x8F50, symBinAddr: 0x97B0, symSize: 0x10 }
  - { offsetInCU: 0x10B02, offset: 0x2F460, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC5606getLdnEv, symObjAddr: 0x8F60, symBinAddr: 0x97C0, symSize: 0x10 }
  - { offsetInCU: 0x10B31, offset: 0x2F48F, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC560D1Ev, symObjAddr: 0x8F70, symBinAddr: 0x97D0, symSize: 0x10 }
  - { offsetInCU: 0x10B67, offset: 0x2F4C5, size: 0x8, addend: 0x0, symName: __ZN13Device_0xC560D0Ev, symObjAddr: 0x8F80, symBinAddr: 0x97E0, symSize: 0x10 }
  - { offsetInCU: 0x10BA0, offset: 0x2F4FE, size: 0x8, addend: 0x0, symName: __ZN24GeneratedWinbondDevice_318getTachometerCountEv, symObjAddr: 0x8F90, symBinAddr: 0x97F0, symSize: 0x10 }
  - { offsetInCU: 0x10BCF, offset: 0x2F52D, size: 0x8, addend: 0x0, symName: __ZN24GeneratedWinbondDevice_317getTachometerNameEh, symObjAddr: 0x8FA0, symBinAddr: 0x9800, symSize: 0x30 }
  - { offsetInCU: 0x10C16, offset: 0x2F574, size: 0x8, addend: 0x0, symName: __ZN24GeneratedWinbondDevice_316updateTachometerEh, symObjAddr: 0x8FD0, symBinAddr: 0x9830, symSize: 0x10 }
  - { offsetInCU: 0x10C55, offset: 0x2F5B3, size: 0x8, addend: 0x0, symName: __ZN24GeneratedWinbondDevice_315getVoltageCountEv, symObjAddr: 0x8FE0, symBinAddr: 0x9840, symSize: 0x10 }
  - { offsetInCU: 0x10C84, offset: 0x2F5E2, size: 0x8, addend: 0x0, symName: __ZN24GeneratedWinbondDevice_314getVoltageNameEh, symObjAddr: 0x8FF0, symBinAddr: 0x9850, symSize: 0x30 }
  - { offsetInCU: 0x10CCB, offset: 0x2F629, size: 0x8, addend: 0x0, symName: __ZN24GeneratedWinbondDevice_313updateVoltageEh, symObjAddr: 0x9020, symBinAddr: 0x9880, symSize: 0x100 }
  - { offsetInCU: 0x11341, offset: 0x2FC9F, size: 0x8, addend: 0x0, symName: __ZN13Device_0x854112getModelNameEv, symObjAddr: 0x9120, symBinAddr: 0x9980, symSize: 0x10 }
  - { offsetInCU: 0x11370, offset: 0x2FCCE, size: 0x8, addend: 0x0, symName: __ZN13Device_0x85416getLdnEv, symObjAddr: 0x9130, symBinAddr: 0x9990, symSize: 0x10 }
  - { offsetInCU: 0x1139F, offset: 0x2FCFD, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8541D1Ev, symObjAddr: 0x9140, symBinAddr: 0x99A0, symSize: 0x10 }
  - { offsetInCU: 0x113D5, offset: 0x2FD33, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8541D0Ev, symObjAddr: 0x9150, symBinAddr: 0x99B0, symSize: 0x10 }
  - { offsetInCU: 0x1140E, offset: 0x2FD6C, size: 0x8, addend: 0x0, symName: __ZN13Device_0x828012getModelNameEv, symObjAddr: 0x9160, symBinAddr: 0x99C0, symSize: 0x10 }
  - { offsetInCU: 0x1143D, offset: 0x2FD9B, size: 0x8, addend: 0x0, symName: __ZN13Device_0x82806getLdnEv, symObjAddr: 0x9170, symBinAddr: 0x99D0, symSize: 0x10 }
  - { offsetInCU: 0x1146C, offset: 0x2FDCA, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8280D1Ev, symObjAddr: 0x9180, symBinAddr: 0x99E0, symSize: 0x10 }
  - { offsetInCU: 0x114A2, offset: 0x2FE00, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8280D0Ev, symObjAddr: 0x9190, symBinAddr: 0x99F0, symSize: 0x10 }
  - { offsetInCU: 0x114DB, offset: 0x2FE39, size: 0x8, addend: 0x0, symName: __ZN13Device_0x524112getModelNameEv, symObjAddr: 0x91A0, symBinAddr: 0x9A00, symSize: 0x10 }
  - { offsetInCU: 0x1150A, offset: 0x2FE68, size: 0x8, addend: 0x0, symName: __ZN13Device_0x52416getLdnEv, symObjAddr: 0x91B0, symBinAddr: 0x9A10, symSize: 0x10 }
  - { offsetInCU: 0x11539, offset: 0x2FE97, size: 0x8, addend: 0x0, symName: __ZN13Device_0x5241D1Ev, symObjAddr: 0x91C0, symBinAddr: 0x9A20, symSize: 0x10 }
  - { offsetInCU: 0x1156F, offset: 0x2FECD, size: 0x8, addend: 0x0, symName: __ZN13Device_0x5241D0Ev, symObjAddr: 0x91D0, symBinAddr: 0x9A30, symSize: 0x10 }
  - { offsetInCU: 0x115A8, offset: 0x2FF06, size: 0x8, addend: 0x0, symName: __ZN13Device_0x523A12getModelNameEv, symObjAddr: 0x91E0, symBinAddr: 0x9A40, symSize: 0x10 }
  - { offsetInCU: 0x115D7, offset: 0x2FF35, size: 0x8, addend: 0x0, symName: __ZN13Device_0x523A6getLdnEv, symObjAddr: 0x91F0, symBinAddr: 0x9A50, symSize: 0x10 }
  - { offsetInCU: 0x11606, offset: 0x2FF64, size: 0x8, addend: 0x0, symName: __ZN13Device_0x523AD1Ev, symObjAddr: 0x9200, symBinAddr: 0x9A60, symSize: 0x10 }
  - { offsetInCU: 0x1163C, offset: 0x2FF9A, size: 0x8, addend: 0x0, symName: __ZN13Device_0x523AD0Ev, symObjAddr: 0x9210, symBinAddr: 0x9A70, symSize: 0x10 }
  - { offsetInCU: 0x11675, offset: 0x2FFD3, size: 0x8, addend: 0x0, symName: __ZN13Device_0x521712getModelNameEv, symObjAddr: 0x9220, symBinAddr: 0x9A80, symSize: 0x10 }
  - { offsetInCU: 0x116A4, offset: 0x30002, size: 0x8, addend: 0x0, symName: __ZN13Device_0x52176getLdnEv, symObjAddr: 0x9230, symBinAddr: 0x9A90, symSize: 0x10 }
  - { offsetInCU: 0x116D3, offset: 0x30031, size: 0x8, addend: 0x0, symName: __ZN13Device_0x5217D1Ev, symObjAddr: 0x9240, symBinAddr: 0x9AA0, symSize: 0x10 }
  - { offsetInCU: 0x11709, offset: 0x30067, size: 0x8, addend: 0x0, symName: __ZN13Device_0x5217D0Ev, symObjAddr: 0x9250, symBinAddr: 0x9AB0, symSize: 0x10 }
  - { offsetInCU: 0x11742, offset: 0x300A0, size: 0x8, addend: 0x0, symName: __ZN23GeneratedFintekDevice_218getTachometerCountEv, symObjAddr: 0x9260, symBinAddr: 0x9AC0, symSize: 0x10 }
  - { offsetInCU: 0x11771, offset: 0x300CF, size: 0x8, addend: 0x0, symName: __ZN23GeneratedFintekDevice_217getTachometerNameEh, symObjAddr: 0x9270, symBinAddr: 0x9AD0, symSize: 0x30 }
  - { offsetInCU: 0x117B8, offset: 0x30116, size: 0x8, addend: 0x0, symName: __ZN23GeneratedFintekDevice_216updateTachometerEh, symObjAddr: 0x92A0, symBinAddr: 0x9B00, symSize: 0x70 }
  - { offsetInCU: 0x11996, offset: 0x302F4, size: 0x8, addend: 0x0, symName: __ZN23GeneratedFintekDevice_215getVoltageCountEv, symObjAddr: 0x9310, symBinAddr: 0x9B70, symSize: 0x10 }
  - { offsetInCU: 0x119C5, offset: 0x30323, size: 0x8, addend: 0x0, symName: __ZN23GeneratedFintekDevice_214getVoltageNameEh, symObjAddr: 0x9320, symBinAddr: 0x9B80, symSize: 0x30 }
  - { offsetInCU: 0x11A0C, offset: 0x3036A, size: 0x8, addend: 0x0, symName: __ZN23GeneratedFintekDevice_213updateVoltageEh, symObjAddr: 0x9350, symBinAddr: 0x9BB0, symSize: 0x40 }
  - { offsetInCU: 0x11B6E, offset: 0x304CC, size: 0x8, addend: 0x0, symName: __ZN13Device_0x090112getModelNameEv, symObjAddr: 0x9390, symBinAddr: 0x9BF0, symSize: 0x10 }
  - { offsetInCU: 0x11B9D, offset: 0x304FB, size: 0x8, addend: 0x0, symName: __ZN13Device_0x09016getLdnEv, symObjAddr: 0x93A0, symBinAddr: 0x9C00, symSize: 0x10 }
  - { offsetInCU: 0x11BCC, offset: 0x3052A, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0901D1Ev, symObjAddr: 0x93B0, symBinAddr: 0x9C10, symSize: 0x10 }
  - { offsetInCU: 0x11C02, offset: 0x30560, size: 0x8, addend: 0x0, symName: __ZN13Device_0x0901D0Ev, symObjAddr: 0x93C0, symBinAddr: 0x9C20, symSize: 0x10 }
  - { offsetInCU: 0x11C3B, offset: 0x30599, size: 0x8, addend: 0x0, symName: __ZN24GeneratedNuvotonDevice_118getTachometerCountEv, symObjAddr: 0x93D0, symBinAddr: 0x9C30, symSize: 0x10 }
  - { offsetInCU: 0x11C6A, offset: 0x305C8, size: 0x8, addend: 0x0, symName: __ZN24GeneratedNuvotonDevice_117getTachometerNameEh, symObjAddr: 0x93E0, symBinAddr: 0x9C40, symSize: 0x30 }
  - { offsetInCU: 0x11CB1, offset: 0x3060F, size: 0x8, addend: 0x0, symName: __ZN24GeneratedNuvotonDevice_116updateTachometerEh, symObjAddr: 0x9410, symBinAddr: 0x9C70, symSize: 0x80 }
  - { offsetInCU: 0x11F95, offset: 0x308F3, size: 0x8, addend: 0x0, symName: __ZN24GeneratedNuvotonDevice_115getVoltageCountEv, symObjAddr: 0x9490, symBinAddr: 0x9CF0, symSize: 0x10 }
  - { offsetInCU: 0x11FC4, offset: 0x30922, size: 0x8, addend: 0x0, symName: __ZN24GeneratedNuvotonDevice_114getVoltageNameEh, symObjAddr: 0x94A0, symBinAddr: 0x9D00, symSize: 0x30 }
  - { offsetInCU: 0x1200B, offset: 0x30969, size: 0x8, addend: 0x0, symName: __ZN24GeneratedNuvotonDevice_113updateVoltageEh, symObjAddr: 0x94D0, symBinAddr: 0x9D30, symSize: 0x80 }
  - { offsetInCU: 0x122DF, offset: 0x30C3D, size: 0x8, addend: 0x0, symName: __ZN13Device_0xB47012getModelNameEv, symObjAddr: 0x9550, symBinAddr: 0x9DB0, symSize: 0x10 }
  - { offsetInCU: 0x1230E, offset: 0x30C6C, size: 0x8, addend: 0x0, symName: __ZN13Device_0xB4706getLdnEv, symObjAddr: 0x9560, symBinAddr: 0x9DC0, symSize: 0x10 }
  - { offsetInCU: 0x1233D, offset: 0x30C9B, size: 0x8, addend: 0x0, symName: __ZN13Device_0xB470D1Ev, symObjAddr: 0x9570, symBinAddr: 0x9DD0, symSize: 0x10 }
  - { offsetInCU: 0x12372, offset: 0x30CD0, size: 0x8, addend: 0x0, symName: __ZN13Device_0xB470D0Ev, symObjAddr: 0x9580, symBinAddr: 0x9DE0, symSize: 0x10 }
  - { offsetInCU: 0x123AA, offset: 0x30D08, size: 0x8, addend: 0x0, symName: __ZN21GeneratedITEDevice_1718getTachometerCountEv, symObjAddr: 0x9590, symBinAddr: 0x9DF0, symSize: 0x10 }
  - { offsetInCU: 0x123D9, offset: 0x30D37, size: 0x8, addend: 0x0, symName: __ZN21GeneratedITEDevice_1717getTachometerNameEh, symObjAddr: 0x95A0, symBinAddr: 0x9E00, symSize: 0x30 }
  - { offsetInCU: 0x12420, offset: 0x30D7E, size: 0x8, addend: 0x0, symName: __ZN21GeneratedITEDevice_1716updateTachometerEh, symObjAddr: 0x95D0, symBinAddr: 0x9E30, symSize: 0x80 }
  - { offsetInCU: 0x12602, offset: 0x30F60, size: 0x8, addend: 0x0, symName: __ZN21GeneratedITEDevice_1715getVoltageCountEv, symObjAddr: 0x9650, symBinAddr: 0x9EB0, symSize: 0x10 }
  - { offsetInCU: 0x12631, offset: 0x30F8F, size: 0x8, addend: 0x0, symName: __ZN21GeneratedITEDevice_1714getVoltageNameEh, symObjAddr: 0x9660, symBinAddr: 0x9EC0, symSize: 0x30 }
  - { offsetInCU: 0x12678, offset: 0x30FD6, size: 0x8, addend: 0x0, symName: __ZN21GeneratedITEDevice_1713updateVoltageEh, symObjAddr: 0x9690, symBinAddr: 0x9EF0, symSize: 0x30 }
  - { offsetInCU: 0x12680, offset: 0x30FDE, size: 0x8, addend: 0x0, symName: __ZN13Device_0x872012getModelNameEv, symObjAddr: 0x96C0, symBinAddr: 0x9F20, symSize: 0x10 }
  - { offsetInCU: 0x127A9, offset: 0x31107, size: 0x8, addend: 0x0, symName: __ZN13Device_0x872012getModelNameEv, symObjAddr: 0x96C0, symBinAddr: 0x9F20, symSize: 0x10 }
  - { offsetInCU: 0x127D8, offset: 0x31136, size: 0x8, addend: 0x0, symName: __ZN13Device_0x87206getLdnEv, symObjAddr: 0x96D0, symBinAddr: 0x9F30, symSize: 0x10 }
  - { offsetInCU: 0x12807, offset: 0x31165, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8720D1Ev, symObjAddr: 0x96E0, symBinAddr: 0x9F40, symSize: 0x10 }
  - { offsetInCU: 0x1283D, offset: 0x3119B, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8720D0Ev, symObjAddr: 0x96F0, symBinAddr: 0x9F50, symSize: 0x10 }
  - { offsetInCU: 0x12876, offset: 0x311D4, size: 0x8, addend: 0x0, symName: __ZN13Device_0x871812getModelNameEv, symObjAddr: 0x9700, symBinAddr: 0x9F60, symSize: 0x10 }
  - { offsetInCU: 0x128A5, offset: 0x31203, size: 0x8, addend: 0x0, symName: __ZN13Device_0x87186getLdnEv, symObjAddr: 0x9710, symBinAddr: 0x9F70, symSize: 0x10 }
  - { offsetInCU: 0x128D4, offset: 0x31232, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8718D1Ev, symObjAddr: 0x9720, symBinAddr: 0x9F80, symSize: 0x10 }
  - { offsetInCU: 0x1290A, offset: 0x31268, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8718D0Ev, symObjAddr: 0x9730, symBinAddr: 0x9F90, symSize: 0x10 }
  - { offsetInCU: 0x12943, offset: 0x312A1, size: 0x8, addend: 0x0, symName: __ZN13Device_0x871612getModelNameEv, symObjAddr: 0x9740, symBinAddr: 0x9FA0, symSize: 0x10 }
  - { offsetInCU: 0x12972, offset: 0x312D0, size: 0x8, addend: 0x0, symName: __ZN13Device_0x87166getLdnEv, symObjAddr: 0x9750, symBinAddr: 0x9FB0, symSize: 0x10 }
  - { offsetInCU: 0x129A1, offset: 0x312FF, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8716D1Ev, symObjAddr: 0x9760, symBinAddr: 0x9FC0, symSize: 0x10 }
  - { offsetInCU: 0x129D7, offset: 0x31335, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8716D0Ev, symObjAddr: 0x9770, symBinAddr: 0x9FD0, symSize: 0x10 }
  - { offsetInCU: 0x12A10, offset: 0x3136E, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_918getTachometerCountEv, symObjAddr: 0x9780, symBinAddr: 0x9FE0, symSize: 0x10 }
  - { offsetInCU: 0x12A3F, offset: 0x3139D, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_917getTachometerNameEh, symObjAddr: 0x9790, symBinAddr: 0x9FF0, symSize: 0x30 }
  - { offsetInCU: 0x12A86, offset: 0x313E4, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_916updateTachometerEh, symObjAddr: 0x97C0, symBinAddr: 0xA020, symSize: 0xF0 }
  - { offsetInCU: 0x13084, offset: 0x319E2, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_915getVoltageCountEv, symObjAddr: 0x98B0, symBinAddr: 0xA110, symSize: 0x10 }
  - { offsetInCU: 0x130B3, offset: 0x31A11, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_914getVoltageNameEh, symObjAddr: 0x98C0, symBinAddr: 0xA120, symSize: 0x30 }
  - { offsetInCU: 0x130FA, offset: 0x31A58, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_913updateVoltageEh, symObjAddr: 0x98F0, symBinAddr: 0xA150, symSize: 0x30 }
  - { offsetInCU: 0x13102, offset: 0x31A60, size: 0x8, addend: 0x0, symName: __ZN13Device_0x898712getModelNameEv, symObjAddr: 0x9920, symBinAddr: 0xA180, symSize: 0x10 }
  - { offsetInCU: 0x1322B, offset: 0x31B89, size: 0x8, addend: 0x0, symName: __ZN13Device_0x898712getModelNameEv, symObjAddr: 0x9920, symBinAddr: 0xA180, symSize: 0x10 }
  - { offsetInCU: 0x1325A, offset: 0x31BB8, size: 0x8, addend: 0x0, symName: __ZN13Device_0x89876getLdnEv, symObjAddr: 0x9930, symBinAddr: 0xA190, symSize: 0x10 }
  - { offsetInCU: 0x13289, offset: 0x31BE7, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8987D1Ev, symObjAddr: 0x9940, symBinAddr: 0xA1A0, symSize: 0x10 }
  - { offsetInCU: 0x132BF, offset: 0x31C1D, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8987D0Ev, symObjAddr: 0x9950, symBinAddr: 0xA1B0, symSize: 0x10 }
  - { offsetInCU: 0x132F8, offset: 0x31C56, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_618getTachometerCountEv, symObjAddr: 0x9960, symBinAddr: 0xA1C0, symSize: 0x10 }
  - { offsetInCU: 0x13327, offset: 0x31C85, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_617getTachometerNameEh, symObjAddr: 0x9970, symBinAddr: 0xA1D0, symSize: 0x30 }
  - { offsetInCU: 0x1336E, offset: 0x31CCC, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_616updateTachometerEh, symObjAddr: 0x99A0, symBinAddr: 0xA200, symSize: 0x80 }
  - { offsetInCU: 0x13550, offset: 0x31EAE, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_615getVoltageCountEv, symObjAddr: 0x9A20, symBinAddr: 0xA280, symSize: 0x10 }
  - { offsetInCU: 0x1357F, offset: 0x31EDD, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_614getVoltageNameEh, symObjAddr: 0x9A30, symBinAddr: 0xA290, symSize: 0x30 }
  - { offsetInCU: 0x135C6, offset: 0x31F24, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_613updateVoltageEh, symObjAddr: 0x9A60, symBinAddr: 0xA2C0, symSize: 0x30 }
  - { offsetInCU: 0x135CE, offset: 0x31F2C, size: 0x8, addend: 0x0, symName: __ZN13Device_0x861312getModelNameEv, symObjAddr: 0x9A90, symBinAddr: 0xA2F0, symSize: 0x10 }
  - { offsetInCU: 0x136F7, offset: 0x32055, size: 0x8, addend: 0x0, symName: __ZN13Device_0x861312getModelNameEv, symObjAddr: 0x9A90, symBinAddr: 0xA2F0, symSize: 0x10 }
  - { offsetInCU: 0x13726, offset: 0x32084, size: 0x8, addend: 0x0, symName: __ZN13Device_0x86136getLdnEv, symObjAddr: 0x9AA0, symBinAddr: 0xA300, symSize: 0x10 }
  - { offsetInCU: 0x13755, offset: 0x320B3, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8613D1Ev, symObjAddr: 0x9AB0, symBinAddr: 0xA310, symSize: 0x10 }
  - { offsetInCU: 0x1378B, offset: 0x320E9, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8613D0Ev, symObjAddr: 0x9AC0, symBinAddr: 0xA320, symSize: 0x10 }
  - { offsetInCU: 0x137C4, offset: 0x32122, size: 0x8, addend: 0x0, symName: __ZN13Device_0x866512getModelNameEv, symObjAddr: 0x9AD0, symBinAddr: 0xA330, symSize: 0x10 }
  - { offsetInCU: 0x137F3, offset: 0x32151, size: 0x8, addend: 0x0, symName: __ZN13Device_0x86656getLdnEv, symObjAddr: 0x9AE0, symBinAddr: 0xA340, symSize: 0x10 }
  - { offsetInCU: 0x13822, offset: 0x32180, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8665D1Ev, symObjAddr: 0x9AF0, symBinAddr: 0xA350, symSize: 0x10 }
  - { offsetInCU: 0x13858, offset: 0x321B6, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8665D0Ev, symObjAddr: 0x9B00, symBinAddr: 0xA360, symSize: 0x10 }
  - { offsetInCU: 0x13891, offset: 0x321EF, size: 0x8, addend: 0x0, symName: __ZN13Device_0x879512getModelNameEv, symObjAddr: 0x9B10, symBinAddr: 0xA370, symSize: 0x10 }
  - { offsetInCU: 0x138C0, offset: 0x3221E, size: 0x8, addend: 0x0, symName: __ZN13Device_0x87956getLdnEv, symObjAddr: 0x9B20, symBinAddr: 0xA380, symSize: 0x10 }
  - { offsetInCU: 0x138EF, offset: 0x3224D, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8795D1Ev, symObjAddr: 0x9B30, symBinAddr: 0xA390, symSize: 0x10 }
  - { offsetInCU: 0x13925, offset: 0x32283, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8795D0Ev, symObjAddr: 0x9B40, symBinAddr: 0xA3A0, symSize: 0x10 }
  - { offsetInCU: 0x1395E, offset: 0x322BC, size: 0x8, addend: 0x0, symName: __ZN13Device_0x868912getModelNameEv, symObjAddr: 0x9B50, symBinAddr: 0xA3B0, symSize: 0x10 }
  - { offsetInCU: 0x1398D, offset: 0x322EB, size: 0x8, addend: 0x0, symName: __ZN13Device_0x86896getLdnEv, symObjAddr: 0x9B60, symBinAddr: 0xA3C0, symSize: 0x10 }
  - { offsetInCU: 0x139BC, offset: 0x3231A, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8689D1Ev, symObjAddr: 0x9B70, symBinAddr: 0xA3D0, symSize: 0x10 }
  - { offsetInCU: 0x139F2, offset: 0x32350, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8689D0Ev, symObjAddr: 0x9B80, symBinAddr: 0xA3E0, symSize: 0x10 }
  - { offsetInCU: 0x13A2B, offset: 0x32389, size: 0x8, addend: 0x0, symName: __ZN13Device_0x868812getModelNameEv, symObjAddr: 0x9B90, symBinAddr: 0xA3F0, symSize: 0x10 }
  - { offsetInCU: 0x13A5A, offset: 0x323B8, size: 0x8, addend: 0x0, symName: __ZN13Device_0x86886getLdnEv, symObjAddr: 0x9BA0, symBinAddr: 0xA400, symSize: 0x10 }
  - { offsetInCU: 0x13A89, offset: 0x323E7, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8688D1Ev, symObjAddr: 0x9BB0, symBinAddr: 0xA410, symSize: 0x10 }
  - { offsetInCU: 0x13ABF, offset: 0x3241D, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8688D0Ev, symObjAddr: 0x9BC0, symBinAddr: 0xA420, symSize: 0x10 }
  - { offsetInCU: 0x13AF8, offset: 0x32456, size: 0x8, addend: 0x0, symName: __ZN13Device_0x879212getModelNameEv, symObjAddr: 0x9BD0, symBinAddr: 0xA430, symSize: 0x10 }
  - { offsetInCU: 0x13B27, offset: 0x32485, size: 0x8, addend: 0x0, symName: __ZN13Device_0x87926getLdnEv, symObjAddr: 0x9BE0, symBinAddr: 0xA440, symSize: 0x10 }
  - { offsetInCU: 0x13B56, offset: 0x324B4, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8792D1Ev, symObjAddr: 0x9BF0, symBinAddr: 0xA450, symSize: 0x10 }
  - { offsetInCU: 0x13B8C, offset: 0x324EA, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8792D0Ev, symObjAddr: 0x9C00, symBinAddr: 0xA460, symSize: 0x10 }
  - { offsetInCU: 0x13BC5, offset: 0x32523, size: 0x8, addend: 0x0, symName: __ZN13Device_0x877212getModelNameEv, symObjAddr: 0x9C10, symBinAddr: 0xA470, symSize: 0x10 }
  - { offsetInCU: 0x13BF4, offset: 0x32552, size: 0x8, addend: 0x0, symName: __ZN13Device_0x87726getLdnEv, symObjAddr: 0x9C20, symBinAddr: 0xA480, symSize: 0x10 }
  - { offsetInCU: 0x13C23, offset: 0x32581, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8772D1Ev, symObjAddr: 0x9C30, symBinAddr: 0xA490, symSize: 0x10 }
  - { offsetInCU: 0x13C59, offset: 0x325B7, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8772D0Ev, symObjAddr: 0x9C40, symBinAddr: 0xA4A0, symSize: 0x10 }
  - { offsetInCU: 0x13C92, offset: 0x325F0, size: 0x8, addend: 0x0, symName: __ZN13Device_0x877112getModelNameEv, symObjAddr: 0x9C50, symBinAddr: 0xA4B0, symSize: 0x10 }
  - { offsetInCU: 0x13CC1, offset: 0x3261F, size: 0x8, addend: 0x0, symName: __ZN13Device_0x87716getLdnEv, symObjAddr: 0x9C60, symBinAddr: 0xA4C0, symSize: 0x10 }
  - { offsetInCU: 0x13CF0, offset: 0x3264E, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8771D1Ev, symObjAddr: 0x9C70, symBinAddr: 0xA4D0, symSize: 0x10 }
  - { offsetInCU: 0x13D26, offset: 0x32684, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8771D0Ev, symObjAddr: 0x9C80, symBinAddr: 0xA4E0, symSize: 0x10 }
  - { offsetInCU: 0x13D5F, offset: 0x326BD, size: 0x8, addend: 0x0, symName: __ZN13Device_0x875212getModelNameEv, symObjAddr: 0x9C90, symBinAddr: 0xA4F0, symSize: 0x10 }
  - { offsetInCU: 0x13D8E, offset: 0x326EC, size: 0x8, addend: 0x0, symName: __ZN13Device_0x87526getLdnEv, symObjAddr: 0x9CA0, symBinAddr: 0xA500, symSize: 0x10 }
  - { offsetInCU: 0x13DBD, offset: 0x3271B, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8752D1Ev, symObjAddr: 0x9CB0, symBinAddr: 0xA510, symSize: 0x10 }
  - { offsetInCU: 0x13DF3, offset: 0x32751, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8752D0Ev, symObjAddr: 0x9CC0, symBinAddr: 0xA520, symSize: 0x10 }
  - { offsetInCU: 0x13E2C, offset: 0x3278A, size: 0x8, addend: 0x0, symName: __ZN13Device_0x872812getModelNameEv, symObjAddr: 0x9CD0, symBinAddr: 0xA530, symSize: 0x10 }
  - { offsetInCU: 0x13E5B, offset: 0x327B9, size: 0x8, addend: 0x0, symName: __ZN13Device_0x87286getLdnEv, symObjAddr: 0x9CE0, symBinAddr: 0xA540, symSize: 0x10 }
  - { offsetInCU: 0x13E8A, offset: 0x327E8, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8728D1Ev, symObjAddr: 0x9CF0, symBinAddr: 0xA550, symSize: 0x10 }
  - { offsetInCU: 0x13EC0, offset: 0x3281E, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8728D0Ev, symObjAddr: 0x9D00, symBinAddr: 0xA560, symSize: 0x10 }
  - { offsetInCU: 0x13EF9, offset: 0x32857, size: 0x8, addend: 0x0, symName: __ZN13Device_0x868612getModelNameEv, symObjAddr: 0x9D10, symBinAddr: 0xA570, symSize: 0x10 }
  - { offsetInCU: 0x13F28, offset: 0x32886, size: 0x8, addend: 0x0, symName: __ZN13Device_0x86866getLdnEv, symObjAddr: 0x9D20, symBinAddr: 0xA580, symSize: 0x10 }
  - { offsetInCU: 0x13F57, offset: 0x328B5, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8686D1Ev, symObjAddr: 0x9D30, symBinAddr: 0xA590, symSize: 0x10 }
  - { offsetInCU: 0x13F8D, offset: 0x328EB, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8686D0Ev, symObjAddr: 0x9D40, symBinAddr: 0xA5A0, symSize: 0x10 }
  - { offsetInCU: 0x13FC6, offset: 0x32924, size: 0x8, addend: 0x0, symName: __ZN13Device_0x863812getModelNameEv, symObjAddr: 0x9D50, symBinAddr: 0xA5B0, symSize: 0x10 }
  - { offsetInCU: 0x13FF5, offset: 0x32953, size: 0x8, addend: 0x0, symName: __ZN13Device_0x86386getLdnEv, symObjAddr: 0x9D60, symBinAddr: 0xA5C0, symSize: 0x10 }
  - { offsetInCU: 0x14024, offset: 0x32982, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8638D1Ev, symObjAddr: 0x9D70, symBinAddr: 0xA5D0, symSize: 0x10 }
  - { offsetInCU: 0x1405A, offset: 0x329B8, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8638D0Ev, symObjAddr: 0x9D80, symBinAddr: 0xA5E0, symSize: 0x10 }
  - { offsetInCU: 0x14093, offset: 0x329F1, size: 0x8, addend: 0x0, symName: __ZN13Device_0x862812getModelNameEv, symObjAddr: 0x9D90, symBinAddr: 0xA5F0, symSize: 0x10 }
  - { offsetInCU: 0x140C2, offset: 0x32A20, size: 0x8, addend: 0x0, symName: __ZN13Device_0x86286getLdnEv, symObjAddr: 0x9DA0, symBinAddr: 0xA600, symSize: 0x10 }
  - { offsetInCU: 0x140F1, offset: 0x32A4F, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8628D1Ev, symObjAddr: 0x9DB0, symBinAddr: 0xA610, symSize: 0x10 }
  - { offsetInCU: 0x14127, offset: 0x32A85, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8628D0Ev, symObjAddr: 0x9DC0, symBinAddr: 0xA620, symSize: 0x10 }
  - { offsetInCU: 0x14160, offset: 0x32ABE, size: 0x8, addend: 0x0, symName: __ZN13Device_0x862012getModelNameEv, symObjAddr: 0x9DD0, symBinAddr: 0xA630, symSize: 0x10 }
  - { offsetInCU: 0x1418F, offset: 0x32AED, size: 0x8, addend: 0x0, symName: __ZN13Device_0x86206getLdnEv, symObjAddr: 0x9DE0, symBinAddr: 0xA640, symSize: 0x10 }
  - { offsetInCU: 0x141BE, offset: 0x32B1C, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8620D1Ev, symObjAddr: 0x9DF0, symBinAddr: 0xA650, symSize: 0x10 }
  - { offsetInCU: 0x141F4, offset: 0x32B52, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8620D0Ev, symObjAddr: 0x9E00, symBinAddr: 0xA660, symSize: 0x10 }
  - { offsetInCU: 0x1422D, offset: 0x32B8B, size: 0x8, addend: 0x0, symName: __ZN13Device_0x872612getModelNameEv, symObjAddr: 0x9E10, symBinAddr: 0xA670, symSize: 0x10 }
  - { offsetInCU: 0x1425C, offset: 0x32BBA, size: 0x8, addend: 0x0, symName: __ZN13Device_0x87266getLdnEv, symObjAddr: 0x9E20, symBinAddr: 0xA680, symSize: 0x10 }
  - { offsetInCU: 0x1428B, offset: 0x32BE9, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8726D1Ev, symObjAddr: 0x9E30, symBinAddr: 0xA690, symSize: 0x10 }
  - { offsetInCU: 0x142C1, offset: 0x32C1F, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8726D0Ev, symObjAddr: 0x9E40, symBinAddr: 0xA6A0, symSize: 0x10 }
  - { offsetInCU: 0x142FA, offset: 0x32C58, size: 0x8, addend: 0x0, symName: __ZN13Device_0x872112getModelNameEv, symObjAddr: 0x9E50, symBinAddr: 0xA6B0, symSize: 0x10 }
  - { offsetInCU: 0x14329, offset: 0x32C87, size: 0x8, addend: 0x0, symName: __ZN13Device_0x87216getLdnEv, symObjAddr: 0x9E60, symBinAddr: 0xA6C0, symSize: 0x10 }
  - { offsetInCU: 0x14358, offset: 0x32CB6, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8721D1Ev, symObjAddr: 0x9E70, symBinAddr: 0xA6D0, symSize: 0x10 }
  - { offsetInCU: 0x1438E, offset: 0x32CEC, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8721D0Ev, symObjAddr: 0x9E80, symBinAddr: 0xA6E0, symSize: 0x10 }
  - { offsetInCU: 0x143C7, offset: 0x32D25, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_518getTachometerCountEv, symObjAddr: 0x9E90, symBinAddr: 0xA6F0, symSize: 0x10 }
  - { offsetInCU: 0x143F6, offset: 0x32D54, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_517getTachometerNameEh, symObjAddr: 0x9EA0, symBinAddr: 0xA700, symSize: 0x30 }
  - { offsetInCU: 0x1443D, offset: 0x32D9B, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_516updateTachometerEh, symObjAddr: 0x9ED0, symBinAddr: 0xA730, symSize: 0x90 }
  - { offsetInCU: 0x14645, offset: 0x32FA3, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_515getVoltageCountEv, symObjAddr: 0x9F60, symBinAddr: 0xA7C0, symSize: 0x10 }
  - { offsetInCU: 0x14674, offset: 0x32FD2, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_514getVoltageNameEh, symObjAddr: 0x9F70, symBinAddr: 0xA7D0, symSize: 0x30 }
  - { offsetInCU: 0x146BB, offset: 0x33019, size: 0x8, addend: 0x0, symName: __ZN20GeneratedITEDevice_513updateVoltageEh, symObjAddr: 0x9FA0, symBinAddr: 0xA800, symSize: 0x30 }
  - { offsetInCU: 0x146C3, offset: 0x33021, size: 0x8, addend: 0x0, symName: __ZN13Device_0x871212getModelNameEv, symObjAddr: 0x9FD0, symBinAddr: 0xA830, symSize: 0x10 }
  - { offsetInCU: 0x147EC, offset: 0x3314A, size: 0x8, addend: 0x0, symName: __ZN13Device_0x871212getModelNameEv, symObjAddr: 0x9FD0, symBinAddr: 0xA830, symSize: 0x10 }
  - { offsetInCU: 0x1481B, offset: 0x33179, size: 0x8, addend: 0x0, symName: __ZN13Device_0x87126getLdnEv, symObjAddr: 0x9FE0, symBinAddr: 0xA840, symSize: 0x10 }
  - { offsetInCU: 0x1484A, offset: 0x331A8, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8712D1Ev, symObjAddr: 0x9FF0, symBinAddr: 0xA850, symSize: 0x10 }
  - { offsetInCU: 0x14880, offset: 0x331DE, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8712D0Ev, symObjAddr: 0xA000, symBinAddr: 0xA860, symSize: 0x10 }
  - { offsetInCU: 0x148B9, offset: 0x33217, size: 0x8, addend: 0x0, symName: __ZN13Device_0x870512getModelNameEv, symObjAddr: 0xA010, symBinAddr: 0xA870, symSize: 0x10 }
  - { offsetInCU: 0x148E8, offset: 0x33246, size: 0x8, addend: 0x0, symName: __ZN13Device_0x87056getLdnEv, symObjAddr: 0xA020, symBinAddr: 0xA880, symSize: 0x10 }
  - { offsetInCU: 0x14917, offset: 0x33275, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8705D1Ev, symObjAddr: 0xA030, symBinAddr: 0xA890, symSize: 0x10 }
  - { offsetInCU: 0x1494D, offset: 0x332AB, size: 0x8, addend: 0x0, symName: __ZN13Device_0x8705D0Ev, symObjAddr: 0xA040, symBinAddr: 0xA8A0, symSize: 0x10 }
  - { offsetInCU: 0x149A7, offset: 0x33305, size: 0x8, addend: 0x0, symName: __ZN2EC11ECDeviceNUCC2Ev, symObjAddr: 0xA050, symBinAddr: 0xA8B0, symSize: 0x1D0 }
  - { offsetInCU: 0x14E1A, offset: 0x33778, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V112createDeviceEPKc, symObjAddr: 0xA220, symBinAddr: 0xAA80, symSize: 0xE0 }
  - { offsetInCU: 0x152B2, offset: 0x33C10, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V312createDeviceEPKc, symObjAddr: 0xA300, symBinAddr: 0xAB60, symSize: 0xE0 }
  - { offsetInCU: 0x1574A, offset: 0x340A8, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V712createDeviceEPKc, symObjAddr: 0xA3E0, symBinAddr: 0xAC40, symSize: 0xE0 }
  - { offsetInCU: 0x15BE2, offset: 0x34540, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V512createDeviceEPKc, symObjAddr: 0xA4C0, symBinAddr: 0xAD20, symSize: 0xD0 }
  - { offsetInCU: 0x15C8E, offset: 0x345EC, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2618getTachometerCountEv, symObjAddr: 0xA590, symBinAddr: 0xADF0, symSize: 0x10 }
  - { offsetInCU: 0x15CBD, offset: 0x3461B, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2617getTachometerNameEh, symObjAddr: 0xA5A0, symBinAddr: 0xAE00, symSize: 0x30 }
  - { offsetInCU: 0x15D31, offset: 0x3468F, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2616updateTachometerEh, symObjAddr: 0xA5D0, symBinAddr: 0xAE30, symSize: 0x60 }
  - { offsetInCU: 0x15DE2, offset: 0x34740, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2615getVoltageCountEv, symObjAddr: 0xA630, symBinAddr: 0xAE90, symSize: 0x10 }
  - { offsetInCU: 0x15E11, offset: 0x3476F, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2614getVoltageNameEh, symObjAddr: 0xA640, symBinAddr: 0xAEA0, symSize: 0x30 }
  - { offsetInCU: 0x15E58, offset: 0x347B6, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2613updateVoltageEh, symObjAddr: 0xA670, symBinAddr: 0xAED0, symSize: 0x90 }
  - { offsetInCU: 0x15F3D, offset: 0x3489B, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2619getTemperatureCountEv, symObjAddr: 0xA700, symBinAddr: 0xAF60, symSize: 0x10 }
  - { offsetInCU: 0x15F6C, offset: 0x348CA, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2618getTemperatureNameEh, symObjAddr: 0xA710, symBinAddr: 0xAF70, symSize: 0x30 }
  - { offsetInCU: 0x15FB3, offset: 0x34911, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2617updateTemperatureEh, symObjAddr: 0xA740, symBinAddr: 0xAFA0, symSize: 0x80 }
  - { offsetInCU: 0x16098, offset: 0x349F6, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V512getModelNameEv, symObjAddr: 0xA7C0, symBinAddr: 0xB020, symSize: 0x10 }
  - { offsetInCU: 0x160C7, offset: 0x34A25, size: 0x8, addend: 0x0, symName: __ZN13SuperIODevice6getLdnEv.273, symObjAddr: 0xA7D0, symBinAddr: 0xB030, symSize: 0x10 }
  - { offsetInCU: 0x160F6, offset: 0x34A54, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V5D1Ev, symObjAddr: 0xA7E0, symBinAddr: 0xB040, symSize: 0x10 }
  - { offsetInCU: 0x1612C, offset: 0x34A8A, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V5D0Ev, symObjAddr: 0xA7F0, symBinAddr: 0xB050, symSize: 0x10 }
  - { offsetInCU: 0x162F4, offset: 0x34C52, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2616setupVoltageKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xA800, symBinAddr: 0xB060, symSize: 0x100 }
  - { offsetInCU: 0x165D0, offset: 0x34F2E, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2620setupTemperatureKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xA900, symBinAddr: 0xB160, symSize: 0x190 }
  - { offsetInCU: 0x16923, offset: 0x35281, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice14tachometerReadEh.274, symObjAddr: 0xAA90, symBinAddr: 0xB2F0, symSize: 0x10 }
  - { offsetInCU: 0x16961, offset: 0x352BF, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice11voltageReadEh.275, symObjAddr: 0xAAA0, symBinAddr: 0xB300, symSize: 0x10 }
  - { offsetInCU: 0x1699F, offset: 0x352FD, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice15temperatureReadEh.276, symObjAddr: 0xAAB0, symBinAddr: 0xB310, symSize: 0x10 }
  - { offsetInCU: 0x169DD, offset: 0x3533B, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice14setupExtraKeysERN13VirtualSMCAPI6PluginE.277, symObjAddr: 0xAAC0, symBinAddr: 0xB320, symSize: 0x30 }
  - { offsetInCU: 0x16A24, offset: 0x35382, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValueC2Ev.278, symObjAddr: 0xAAF0, symBinAddr: 0xB350, symSize: 0xB0 }
  - { offsetInCU: 0x16A5C, offset: 0x353BA, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2518getTachometerCountEv, symObjAddr: 0xABA0, symBinAddr: 0xB400, symSize: 0x10 }
  - { offsetInCU: 0x16A8B, offset: 0x353E9, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2517getTachometerNameEh, symObjAddr: 0xABB0, symBinAddr: 0xB410, symSize: 0x30 }
  - { offsetInCU: 0x16AD2, offset: 0x35430, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2516updateTachometerEh, symObjAddr: 0xABE0, symBinAddr: 0xB440, symSize: 0x40 }
  - { offsetInCU: 0x16B4E, offset: 0x354AC, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2515getVoltageCountEv, symObjAddr: 0xAC20, symBinAddr: 0xB480, symSize: 0x10 }
  - { offsetInCU: 0x16B7D, offset: 0x354DB, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2514getVoltageNameEh, symObjAddr: 0xAC30, symBinAddr: 0xB490, symSize: 0x30 }
  - { offsetInCU: 0x16BC4, offset: 0x35522, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2513updateVoltageEh, symObjAddr: 0xAC60, symBinAddr: 0xB4C0, symSize: 0xE0 }
  - { offsetInCU: 0x16D13, offset: 0x35671, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2519getTemperatureCountEv, symObjAddr: 0xAD40, symBinAddr: 0xB5A0, symSize: 0x10 }
  - { offsetInCU: 0x16D42, offset: 0x356A0, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2518getTemperatureNameEh, symObjAddr: 0xAD50, symBinAddr: 0xB5B0, symSize: 0x30 }
  - { offsetInCU: 0x16D89, offset: 0x356E7, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2517updateTemperatureEh, symObjAddr: 0xAD80, symBinAddr: 0xB5E0, symSize: 0x80 }
  - { offsetInCU: 0x16E6E, offset: 0x357CC, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V712getModelNameEv, symObjAddr: 0xAE00, symBinAddr: 0xB660, symSize: 0x10 }
  - { offsetInCU: 0x16E9D, offset: 0x357FB, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V7D1Ev, symObjAddr: 0xAE10, symBinAddr: 0xB670, symSize: 0x10 }
  - { offsetInCU: 0x16ED3, offset: 0x35831, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V7D0Ev, symObjAddr: 0xAE20, symBinAddr: 0xB680, symSize: 0x10 }
  - { offsetInCU: 0x16F0C, offset: 0x3586A, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2516setupVoltageKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xAE30, symBinAddr: 0xB690, symSize: 0x1D0 }
  - { offsetInCU: 0x17327, offset: 0x35C85, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2520setupTemperatureKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xB000, symBinAddr: 0xB860, symSize: 0xA0 }
  - { offsetInCU: 0x17472, offset: 0x35DD0, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2218getTachometerCountEv, symObjAddr: 0xB0A0, symBinAddr: 0xB900, symSize: 0x10 }
  - { offsetInCU: 0x174A1, offset: 0x35DFF, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2217getTachometerNameEh, symObjAddr: 0xB0B0, symBinAddr: 0xB910, symSize: 0x30 }
  - { offsetInCU: 0x174E8, offset: 0x35E46, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2216updateTachometerEh, symObjAddr: 0xB0E0, symBinAddr: 0xB940, symSize: 0x40 }
  - { offsetInCU: 0x17564, offset: 0x35EC2, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2215getVoltageCountEv, symObjAddr: 0xB120, symBinAddr: 0xB980, symSize: 0x10 }
  - { offsetInCU: 0x17593, offset: 0x35EF1, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2214getVoltageNameEh, symObjAddr: 0xB130, symBinAddr: 0xB990, symSize: 0x30 }
  - { offsetInCU: 0x175DA, offset: 0x35F38, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2213updateVoltageEh, symObjAddr: 0xB160, symBinAddr: 0xB9C0, symSize: 0xC0 }
  - { offsetInCU: 0x176F4, offset: 0x36052, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2219getTemperatureCountEv, symObjAddr: 0xB220, symBinAddr: 0xBA80, symSize: 0x10 }
  - { offsetInCU: 0x17723, offset: 0x36081, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2218getTemperatureNameEh, symObjAddr: 0xB230, symBinAddr: 0xBA90, symSize: 0x30 }
  - { offsetInCU: 0x1776A, offset: 0x360C8, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2217updateTemperatureEh, symObjAddr: 0xB260, symBinAddr: 0xBAC0, symSize: 0xB0 }
  - { offsetInCU: 0x17884, offset: 0x361E2, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V312getModelNameEv, symObjAddr: 0xB310, symBinAddr: 0xBB70, symSize: 0x10 }
  - { offsetInCU: 0x178B3, offset: 0x36211, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V3D1Ev, symObjAddr: 0xB320, symBinAddr: 0xBB80, symSize: 0x10 }
  - { offsetInCU: 0x178E9, offset: 0x36247, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V3D0Ev, symObjAddr: 0xB330, symBinAddr: 0xBB90, symSize: 0x10 }
  - { offsetInCU: 0x17922, offset: 0x36280, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2216setupVoltageKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xB340, symBinAddr: 0xBBA0, symSize: 0x100 }
  - { offsetInCU: 0x17B53, offset: 0x364B1, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2220setupTemperatureKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xB440, symBinAddr: 0xBCA0, symSize: 0x190 }
  - { offsetInCU: 0x17EA6, offset: 0x36804, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2018getTachometerCountEv, symObjAddr: 0xB5D0, symBinAddr: 0xBE30, symSize: 0x10 }
  - { offsetInCU: 0x17ED5, offset: 0x36833, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2017getTachometerNameEh, symObjAddr: 0xB5E0, symBinAddr: 0xBE40, symSize: 0x30 }
  - { offsetInCU: 0x17F1C, offset: 0x3687A, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2016updateTachometerEh, symObjAddr: 0xB610, symBinAddr: 0xBE70, symSize: 0x40 }
  - { offsetInCU: 0x17F98, offset: 0x368F6, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2015getVoltageCountEv, symObjAddr: 0xB650, symBinAddr: 0xBEB0, symSize: 0x10 }
  - { offsetInCU: 0x17FC7, offset: 0x36925, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2014getVoltageNameEh, symObjAddr: 0xB660, symBinAddr: 0xBEC0, symSize: 0x30 }
  - { offsetInCU: 0x1800E, offset: 0x3696C, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2013updateVoltageEh, symObjAddr: 0xB690, symBinAddr: 0xBEF0, symSize: 0xE0 }
  - { offsetInCU: 0x1815D, offset: 0x36ABB, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2019getTemperatureCountEv, symObjAddr: 0xB770, symBinAddr: 0xBFD0, symSize: 0x10 }
  - { offsetInCU: 0x1818C, offset: 0x36AEA, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2018getTemperatureNameEh, symObjAddr: 0xB780, symBinAddr: 0xBFE0, symSize: 0x30 }
  - { offsetInCU: 0x181D3, offset: 0x36B31, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2017updateTemperatureEh, symObjAddr: 0xB7B0, symBinAddr: 0xC010, symSize: 0xB0 }
  - { offsetInCU: 0x182ED, offset: 0x36C4B, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V112getModelNameEv, symObjAddr: 0xB860, symBinAddr: 0xC0C0, symSize: 0x10 }
  - { offsetInCU: 0x1831C, offset: 0x36C7A, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V1D1Ev, symObjAddr: 0xB870, symBinAddr: 0xC0D0, symSize: 0x10 }
  - { offsetInCU: 0x18352, offset: 0x36CB0, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V1D0Ev, symObjAddr: 0xB880, symBinAddr: 0xC0E0, symSize: 0x10 }
  - { offsetInCU: 0x1838B, offset: 0x36CE9, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2016setupVoltageKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xB890, symBinAddr: 0xC0F0, symSize: 0x170 }
  - { offsetInCU: 0x186B1, offset: 0x3700F, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_2020setupTemperatureKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xBA00, symBinAddr: 0xC260, symSize: 0x200 }
  - { offsetInCU: 0x18B08, offset: 0x37466, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1918getTachometerCountEv, symObjAddr: 0xBC00, symBinAddr: 0xC460, symSize: 0x10 }
  - { offsetInCU: 0x18B37, offset: 0x37495, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1917getTachometerNameEh, symObjAddr: 0xBC10, symBinAddr: 0xC470, symSize: 0x30 }
  - { offsetInCU: 0x18B7E, offset: 0x374DC, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1916updateTachometerEh, symObjAddr: 0xBC40, symBinAddr: 0xC4A0, symSize: 0x40 }
  - { offsetInCU: 0x18BFA, offset: 0x37558, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1915getVoltageCountEv, symObjAddr: 0xBC80, symBinAddr: 0xC4E0, symSize: 0x10 }
  - { offsetInCU: 0x18C29, offset: 0x37587, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1914getVoltageNameEh, symObjAddr: 0xBC90, symBinAddr: 0xC4F0, symSize: 0x30 }
  - { offsetInCU: 0x18C70, offset: 0x375CE, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1913updateVoltageEh, symObjAddr: 0xBCC0, symBinAddr: 0xC520, symSize: 0x90 }
  - { offsetInCU: 0x18D55, offset: 0x376B3, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1919getTemperatureCountEv, symObjAddr: 0xBD50, symBinAddr: 0xC5B0, symSize: 0x10 }
  - { offsetInCU: 0x18D84, offset: 0x376E2, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1918getTemperatureNameEh, symObjAddr: 0xBD60, symBinAddr: 0xC5C0, symSize: 0x30 }
  - { offsetInCU: 0x18DCB, offset: 0x37729, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1917updateTemperatureEh, symObjAddr: 0xBD90, symBinAddr: 0xC5F0, symSize: 0xD0 }
  - { offsetInCU: 0x18F1A, offset: 0x37878, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V612getModelNameEv, symObjAddr: 0xBE60, symBinAddr: 0xC6C0, symSize: 0x10 }
  - { offsetInCU: 0x18F49, offset: 0x378A7, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V6D1Ev, symObjAddr: 0xBE70, symBinAddr: 0xC6D0, symSize: 0x10 }
  - { offsetInCU: 0x18F7F, offset: 0x378DD, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V6D0Ev, symObjAddr: 0xBE80, symBinAddr: 0xC6E0, symSize: 0x10 }
  - { offsetInCU: 0x18FB8, offset: 0x37916, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1916setupVoltageKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xBE90, symBinAddr: 0xC6F0, symSize: 0x100 }
  - { offsetInCU: 0x191E9, offset: 0x37B47, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1920setupTemperatureKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xBF90, symBinAddr: 0xC7F0, symSize: 0x190 }
  - { offsetInCU: 0x1953C, offset: 0x37E9A, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1618getTachometerCountEv, symObjAddr: 0xC120, symBinAddr: 0xC980, symSize: 0x10 }
  - { offsetInCU: 0x1956B, offset: 0x37EC9, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1617getTachometerNameEh, symObjAddr: 0xC130, symBinAddr: 0xC990, symSize: 0x30 }
  - { offsetInCU: 0x195B2, offset: 0x37F10, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1616updateTachometerEh, symObjAddr: 0xC160, symBinAddr: 0xC9C0, symSize: 0x40 }
  - { offsetInCU: 0x1962E, offset: 0x37F8C, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1615getVoltageCountEv, symObjAddr: 0xC1A0, symBinAddr: 0xCA00, symSize: 0x10 }
  - { offsetInCU: 0x1965D, offset: 0x37FBB, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1614getVoltageNameEh, symObjAddr: 0xC1B0, symBinAddr: 0xCA10, symSize: 0x30 }
  - { offsetInCU: 0x196A4, offset: 0x38002, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1613updateVoltageEh, symObjAddr: 0xC1E0, symBinAddr: 0xCA40, symSize: 0x90 }
  - { offsetInCU: 0x19789, offset: 0x380E7, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1619getTemperatureCountEv, symObjAddr: 0xC270, symBinAddr: 0xCAD0, symSize: 0x10 }
  - { offsetInCU: 0x197B8, offset: 0x38116, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1618getTemperatureNameEh, symObjAddr: 0xC280, symBinAddr: 0xCAE0, symSize: 0x30 }
  - { offsetInCU: 0x197FF, offset: 0x3815D, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1617updateTemperatureEh, symObjAddr: 0xC2B0, symBinAddr: 0xCB10, symSize: 0xD0 }
  - { offsetInCU: 0x1994E, offset: 0x382AC, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V412getModelNameEv, symObjAddr: 0xC380, symBinAddr: 0xCBE0, symSize: 0x10 }
  - { offsetInCU: 0x1997D, offset: 0x382DB, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V4D1Ev, symObjAddr: 0xC390, symBinAddr: 0xCBF0, symSize: 0x10 }
  - { offsetInCU: 0x199B3, offset: 0x38311, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V4D0Ev, symObjAddr: 0xC3A0, symBinAddr: 0xCC00, symSize: 0x10 }
  - { offsetInCU: 0x199EC, offset: 0x3834A, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1616setupVoltageKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xC3B0, symBinAddr: 0xCC10, symSize: 0x100 }
  - { offsetInCU: 0x19C1D, offset: 0x3857B, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1620setupTemperatureKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xC4B0, symBinAddr: 0xCD10, symSize: 0x190 }
  - { offsetInCU: 0x19F70, offset: 0x388CE, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1218getTachometerCountEv, symObjAddr: 0xC640, symBinAddr: 0xCEA0, symSize: 0x10 }
  - { offsetInCU: 0x19F9F, offset: 0x388FD, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1217getTachometerNameEh, symObjAddr: 0xC650, symBinAddr: 0xCEB0, symSize: 0x30 }
  - { offsetInCU: 0x19FE6, offset: 0x38944, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1216updateTachometerEh, symObjAddr: 0xC680, symBinAddr: 0xCEE0, symSize: 0x40 }
  - { offsetInCU: 0x1A062, offset: 0x389C0, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1215getVoltageCountEv, symObjAddr: 0xC6C0, symBinAddr: 0xCF20, symSize: 0x10 }
  - { offsetInCU: 0x1A091, offset: 0x389EF, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1214getVoltageNameEh, symObjAddr: 0xC6D0, symBinAddr: 0xCF30, symSize: 0x30 }
  - { offsetInCU: 0x1A0D8, offset: 0x38A36, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1213updateVoltageEh, symObjAddr: 0xC700, symBinAddr: 0xCF60, symSize: 0xC0 }
  - { offsetInCU: 0x1A1F2, offset: 0x38B50, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1219getTemperatureCountEv, symObjAddr: 0xC7C0, symBinAddr: 0xD020, symSize: 0x10 }
  - { offsetInCU: 0x1A221, offset: 0x38B7F, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1218getTemperatureNameEh, symObjAddr: 0xC7D0, symBinAddr: 0xD030, symSize: 0x30 }
  - { offsetInCU: 0x1A268, offset: 0x38BC6, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1217updateTemperatureEh, symObjAddr: 0xC800, symBinAddr: 0xD060, symSize: 0x60 }
  - { offsetInCU: 0x1A318, offset: 0x38C76, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V212getModelNameEv, symObjAddr: 0xC860, symBinAddr: 0xD0C0, symSize: 0x10 }
  - { offsetInCU: 0x1A347, offset: 0x38CA5, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V2D1Ev, symObjAddr: 0xC870, symBinAddr: 0xD0D0, symSize: 0x10 }
  - { offsetInCU: 0x1A37D, offset: 0x38CDB, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V2D0Ev, symObjAddr: 0xC880, symBinAddr: 0xD0E0, symSize: 0x10 }
  - { offsetInCU: 0x1A3B6, offset: 0x38D14, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1216setupVoltageKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xC890, symBinAddr: 0xD0F0, symSize: 0x170 }
  - { offsetInCU: 0x1A6DC, offset: 0x3903A, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice20setupTemperatureKeysERN13VirtualSMCAPI6PluginE.279, symObjAddr: 0xCA00, symBinAddr: 0xD260, symSize: 0x10 }
  - { offsetInCU: 0x1A71A, offset: 0x39078, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1118getTachometerCountEv, symObjAddr: 0xCA10, symBinAddr: 0xD270, symSize: 0x10 }
  - { offsetInCU: 0x1A749, offset: 0x390A7, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1117getTachometerNameEh, symObjAddr: 0xCA20, symBinAddr: 0xD280, symSize: 0x50 }
  - { offsetInCU: 0x1A7BB, offset: 0x39119, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1116updateTachometerEh, symObjAddr: 0xCA70, symBinAddr: 0xD2D0, symSize: 0xF0 }
  - { offsetInCU: 0x1A940, offset: 0x3929E, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1115getVoltageCountEv, symObjAddr: 0xCB60, symBinAddr: 0xD3C0, symSize: 0x10 }
  - { offsetInCU: 0x1A96F, offset: 0x392CD, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1114getVoltageNameEh, symObjAddr: 0xCB70, symBinAddr: 0xD3D0, symSize: 0x50 }
  - { offsetInCU: 0x1A9E1, offset: 0x3933F, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1113updateVoltageEh, symObjAddr: 0xCBC0, symBinAddr: 0xD420, symSize: 0x100 }
  - { offsetInCU: 0x1AB65, offset: 0x394C3, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1119getTemperatureCountEv, symObjAddr: 0xCCC0, symBinAddr: 0xD520, symSize: 0x10 }
  - { offsetInCU: 0x1AB94, offset: 0x394F2, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1118getTemperatureNameEh, symObjAddr: 0xCCD0, symBinAddr: 0xD530, symSize: 0x50 }
  - { offsetInCU: 0x1AC06, offset: 0x39564, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1117updateTemperatureEh, symObjAddr: 0xCD20, symBinAddr: 0xD580, symSize: 0xE0 }
  - { offsetInCU: 0x1AD55, offset: 0x396B3, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_VA12getModelNameEv, symObjAddr: 0xCE00, symBinAddr: 0xD660, symSize: 0x10 }
  - { offsetInCU: 0x1AD84, offset: 0x396E2, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_VAD1Ev, symObjAddr: 0xCE10, symBinAddr: 0xD670, symSize: 0x10 }
  - { offsetInCU: 0x1ADBA, offset: 0x39718, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_VAD0Ev, symObjAddr: 0xCE20, symBinAddr: 0xD680, symSize: 0x10 }
  - { offsetInCU: 0x1ADF3, offset: 0x39751, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1116setupVoltageKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xCE30, symBinAddr: 0xD690, symSize: 0x3D0 }
  - { offsetInCU: 0x1B584, offset: 0x39EE2, size: 0x8, addend: 0x0, symName: __ZN20GeneratedECDevice_1120setupTemperatureKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xD200, symBinAddr: 0xDA60, symSize: 0x360 }
  - { offsetInCU: 0x1BC29, offset: 0x3A587, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_718getTachometerCountEv, symObjAddr: 0xD560, symBinAddr: 0xDDC0, symSize: 0x10 }
  - { offsetInCU: 0x1BC58, offset: 0x3A5B6, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_717getTachometerNameEh, symObjAddr: 0xD570, symBinAddr: 0xDDD0, symSize: 0x30 }
  - { offsetInCU: 0x1BC9F, offset: 0x3A5FD, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_716updateTachometerEh, symObjAddr: 0xD5A0, symBinAddr: 0xDE00, symSize: 0x40 }
  - { offsetInCU: 0x1BD1B, offset: 0x3A679, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_715getVoltageCountEv, symObjAddr: 0xD5E0, symBinAddr: 0xDE40, symSize: 0x10 }
  - { offsetInCU: 0x1BD4A, offset: 0x3A6A8, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_714getVoltageNameEh, symObjAddr: 0xD5F0, symBinAddr: 0xDE50, symSize: 0x30 }
  - { offsetInCU: 0x1BD91, offset: 0x3A6EF, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_713updateVoltageEh, symObjAddr: 0xD620, symBinAddr: 0xDE80, symSize: 0xC0 }
  - { offsetInCU: 0x1BEAB, offset: 0x3A809, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_719getTemperatureCountEv, symObjAddr: 0xD6E0, symBinAddr: 0xDF40, symSize: 0x10 }
  - { offsetInCU: 0x1BEDA, offset: 0x3A838, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_718getTemperatureNameEh, symObjAddr: 0xD6F0, symBinAddr: 0xDF50, symSize: 0x30 }
  - { offsetInCU: 0x1BF21, offset: 0x3A87F, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_717updateTemperatureEh, symObjAddr: 0xD720, symBinAddr: 0xDF80, symSize: 0xA0 }
  - { offsetInCU: 0x1BFD1, offset: 0x3A92F, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V812getModelNameEv, symObjAddr: 0xD7C0, symBinAddr: 0xE020, symSize: 0x10 }
  - { offsetInCU: 0x1C000, offset: 0x3A95E, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V8D1Ev, symObjAddr: 0xD7D0, symBinAddr: 0xE030, symSize: 0x10 }
  - { offsetInCU: 0x1C036, offset: 0x3A994, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V8D0Ev, symObjAddr: 0xD7E0, symBinAddr: 0xE040, symSize: 0x10 }
  - { offsetInCU: 0x1C06F, offset: 0x3A9CD, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_716setupVoltageKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xD7F0, symBinAddr: 0xE050, symSize: 0x100 }
  - { offsetInCU: 0x1C2A0, offset: 0x3ABFE, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_720setupTemperatureKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xD8F0, symBinAddr: 0xE150, symSize: 0x120 }
  - { offsetInCU: 0x1C4EF, offset: 0x3AE4D, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_418getTachometerCountEv, symObjAddr: 0xDA10, symBinAddr: 0xE270, symSize: 0x10 }
  - { offsetInCU: 0x1C51E, offset: 0x3AE7C, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_417getTachometerNameEh, symObjAddr: 0xDA20, symBinAddr: 0xE280, symSize: 0x30 }
  - { offsetInCU: 0x1C565, offset: 0x3AEC3, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_416updateTachometerEh, symObjAddr: 0xDA50, symBinAddr: 0xE2B0, symSize: 0x40 }
  - { offsetInCU: 0x1C5E1, offset: 0x3AF3F, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_415getVoltageCountEv, symObjAddr: 0xDA90, symBinAddr: 0xE2F0, symSize: 0x10 }
  - { offsetInCU: 0x1C610, offset: 0x3AF6E, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_414getVoltageNameEh, symObjAddr: 0xDAA0, symBinAddr: 0xE300, symSize: 0x30 }
  - { offsetInCU: 0x1C657, offset: 0x3AFB5, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_413updateVoltageEh, symObjAddr: 0xDAD0, symBinAddr: 0xE330, symSize: 0xE0 }
  - { offsetInCU: 0x1C7A6, offset: 0x3B104, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_419getTemperatureCountEv, symObjAddr: 0xDBB0, symBinAddr: 0xE410, symSize: 0x10 }
  - { offsetInCU: 0x1C7D5, offset: 0x3B133, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_418getTemperatureNameEh, symObjAddr: 0xDBC0, symBinAddr: 0xE420, symSize: 0x30 }
  - { offsetInCU: 0x1C81C, offset: 0x3B17A, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_417updateTemperatureEh, symObjAddr: 0xDBF0, symBinAddr: 0xE450, symSize: 0xF0 }
  - { offsetInCU: 0x1C9A0, offset: 0x3B2FE, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_VB12getModelNameEv, symObjAddr: 0xDCE0, symBinAddr: 0xE540, symSize: 0x10 }
  - { offsetInCU: 0x1C9CF, offset: 0x3B32D, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_VBD1Ev, symObjAddr: 0xDCF0, symBinAddr: 0xE550, symSize: 0x10 }
  - { offsetInCU: 0x1CA05, offset: 0x3B363, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_VBD0Ev, symObjAddr: 0xDD00, symBinAddr: 0xE560, symSize: 0x10 }
  - { offsetInCU: 0x1CA3E, offset: 0x3B39C, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_416setupVoltageKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xDD10, symBinAddr: 0xE570, symSize: 0x100 }
  - { offsetInCU: 0x1CC6F, offset: 0x3B5CD, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_420setupTemperatureKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xDE10, symBinAddr: 0xE670, symSize: 0x190 }
  - { offsetInCU: 0x1CFC2, offset: 0x3B920, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_018getTachometerCountEv, symObjAddr: 0xDFA0, symBinAddr: 0xE800, symSize: 0x10 }
  - { offsetInCU: 0x1CFF1, offset: 0x3B94F, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_017getTachometerNameEh, symObjAddr: 0xDFB0, symBinAddr: 0xE810, symSize: 0x50 }
  - { offsetInCU: 0x1D063, offset: 0x3B9C1, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_016updateTachometerEh, symObjAddr: 0xE000, symBinAddr: 0xE860, symSize: 0x80 }
  - { offsetInCU: 0x1D146, offset: 0x3BAA4, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_015getVoltageCountEv, symObjAddr: 0xE080, symBinAddr: 0xE8E0, symSize: 0x10 }
  - { offsetInCU: 0x1D175, offset: 0x3BAD3, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_014getVoltageNameEh, symObjAddr: 0xE090, symBinAddr: 0xE8F0, symSize: 0x50 }
  - { offsetInCU: 0x1D1E7, offset: 0x3BB45, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_013updateVoltageEh, symObjAddr: 0xE0E0, symBinAddr: 0xE940, symSize: 0xE0 }
  - { offsetInCU: 0x1D331, offset: 0x3BC8F, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_019getTemperatureCountEv, symObjAddr: 0xE1C0, symBinAddr: 0xEA20, symSize: 0x10 }
  - { offsetInCU: 0x1D360, offset: 0x3BCBE, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_018getTemperatureNameEh, symObjAddr: 0xE1D0, symBinAddr: 0xEA30, symSize: 0x50 }
  - { offsetInCU: 0x1D3D2, offset: 0x3BD30, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_017updateTemperatureEh, symObjAddr: 0xE220, symBinAddr: 0xEA80, symSize: 0x90 }
  - { offsetInCU: 0x1D4B4, offset: 0x3BE12, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V912getModelNameEv, symObjAddr: 0xE2B0, symBinAddr: 0xEB10, symSize: 0x10 }
  - { offsetInCU: 0x1D4E3, offset: 0x3BE41, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V9D1Ev, symObjAddr: 0xE2C0, symBinAddr: 0xEB20, symSize: 0x10 }
  - { offsetInCU: 0x1D518, offset: 0x3BE76, size: 0x8, addend: 0x0, symName: __ZN18Device_Intel_EC_V9D0Ev, symObjAddr: 0xE2D0, symBinAddr: 0xEB30, symSize: 0x10 }
  - { offsetInCU: 0x1D550, offset: 0x3BEAE, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_016setupVoltageKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xE2E0, symBinAddr: 0xEB40, symSize: 0x330 }
  - { offsetInCU: 0x1DB9B, offset: 0x3C4F9, size: 0x8, addend: 0x0, symName: __ZN19GeneratedECDevice_020setupTemperatureKeysERN13VirtualSMCAPI6PluginE, symObjAddr: 0xE610, symBinAddr: 0xEE70, symSize: 0x210 }
  - { offsetInCU: 0x1DBA3, offset: 0x3C501, size: 0x8, addend: 0x0, symName: __ZN2EC11ECDeviceNUCD1Ev, symObjAddr: 0xE820, symBinAddr: 0xF080, symSize: 0x10 }
  - { offsetInCU: 0x1DFAB, offset: 0x3C909, size: 0x8, addend: 0x0, symName: __ZN2EC11ECDeviceNUCD1Ev, symObjAddr: 0xE820, symBinAddr: 0xF080, symSize: 0x10 }
  - { offsetInCU: 0x1DFE0, offset: 0x3C93E, size: 0x8, addend: 0x0, symName: __ZN2EC11ECDeviceNUCD0Ev, symObjAddr: 0xE830, symBinAddr: 0xF090, symSize: 0x10 }
  - { offsetInCU: 0x1E015, offset: 0x3C973, size: 0x8, addend: 0x0, symName: __ZN2EC8ECDevice16setupVoltageKeysERN13VirtualSMCAPI6PluginE.280, symObjAddr: 0xE840, symBinAddr: 0xF0A0, symSize: 0x6 }
...
