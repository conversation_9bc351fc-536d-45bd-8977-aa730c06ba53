---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/work/VirtualSMC/VirtualSMC/build/Release/SMCDellSensors.kext/Contents/MacOS/SMCDellSensors'
relocations:
  - { offsetInCU: 0x10, offset: 0x10, size: 0x8, addend: 0x0, symName: _dell_smm_lowlevel, symObjAddr: 0x0, symBinAddr: 0x12F0, symSize: 0x65 }
  - { offsetInCU: 0x39, offset: 0x39, size: 0x8, addend: 0x0, symName: _dell_smm_lowlevel, symObjAddr: 0x0, symBinAddr: 0x12F0, symSize: 0x65 }
  - { offsetInCU: 0x42, offset: 0x42, size: 0x8, addend: 0x0, symName: cmdok, symObjAddr: 0x65, symBinAddr: 0x1355, symSize: 0x23 }
  - { offsetInCU: 0x57, offset: 0x57, size: 0x8, addend: 0x0, symName: error, symObjAddr: 0x88, symBinAddr: 0x1378, symSize: 0xB }
  - { offsetInCU: 0x69, offset: 0x69, size: 0x8, addend: 0x0, symName: ok, symObjAddr: 0x93, symBinAddr: 0x1383, symSize: 0x3 }
  - { offsetInCU: 0x80, offset: 0x80, size: 0x8, addend: 0x0, symName: ende, symObjAddr: 0x96, symBinAddr: 0x1386, symSize: 0xD }
  - { offsetInCU: 0x35, offset: 0xBD, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0x4040, symBinAddr: 0x6EA0, symSize: 0x0 }
  - { offsetInCU: 0x206, offset: 0x28E, size: 0x8, addend: 0x0, symName: __realmain, symObjAddr: 0x4108, symBinAddr: 0x6F68, symSize: 0x0 }
  - { offsetInCU: 0x21C, offset: 0x2A4, size: 0x8, addend: 0x0, symName: __antimain, symObjAddr: 0x4110, symBinAddr: 0x6F70, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x2D4, size: 0x8, addend: 0x0, symName: _SMCDellSensors_kern_start, symObjAddr: 0x0, symBinAddr: 0x1400, symSize: 0x230 }
  - { offsetInCU: 0x2F, offset: 0x2DC, size: 0x8, addend: 0x0, symName: '__ZZN11KERNELHOOKS4initEvEN3$_08__invokeEPvR13KernelPatchermym', symObjAddr: 0x240, symBinAddr: 0x1640, symSize: 0x420 }
  - { offsetInCU: 0x45, offset: 0x2F2, size: 0x8, addend: 0x0, symName: _SMCDellSensors_startSuccess, symObjAddr: 0x4FE30, symBinAddr: 0x7020, symSize: 0x0 }
  - { offsetInCU: 0x6C, offset: 0x319, size: 0x8, addend: 0x0, symName: _SMCDellSensors_debugEnabled, symObjAddr: 0x4FE31, symBinAddr: 0x7021, symSize: 0x0 }
  - { offsetInCU: 0x8A, offset: 0x337, size: 0x8, addend: 0x0, symName: _SMCDellSensors_debugPrintDelay, symObjAddr: 0x4FE34, symBinAddr: 0x7024, symSize: 0x0 }
  - { offsetInCU: 0xB4EB, offset: 0xB798, size: 0x8, addend: 0x0, symName: _SMCDellSensors_kern_start, symObjAddr: 0x0, symBinAddr: 0x1400, symSize: 0x230 }
  - { offsetInCU: 0xB66A, offset: 0xB917, size: 0x8, addend: 0x0, symName: _SMCDellSensors_kern_stop, symObjAddr: 0x230, symBinAddr: 0x1630, symSize: 0x10 }
  - { offsetInCU: 0xB672, offset: 0xB91F, size: 0x8, addend: 0x0, symName: '__ZZN11KERNELHOOKS4initEvEN3$_08__invokeEPvR13KernelPatchermym', symObjAddr: 0x240, symBinAddr: 0x1640, symSize: 0x420 }
  - { offsetInCU: 0x27, offset: 0xB992, size: 0x8, addend: 0x0, symName: '__ZZN11KERNELHOOKS4initEvEN3$_08__invokeEPvR13KernelPatchermym', symObjAddr: 0x240, symBinAddr: 0x1640, symSize: 0x420 }
  - { offsetInCU: 0x47, offset: 0xB9B2, size: 0x8, addend: 0x0, symName: __ZN11KERNELHOOKS13active_outputE, symObjAddr: 0x4FED8, symBinAddr: 0x70C8, symSize: 0x0 }
  - { offsetInCU: 0xD665, offset: 0x18FD0, size: 0x8, addend: 0x0, symName: __ZL19callbackKERNELHOOKS, symObjAddr: 0x4FE38, symBinAddr: 0x7028, symSize: 0x0 }
  - { offsetInCU: 0xD68C, offset: 0x18FF7, size: 0x8, addend: 0x0, symName: __ZL8kextList, symObjAddr: 0x4120, symBinAddr: 0x6F80, symSize: 0x0 }
  - { offsetInCU: 0xD6BE, offset: 0x19029, size: 0x8, addend: 0x0, symName: __ZL17kextIOAudioFamily, symObjAddr: 0x4170, symBinAddr: 0x6FD0, symSize: 0x0 }
  - { offsetInCU: 0xD6F0, offset: 0x1905B, size: 0x8, addend: 0x0, symName: __ZL21kextIOBluetoothFamily, symObjAddr: 0x4178, symBinAddr: 0x6FD8, symSize: 0x0 }
  - { offsetInCU: 0xE25D, offset: 0x19BC8, size: 0x8, addend: 0x0, symName: '__ZZN11KERNELHOOKS4initEvEN3$_08__invokeEPvR13KernelPatchermym', symObjAddr: 0x240, symBinAddr: 0x1640, symSize: 0x420 }
  - { offsetInCU: 0xE9BD, offset: 0x1A328, size: 0x8, addend: 0x0, symName: '__ZZN11KERNELHOOKS11processKextER13KernelPatchermymEN3$_18__invokeEP8OSObjectP18IOTimerEventSource', symObjAddr: 0x660, symBinAddr: 0x1A60, symSize: 0x30 }
  - { offsetInCU: 0xEA3F, offset: 0x1A3AA, size: 0x8, addend: 0x0, symName: __ZN11KERNELHOOKS43IOAudioEngineUserClient_performClientOutputEPvjjS0_jj, symObjAddr: 0x690, symBinAddr: 0x1A90, symSize: 0xE0 }
  - { offsetInCU: 0xEB41, offset: 0x1A4AC, size: 0x8, addend: 0x0, symName: __ZN11KERNELHOOKS45IOAudioEngineUserClient_performWatchdogOutputEPvS0_j, symObjAddr: 0x770, symBinAddr: 0x1B70, symSize: 0xC0 }
  - { offsetInCU: 0xEBF4, offset: 0x1A55F, size: 0x8, addend: 0x0, symName: __ZN11KERNELHOOKS42IOAudioEngineUserClient_performClientInputEPvjS0_, symObjAddr: 0x830, symBinAddr: 0x1C30, symSize: 0xC0 }
  - { offsetInCU: 0xECBA, offset: 0x1A625, size: 0x8, addend: 0x0, symName: __ZN11KERNELHOOKS34IOAudioStream_processOutputSamplesEPvS0_jjb, symObjAddr: 0x8F0, symBinAddr: 0x1CF0, symSize: 0xD0 }
  - { offsetInCU: 0xEDA4, offset: 0x1A70F, size: 0x8, addend: 0x0, symName: __ZN11KERNELHOOKS34IOBluetoothDevice_moreIncomingDataEPvS0_j, symObjAddr: 0x9C0, symBinAddr: 0x1DC0, symSize: 0x80 }
  - { offsetInCU: 0xEE25, offset: 0x1A790, size: 0x8, addend: 0x0, symName: __ZN11KERNELHOOKS41IOBluetoothL2CAPChannelUserClient_writeWLEPvS0_tyy, symObjAddr: 0xA40, symBinAddr: 0x1E40, symSize: 0x90 }
  - { offsetInCU: 0xEECE, offset: 0x1A839, size: 0x8, addend: 0x0, symName: __ZN11KERNELHOOKS44IOBluetoothL2CAPChannelUserClient_writeOOLWLEPvytyy, symObjAddr: 0xAD0, symBinAddr: 0x1ED0, symSize: 0x90 }
  - { offsetInCU: 0xEF77, offset: 0x1A8E2, size: 0x8, addend: 0x0, symName: __ZN11KERNELHOOKS58IOBluetoothL2CAPChannelUserClient_WriteAsyncAudioData_TrapEPvS0_tyy, symObjAddr: 0xB60, symBinAddr: 0x1F60, symSize: 0x90 }
  - { offsetInCU: 0xF020, offset: 0x1A98B, size: 0x8, addend: 0x0, symName: __ZN11KERNELHOOKS57IOBluetoothL2CAPChannelUserClient_callBackAfterDataIsSentEPvP9IOServiceS0_iyy, symObjAddr: 0xBF0, symBinAddr: 0x1FF0, symSize: 0xE0 }
  - { offsetInCU: 0x39, offset: 0x1AAC5, size: 0x8, addend: 0x0, symName: _hooks, symObjAddr: 0x4FE40, symBinAddr: 0x7030, symSize: 0x0 }
  - { offsetInCU: 0x5A, offset: 0x1AAE6, size: 0x8, addend: 0x0, symName: __ZL10bootargOff, symObjAddr: 0x4180, symBinAddr: 0x6FE0, symSize: 0x0 }
  - { offsetInCU: 0x78, offset: 0x1AB04, size: 0x8, addend: 0x0, symName: __ZL12bootargDebug, symObjAddr: 0x4188, symBinAddr: 0x6FE8, symSize: 0x0 }
  - { offsetInCU: 0x96, offset: 0x1AB22, size: 0x8, addend: 0x0, symName: __ZL11bootargBeta, symObjAddr: 0x4190, symBinAddr: 0x6FF0, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x1ABCB, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitorD1Ev, symObjAddr: 0xCD0, symBinAddr: 0x20D0, symSize: 0x10 }
  - { offsetInCU: 0x3F, offset: 0x1ABE3, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitor8instanceE, symObjAddr: 0x4FEE0, symBinAddr: 0x70D0, symSize: 0x0 }
  - { offsetInCU: 0xDB7, offset: 0x1B95B, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitor4busyE, symObjAddr: 0x4FEA8, symBinAddr: 0x7098, symSize: 0x0 }
  - { offsetInCU: 0xDCF, offset: 0x1B973, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitor10gMetaClassE, symObjAddr: 0x4FEB0, symBinAddr: 0x70A0, symSize: 0x0 }
  - { offsetInCU: 0xE52, offset: 0x1B9F6, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitorD1Ev, symObjAddr: 0xCD0, symBinAddr: 0x20D0, symSize: 0x10 }
  - { offsetInCU: 0xECD, offset: 0x1BA71, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitorD0Ev, symObjAddr: 0xCE0, symBinAddr: 0x20E0, symSize: 0x30 }
  - { offsetInCU: 0xF59, offset: 0x1BAFD, size: 0x8, addend: 0x0, symName: __ZNK10SMIMonitor12getMetaClassEv, symObjAddr: 0xD10, symBinAddr: 0x2110, symSize: 0x10 }
  - { offsetInCU: 0xFB9, offset: 0x1BB5D, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitor9MetaClassD1Ev, symObjAddr: 0xD20, symBinAddr: 0x2120, symSize: 0x10 }
  - { offsetInCU: 0x1034, offset: 0x1BBD8, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitor9MetaClassD0Ev, symObjAddr: 0xD30, symBinAddr: 0x2130, symSize: 0x10 }
  - { offsetInCU: 0x10E3, offset: 0x1BC87, size: 0x8, addend: 0x0, symName: __ZNK10SMIMonitor9MetaClass5allocEv, symObjAddr: 0xD40, symBinAddr: 0x2140, symSize: 0x30 }
  - { offsetInCU: 0x11C8, offset: 0x1BD6C, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitorC2Ev, symObjAddr: 0xD70, symBinAddr: 0x2170, symSize: 0x1F0 }
  - { offsetInCU: 0x12CE, offset: 0x1BE72, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_SMIMonitor.cpp, symObjAddr: 0xF60, symBinAddr: 0x2360, symSize: 0x40 }
  - { offsetInCU: 0x135A, offset: 0x1BEFE, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0xFA0, symBinAddr: 0x23A0, symSize: 0x20 }
  - { offsetInCU: 0x142A, offset: 0x1BFCE, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitor12i8k_get_tempEib, symObjAddr: 0xFC0, symBinAddr: 0x23C0, symSize: 0x100 }
  - { offsetInCU: 0x152E, offset: 0x1C0D2, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitor26i8k_set_fan_control_manualEv, symObjAddr: 0x10C0, symBinAddr: 0x24C0, symSize: 0xE0 }
  - { offsetInCU: 0x1633, offset: 0x1C1D7, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitor24i8k_set_fan_control_autoEv, symObjAddr: 0x11A0, symBinAddr: 0x25A0, symSize: 0xE0 }
  - { offsetInCU: 0x2683, offset: 0x1D227, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitor23staticUpdateThreadEntryEPvS0_, symObjAddr: 0x1280, symBinAddr: 0x2680, symSize: 0x840 }
  - { offsetInCU: 0x2E97, offset: 0x1DA3B, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitor17updateSensorsLoopEv, symObjAddr: 0x1AC0, symBinAddr: 0x2EC0, symSize: 0x350 }
  - { offsetInCU: 0x32F4, offset: 0x1DE98, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitor22handleSmcUpdatesInIdleEi, symObjAddr: 0x1E10, symBinAddr: 0x3210, symSize: 0x460 }
  - { offsetInCU: 0x36BC, offset: 0x1E260, size: 0x8, addend: 0x0, symName: __ZN10SMIMonitor13postSmcUpdateEjmPKvjb, symObjAddr: 0x2270, symBinAddr: 0x3670, symSize: 0x300 }
  - { offsetInCU: 0x27, offset: 0x1E4B2, size: 0x8, addend: 0x0, symName: __ZN4F0Ac10readAccessEv, symObjAddr: 0x2570, symBinAddr: 0x3970, symSize: 0x40 }
  - { offsetInCU: 0x17A, offset: 0x1E605, size: 0x8, addend: 0x0, symName: __ZN4F0Ac10readAccessEv, symObjAddr: 0x2570, symBinAddr: 0x3970, symSize: 0x40 }
  - { offsetInCU: 0x1E0, offset: 0x1E66B, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValue11writeAccessEv, symObjAddr: 0x25B0, symBinAddr: 0x39B0, symSize: 0x10 }
  - { offsetInCU: 0x210, offset: 0x1E69B, size: 0x8, addend: 0x0, symName: __ZN4F0AcD1Ev, symObjAddr: 0x25C0, symBinAddr: 0x39C0, symSize: 0x10 }
  - { offsetInCU: 0x246, offset: 0x1E6D1, size: 0x8, addend: 0x0, symName: __ZN4F0AcD0Ev, symObjAddr: 0x25D0, symBinAddr: 0x39D0, symSize: 0x10 }
  - { offsetInCU: 0x32D, offset: 0x1E7B8, size: 0x8, addend: 0x0, symName: __ZN4F0Mn10readAccessEv, symObjAddr: 0x25E0, symBinAddr: 0x39E0, symSize: 0x40 }
  - { offsetInCU: 0x393, offset: 0x1E81E, size: 0x8, addend: 0x0, symName: __ZN4F0Mn6updateEPKh, symObjAddr: 0x2620, symBinAddr: 0x3A20, symSize: 0x10 }
  - { offsetInCU: 0x3DD, offset: 0x1E868, size: 0x8, addend: 0x0, symName: __ZN4F0MnD1Ev, symObjAddr: 0x2630, symBinAddr: 0x3A30, symSize: 0x10 }
  - { offsetInCU: 0x413, offset: 0x1E89E, size: 0x8, addend: 0x0, symName: __ZN4F0MnD0Ev, symObjAddr: 0x2640, symBinAddr: 0x3A40, symSize: 0x10 }
  - { offsetInCU: 0x4FA, offset: 0x1E985, size: 0x8, addend: 0x0, symName: __ZN4F0Mx10readAccessEv, symObjAddr: 0x2650, symBinAddr: 0x3A50, symSize: 0x40 }
  - { offsetInCU: 0x560, offset: 0x1E9EB, size: 0x8, addend: 0x0, symName: __ZN4F0Mx6updateEPKh, symObjAddr: 0x2690, symBinAddr: 0x3A90, symSize: 0x10 }
  - { offsetInCU: 0x5AA, offset: 0x1EA35, size: 0x8, addend: 0x0, symName: __ZN4F0MxD1Ev, symObjAddr: 0x26A0, symBinAddr: 0x3AA0, symSize: 0x10 }
  - { offsetInCU: 0x5E0, offset: 0x1EA6B, size: 0x8, addend: 0x0, symName: __ZN4F0MxD0Ev, symObjAddr: 0x26B0, symBinAddr: 0x3AB0, symSize: 0x10 }
  - { offsetInCU: 0x6C7, offset: 0x1EB52, size: 0x8, addend: 0x0, symName: __ZN4F0Md10readAccessEv, symObjAddr: 0x26C0, symBinAddr: 0x3AC0, symSize: 0x30 }
  - { offsetInCU: 0x72B, offset: 0x1EBB6, size: 0x8, addend: 0x0, symName: __ZN4F0Md6updateEPKh, symObjAddr: 0x26F0, symBinAddr: 0x3AF0, symSize: 0x40 }
  - { offsetInCU: 0x792, offset: 0x1EC1D, size: 0x8, addend: 0x0, symName: __ZN4F0MdD1Ev, symObjAddr: 0x2730, symBinAddr: 0x3B30, symSize: 0x10 }
  - { offsetInCU: 0x7C8, offset: 0x1EC53, size: 0x8, addend: 0x0, symName: __ZN4F0MdD0Ev, symObjAddr: 0x2740, symBinAddr: 0x3B40, symSize: 0x10 }
  - { offsetInCU: 0x8AF, offset: 0x1ED3A, size: 0x8, addend: 0x0, symName: __ZN4F0Tg10readAccessEv, symObjAddr: 0x2750, symBinAddr: 0x3B50, symSize: 0x40 }
  - { offsetInCU: 0x915, offset: 0x1EDA0, size: 0x8, addend: 0x0, symName: __ZN4F0Tg6updateEPKh, symObjAddr: 0x2790, symBinAddr: 0x3B90, symSize: 0x40 }
  - { offsetInCU: 0x97C, offset: 0x1EE07, size: 0x8, addend: 0x0, symName: __ZN4F0TgD1Ev, symObjAddr: 0x27D0, symBinAddr: 0x3BD0, symSize: 0x10 }
  - { offsetInCU: 0x9B2, offset: 0x1EE3D, size: 0x8, addend: 0x0, symName: __ZN4F0TgD0Ev, symObjAddr: 0x27E0, symBinAddr: 0x3BE0, symSize: 0x10 }
  - { offsetInCU: 0xACE, offset: 0x1EF59, size: 0x8, addend: 0x0, symName: __ZN4FS__10readAccessEv, symObjAddr: 0x27F0, symBinAddr: 0x3BF0, symSize: 0x20 }
  - { offsetInCU: 0xB42, offset: 0x1EFCD, size: 0x8, addend: 0x0, symName: __ZN4FS__6updateEPKh, symObjAddr: 0x2810, symBinAddr: 0x3C10, symSize: 0x40 }
  - { offsetInCU: 0xBA9, offset: 0x1F034, size: 0x8, addend: 0x0, symName: __ZN4FS__D1Ev, symObjAddr: 0x2850, symBinAddr: 0x3C50, symSize: 0x10 }
  - { offsetInCU: 0xBDF, offset: 0x1F06A, size: 0x8, addend: 0x0, symName: __ZN4FS__D0Ev, symObjAddr: 0x2860, symBinAddr: 0x3C60, symSize: 0x10 }
  - { offsetInCU: 0xC8F, offset: 0x1F11A, size: 0x8, addend: 0x0, symName: __ZN4TG0P10readAccessEv, symObjAddr: 0x2870, symBinAddr: 0x3C70, symSize: 0x40 }
  - { offsetInCU: 0xCF5, offset: 0x1F180, size: 0x8, addend: 0x0, symName: __ZN4TG0PD1Ev, symObjAddr: 0x28B0, symBinAddr: 0x3CB0, symSize: 0x10 }
  - { offsetInCU: 0xD2B, offset: 0x1F1B6, size: 0x8, addend: 0x0, symName: __ZN4TG0PD0Ev, symObjAddr: 0x28C0, symBinAddr: 0x3CC0, symSize: 0x10 }
  - { offsetInCU: 0xDDB, offset: 0x1F266, size: 0x8, addend: 0x0, symName: __ZN4Tm0P10readAccessEv, symObjAddr: 0x28D0, symBinAddr: 0x3CD0, symSize: 0x40 }
  - { offsetInCU: 0xE41, offset: 0x1F2CC, size: 0x8, addend: 0x0, symName: __ZN4Tm0PD1Ev, symObjAddr: 0x2910, symBinAddr: 0x3D10, symSize: 0x10 }
  - { offsetInCU: 0xE77, offset: 0x1F302, size: 0x8, addend: 0x0, symName: __ZN4Tm0PD0Ev, symObjAddr: 0x2920, symBinAddr: 0x3D20, symSize: 0x10 }
  - { offsetInCU: 0xF27, offset: 0x1F3B2, size: 0x8, addend: 0x0, symName: __ZN4TN0P10readAccessEv, symObjAddr: 0x2930, symBinAddr: 0x3D30, symSize: 0x40 }
  - { offsetInCU: 0xF8D, offset: 0x1F418, size: 0x8, addend: 0x0, symName: __ZN4TN0PD1Ev, symObjAddr: 0x2970, symBinAddr: 0x3D70, symSize: 0x10 }
  - { offsetInCU: 0xFC3, offset: 0x1F44E, size: 0x8, addend: 0x0, symName: __ZN4TN0PD0Ev, symObjAddr: 0x2980, symBinAddr: 0x3D80, symSize: 0x10 }
  - { offsetInCU: 0x1073, offset: 0x1F4FE, size: 0x8, addend: 0x0, symName: __ZN4TA0P10readAccessEv, symObjAddr: 0x2990, symBinAddr: 0x3D90, symSize: 0x40 }
  - { offsetInCU: 0x10D9, offset: 0x1F564, size: 0x8, addend: 0x0, symName: __ZN4TA0PD1Ev, symObjAddr: 0x29D0, symBinAddr: 0x3DD0, symSize: 0x10 }
  - { offsetInCU: 0x110F, offset: 0x1F59A, size: 0x8, addend: 0x0, symName: __ZN4TA0PD0Ev, symObjAddr: 0x29E0, symBinAddr: 0x3DE0, symSize: 0x10 }
  - { offsetInCU: 0x11BF, offset: 0x1F64A, size: 0x8, addend: 0x0, symName: __ZN4TW0P10readAccessEv, symObjAddr: 0x29F0, symBinAddr: 0x3DF0, symSize: 0x40 }
  - { offsetInCU: 0x1225, offset: 0x1F6B0, size: 0x8, addend: 0x0, symName: __ZN4TW0PD1Ev, symObjAddr: 0x2A30, symBinAddr: 0x3E30, symSize: 0x10 }
  - { offsetInCU: 0x125B, offset: 0x1F6E6, size: 0x8, addend: 0x0, symName: __ZN4TW0PD0Ev, symObjAddr: 0x2A40, symBinAddr: 0x3E40, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x1F7B0, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensorsD1Ev, symObjAddr: 0x2A50, symBinAddr: 0x3E50, symSize: 0x10 }
  - { offsetInCU: 0x3F, offset: 0x1F7C8, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors10gMetaClassE, symObjAddr: 0x4FEE8, symBinAddr: 0x6FF8, symSize: 0x0 }
  - { offsetInCU: 0xF2E, offset: 0x206B7, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors9metaClassE, symObjAddr: 0x4EB0, symBinAddr: 0x6280, symSize: 0x0 }
  - { offsetInCU: 0xF46, offset: 0x206CF, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors10superClassE, symObjAddr: 0x4EB8, symBinAddr: 0x6288, symSize: 0x0 }
  - { offsetInCU: 0xFAD, offset: 0x20736, size: 0x8, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0x5B90, symBinAddr: 0x5FE0, symSize: 0x0 }
  - { offsetInCU: 0x1069, offset: 0x207F2, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensorsD1Ev, symObjAddr: 0x2A50, symBinAddr: 0x3E50, symSize: 0x10 }
  - { offsetInCU: 0x10E4, offset: 0x2086D, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensorsD0Ev, symObjAddr: 0x2A60, symBinAddr: 0x3E60, symSize: 0x30 }
  - { offsetInCU: 0x1170, offset: 0x208F9, size: 0x8, addend: 0x0, symName: __ZNK14SMCDellSensors12getMetaClassEv, symObjAddr: 0x2A90, symBinAddr: 0x3E90, symSize: 0x10 }
  - { offsetInCU: 0x11AD, offset: 0x20936, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors4initEP12OSDictionary, symObjAddr: 0x2AA0, symBinAddr: 0x3EA0, symSize: 0x180 }
  - { offsetInCU: 0x1F3D, offset: 0x216C6, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors5probeEP9IOServicePi, symObjAddr: 0x2C20, symBinAddr: 0x4020, symSize: 0x970 }
  - { offsetInCU: 0x2F2E, offset: 0x226B7, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors5startEP9IOService, symObjAddr: 0x3590, symBinAddr: 0x4990, symSize: 0x1B0 }
  - { offsetInCU: 0x2FE8, offset: 0x22771, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors4stopEP9IOService, symObjAddr: 0x3740, symBinAddr: 0x4B40, symSize: 0x20 }
  - { offsetInCU: 0x2FF0, offset: 0x22779, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors13setPowerStateEmP9IOService, symObjAddr: 0x3760, symBinAddr: 0x4B60, symSize: 0x10 }
  - { offsetInCU: 0x302A, offset: 0x227B3, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors13setPowerStateEmP9IOService, symObjAddr: 0x3760, symBinAddr: 0x4B60, symSize: 0x10 }
  - { offsetInCU: 0x307C, offset: 0x22805, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors14IOSleepHandlerEPvS0_jP9IOServiceS0_m, symObjAddr: 0x3770, symBinAddr: 0x4B70, symSize: 0x1C0 }
  - { offsetInCU: 0x3249, offset: 0x229D2, size: 0x8, addend: 0x0, symName: '__ZZN14SMCDellSensors5startEP9IOServiceEN3$_08__invokeEP8OSObjectP18IOTimerEventSource', symObjAddr: 0x3930, symBinAddr: 0x4D30, symSize: 0x30 }
  - { offsetInCU: 0x32E6, offset: 0x22A6F, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors23vsmcNotificationHandlerEPvS0_P9IOServiceP10IONotifier, symObjAddr: 0x3960, symBinAddr: 0x4D60, symSize: 0xA0 }
  - { offsetInCU: 0x3385, offset: 0x22B0E, size: 0x8, addend: 0x0, symName: __ZN15VirtualSMCValueC2Ev, symObjAddr: 0x3A00, symBinAddr: 0x4E00, symSize: 0xB0 }
  - { offsetInCU: 0x33E9, offset: 0x22B72, size: 0x8, addend: 0x0, symName: __ZN18VirtualSMCKeyValue7compareEPKvS1_, symObjAddr: 0x3AB0, symBinAddr: 0x4EB0, symSize: 0x30 }
  - { offsetInCU: 0x3492, offset: 0x22C1B, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors9MetaClassD1Ev, symObjAddr: 0x3AE0, symBinAddr: 0x4EE0, symSize: 0x10 }
  - { offsetInCU: 0x350D, offset: 0x22C96, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors9MetaClassD0Ev, symObjAddr: 0x3AF0, symBinAddr: 0x4EF0, symSize: 0x10 }
  - { offsetInCU: 0x366B, offset: 0x22DF4, size: 0x8, addend: 0x0, symName: __ZNK14SMCDellSensors9MetaClass5allocEv, symObjAddr: 0x3B00, symBinAddr: 0x4F00, symSize: 0xF0 }
  - { offsetInCU: 0x37BB, offset: 0x22F44, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_SMCDellSensors.cpp, symObjAddr: 0x3BF0, symBinAddr: 0x4FF0, symSize: 0x40 }
  - { offsetInCU: 0x3847, offset: 0x22FD0, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a.73, symObjAddr: 0x3C30, symBinAddr: 0x5030, symSize: 0x20 }
  - { offsetInCU: 0x38B6, offset: 0x2303F, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors9MetaClassC1Ev, symObjAddr: 0x3C50, symBinAddr: 0x5050, symSize: 0x40 }
  - { offsetInCU: 0x390E, offset: 0x23097, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensorsC2EPK11OSMetaClass, symObjAddr: 0x3C90, symBinAddr: 0x5090, symSize: 0xD0 }
  - { offsetInCU: 0x39F7, offset: 0x23180, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensorsC1EPK11OSMetaClass, symObjAddr: 0x3D60, symBinAddr: 0x5160, symSize: 0xD0 }
  - { offsetInCU: 0x3AF5, offset: 0x2327E, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensorsD2Ev, symObjAddr: 0x3E30, symBinAddr: 0x5230, symSize: 0x10 }
  - { offsetInCU: 0x3B22, offset: 0x232AB, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensors9MetaClassC2Ev, symObjAddr: 0x3E40, symBinAddr: 0x5240, symSize: 0x40 }
  - { offsetInCU: 0x3B50, offset: 0x232D9, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensorsC1Ev, symObjAddr: 0x3E80, symBinAddr: 0x5280, symSize: 0xE0 }
  - { offsetInCU: 0x3C21, offset: 0x233AA, size: 0x8, addend: 0x0, symName: __ZN14SMCDellSensorsC2Ev, symObjAddr: 0x3F60, symBinAddr: 0x5360, symSize: 0xD9 }
...
