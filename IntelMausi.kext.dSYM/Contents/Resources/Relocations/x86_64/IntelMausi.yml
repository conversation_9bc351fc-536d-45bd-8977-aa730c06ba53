---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/work/IntelMausi/IntelMausi/build/Release/IntelMausi.kext/Contents/MacOS/IntelMausi'
relocations:
  - { offsetInCU: 0x36, offset: 0x43738, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0x0, symBinAddr: 0x13880, symSize: 0x0 }
  - { offsetInCU: 0x217, offset: 0x43919, size: 0x8, addend: 0x0, symName: __realmain, symObjAddr: 0xAD8, symBinAddr: 0x13A10, symSize: 0x0 }
  - { offsetInCU: 0x22E, offset: 0x43930, size: 0x8, addend: 0x0, symName: __antimain, symObjAddr: 0xAE0, symBinAddr: 0x13A18, symSize: 0x0 }
  - { offsetInCU: 0x245, offset: 0x43947, size: 0x8, addend: 0x0, symName: __kext_apple_cc, symObjAddr: 0xC4, symBinAddr: 0x13944, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x43978, size: 0x8, addend: 0x0, symName: ___ew32_prepare, symObjAddr: 0x0, symBinAddr: 0x578, symSize: 0x4C }
  - { offsetInCU: 0x28F, offset: 0x43BE0, size: 0x8, addend: 0x0, symName: ___ew32_prepare, symObjAddr: 0x0, symBinAddr: 0x578, symSize: 0x4C }
  - { offsetInCU: 0x297, offset: 0x43BE8, size: 0x8, addend: 0x0, symName: ___ew32, symObjAddr: 0x4C, symBinAddr: 0x5C4, symSize: 0x39 }
  - { offsetInCU: 0x318, offset: 0x43C69, size: 0x8, addend: 0x0, symName: ___ew32, symObjAddr: 0x4C, symBinAddr: 0x5C4, symSize: 0x39 }
  - { offsetInCU: 0x320, offset: 0x43C71, size: 0x8, addend: 0x0, symName: _e1000e_get_hw_control, symObjAddr: 0x85, symBinAddr: 0x5FD, symSize: 0x95 }
  - { offsetInCU: 0x1C1D, offset: 0x4556E, size: 0x8, addend: 0x0, symName: _e1000e_get_hw_control, symObjAddr: 0x85, symBinAddr: 0x5FD, symSize: 0x95 }
  - { offsetInCU: 0x1C25, offset: 0x45576, size: 0x8, addend: 0x0, symName: _e1000e_release_hw_control, symObjAddr: 0x11A, symBinAddr: 0x692, symSize: 0x95 }
  - { offsetInCU: 0x1D37, offset: 0x45688, size: 0x8, addend: 0x0, symName: _e1000e_release_hw_control, symObjAddr: 0x11A, symBinAddr: 0x692, symSize: 0x95 }
  - { offsetInCU: 0x1D3F, offset: 0x45690, size: 0x8, addend: 0x0, symName: _e1000e_power_up_phy, symObjAddr: 0x1AF, symBinAddr: 0x727, symSize: 0x35 }
  - { offsetInCU: 0x1E51, offset: 0x457A2, size: 0x8, addend: 0x0, symName: _e1000e_power_up_phy, symObjAddr: 0x1AF, symBinAddr: 0x727, symSize: 0x35 }
  - { offsetInCU: 0x1E59, offset: 0x457AA, size: 0x8, addend: 0x0, symName: _e1000e_update_phy_stats, symObjAddr: 0x1E4, symBinAddr: 0x75C, symSize: 0x1F5 }
  - { offsetInCU: 0x1E81, offset: 0x457D2, size: 0x8, addend: 0x0, symName: _e1000e_update_phy_stats, symObjAddr: 0x1E4, symBinAddr: 0x75C, symSize: 0x1F5 }
  - { offsetInCU: 0x27, offset: 0x45875, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x952, symSize: 0x32 }
  - { offsetInCU: 0x3F, offset: 0x4588D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10gMetaClassE, symObjAddr: 0x196A8, symBinAddr: 0x13A20, symSize: 0x0 }
  - { offsetInCU: 0x3D7B, offset: 0x495C9, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9metaClassE, symObjAddr: 0x34D0, symBinAddr: 0x12070, symSize: 0x0 }
  - { offsetInCU: 0x3D93, offset: 0x495E1, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10superClassE, symObjAddr: 0x34D8, symBinAddr: 0x12078, symSize: 0x0 }
  - { offsetInCU: 0x3DC1, offset: 0x4960F, size: 0x8, addend: 0x0, symName: __ZL15powerStateArray, symObjAddr: 0x51B0, symBinAddr: 0x13950, symSize: 0x0 }
  - { offsetInCU: 0x3DF4, offset: 0x49642, size: 0x8, addend: 0x0, symName: __ZL11deviceTable, symObjAddr: 0x4230, symBinAddr: 0x12DD0, symSize: 0x0 }
  - { offsetInCU: 0x40DE, offset: 0x4992C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x952, symSize: 0x32 }
  - { offsetInCU: 0x40E6, offset: 0x49934, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassD1Ev, symObjAddr: 0x32, symBinAddr: 0x984, symSize: 0xA }
  - { offsetInCU: 0x415F, offset: 0x499AD, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassD1Ev, symObjAddr: 0x32, symBinAddr: 0x984, symSize: 0xA }
  - { offsetInCU: 0x4167, offset: 0x499B5, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC2EPK11OSMetaClass, symObjAddr: 0x3C, symBinAddr: 0x98E, symSize: 0x20 }
  - { offsetInCU: 0x4197, offset: 0x499E5, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC2EPK11OSMetaClass, symObjAddr: 0x3C, symBinAddr: 0x98E, symSize: 0x20 }
  - { offsetInCU: 0x41B7, offset: 0x49A05, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC2EPK11OSMetaClass, symObjAddr: 0x3C, symBinAddr: 0x98E, symSize: 0x20 }
  - { offsetInCU: 0x4232, offset: 0x49A80, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC1EPK11OSMetaClass, symObjAddr: 0x5C, symBinAddr: 0x9AE, symSize: 0x20 }
  - { offsetInCU: 0x42BA, offset: 0x49B08, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiD2Ev, symObjAddr: 0x7C, symBinAddr: 0x9CE, symSize: 0xA }
  - { offsetInCU: 0x42C2, offset: 0x49B10, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiD1Ev, symObjAddr: 0x86, symBinAddr: 0x9D8, symSize: 0xA }
  - { offsetInCU: 0x430C, offset: 0x49B5A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiD1Ev, symObjAddr: 0x86, symBinAddr: 0x9D8, symSize: 0xA }
  - { offsetInCU: 0x4314, offset: 0x49B62, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiD0Ev, symObjAddr: 0x90, symBinAddr: 0x9E2, symSize: 0x22 }
  - { offsetInCU: 0x4344, offset: 0x49B92, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiD0Ev, symObjAddr: 0x90, symBinAddr: 0x9E2, symSize: 0x22 }
  - { offsetInCU: 0x4388, offset: 0x49BD6, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiD0Ev, symObjAddr: 0x90, symBinAddr: 0x9E2, symSize: 0x22 }
  - { offsetInCU: 0x4416, offset: 0x49C64, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi12getMetaClassEv, symObjAddr: 0xB2, symBinAddr: 0xA04, symSize: 0xE }
  - { offsetInCU: 0x4448, offset: 0x49C96, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassC2Ev, symObjAddr: 0xC0, symBinAddr: 0xA12, symSize: 0x32 }
  - { offsetInCU: 0x4450, offset: 0x49C9E, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi9MetaClass5allocEv, symObjAddr: 0xF2, symBinAddr: 0xA44, symSize: 0x40 }
  - { offsetInCU: 0x44C1, offset: 0x49D0F, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi9MetaClass5allocEv, symObjAddr: 0xF2, symBinAddr: 0xA44, symSize: 0x40 }
  - { offsetInCU: 0x44C9, offset: 0x49D17, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC1Ev, symObjAddr: 0x132, symBinAddr: 0xA84, symSize: 0x30 }
  - { offsetInCU: 0x4547, offset: 0x49D95, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC1Ev, symObjAddr: 0x132, symBinAddr: 0xA84, symSize: 0x30 }
  - { offsetInCU: 0x45A2, offset: 0x49DF0, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC2Ev, symObjAddr: 0x162, symBinAddr: 0xAB4, symSize: 0x30 }
  - { offsetInCU: 0x45D1, offset: 0x49E1F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi4initEP12OSDictionary, symObjAddr: 0x192, symBinAddr: 0xAE4, symSize: 0x126 }
  - { offsetInCU: 0x463F, offset: 0x49E8D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi4freeEv, symObjAddr: 0x2B8, symBinAddr: 0xC0A, symSize: 0x1D2 }
  - { offsetInCU: 0x4689, offset: 0x49ED7, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi5startEP9IOService, symObjAddr: 0x48A, symBinAddr: 0xDDC, symSize: 0x288 }
  - { offsetInCU: 0x4691, offset: 0x49EDF, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10intelStartEv, symObjAddr: 0x712, symBinAddr: 0x1064, symSize: 0x3DE }
  - { offsetInCU: 0x4918, offset: 0x4A166, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10intelStartEv, symObjAddr: 0x712, symBinAddr: 0x1064, symSize: 0x3DE }
  - { offsetInCU: 0x4920, offset: 0x4A16E, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi4stopEP9IOService, symObjAddr: 0xAF0, symBinAddr: 0x1442, symSize: 0x20E }
  - { offsetInCU: 0x4B9D, offset: 0x4A3EB, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi4stopEP9IOService, symObjAddr: 0xAF0, symBinAddr: 0x1442, symSize: 0x20E }
  - { offsetInCU: 0x4BA5, offset: 0x4A3F3, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi23registerWithPolicyMakerEP9IOService, symObjAddr: 0xCFE, symBinAddr: 0x1650, symSize: 0x32 }
  - { offsetInCU: 0x4BFE, offset: 0x4A44C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi23registerWithPolicyMakerEP9IOService, symObjAddr: 0xCFE, symBinAddr: 0x1650, symSize: 0x32 }
  - { offsetInCU: 0x4C06, offset: 0x4A454, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi13setPowerStateEmP9IOService, symObjAddr: 0xD30, symBinAddr: 0x1682, symSize: 0x54 }
  - { offsetInCU: 0x4C4A, offset: 0x4A498, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi13setPowerStateEmP9IOService, symObjAddr: 0xD30, symBinAddr: 0x1682, symSize: 0x54 }
  - { offsetInCU: 0x4CCA, offset: 0x4A518, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18systemWillShutdownEj, symObjAddr: 0xD84, symBinAddr: 0x16D6, symSize: 0x6C }
  - { offsetInCU: 0x4CD2, offset: 0x4A520, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi6enableEP16IOKernelDebugger, symObjAddr: 0xDF0, symBinAddr: 0x1742, symSize: 0xA }
  - { offsetInCU: 0x4D16, offset: 0x4A564, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi6enableEP16IOKernelDebugger, symObjAddr: 0xDF0, symBinAddr: 0x1742, symSize: 0xA }
  - { offsetInCU: 0x4D1E, offset: 0x4A56C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12driverEnableEv, symObjAddr: 0xDFA, symBinAddr: 0x174C, symSize: 0xC6 }
  - { offsetInCU: 0x4D5C, offset: 0x4A5AA, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12driverEnableEv, symObjAddr: 0xDFA, symBinAddr: 0x174C, symSize: 0xC6 }
  - { offsetInCU: 0x4D64, offset: 0x4A5B2, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi7disableEP16IOKernelDebugger, symObjAddr: 0xEC0, symBinAddr: 0x1812, symSize: 0xE }
  - { offsetInCU: 0x4DB9, offset: 0x4A607, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi7disableEP16IOKernelDebugger, symObjAddr: 0xEC0, symBinAddr: 0x1812, symSize: 0xE }
  - { offsetInCU: 0x4E01, offset: 0x4A64F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi13driverDisableEv, symObjAddr: 0xECE, symBinAddr: 0x1820, symSize: 0xEE }
  - { offsetInCU: 0x4E5B, offset: 0x4A6A9, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi6enableEP18IONetworkInterface, symObjAddr: 0xFBC, symBinAddr: 0x190E, symSize: 0xA }
  - { offsetInCU: 0x4E63, offset: 0x4A6B1, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi7disableEP18IONetworkInterface, symObjAddr: 0xFC6, symBinAddr: 0x1918, symSize: 0xE }
  - { offsetInCU: 0x4EA1, offset: 0x4A6EF, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi7disableEP18IONetworkInterface, symObjAddr: 0xFC6, symBinAddr: 0x1918, symSize: 0xE }
  - { offsetInCU: 0x4EE9, offset: 0x4A737, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12freePacketExEP6__mbufj, symObjAddr: 0xFD4, symBinAddr: 0x1926, symSize: 0x90 }
  - { offsetInCU: 0x4EF1, offset: 0x4A73F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10kdpStartupEv, symObjAddr: 0x1064, symBinAddr: 0x19B6, symSize: 0x10A }
  - { offsetInCU: 0x4F5F, offset: 0x4A7AD, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10kdpStartupEv, symObjAddr: 0x1064, symBinAddr: 0x19B6, symSize: 0x10A }
  - { offsetInCU: 0x4FC0, offset: 0x4A80E, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11isKdpPacketEPhj, symObjAddr: 0x116E, symBinAddr: 0x1AC0, symSize: 0x8A }
  - { offsetInCU: 0x4FC8, offset: 0x4A816, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi13receivePacketEPvPjj, symObjAddr: 0x11F8, symBinAddr: 0x1B4A, symSize: 0x226 }
  - { offsetInCU: 0x50D1, offset: 0x4A91F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi13receivePacketEPvPjj, symObjAddr: 0x11F8, symBinAddr: 0x1B4A, symSize: 0x226 }
  - { offsetInCU: 0x527E, offset: 0x4AACC, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10sendPacketEPvj, symObjAddr: 0x141E, symBinAddr: 0x1D70, symSize: 0x57A }
  - { offsetInCU: 0x5286, offset: 0x4AAD4, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11txInterruptEj, symObjAddr: 0x1998, symBinAddr: 0x22EA, symSize: 0xE8 }
  - { offsetInCU: 0x546E, offset: 0x4ACBC, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11txInterruptEj, symObjAddr: 0x1998, symBinAddr: 0x22EA, symSize: 0xE8 }
  - { offsetInCU: 0x5476, offset: 0x4ACC4, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11outputStartEP18IONetworkInterfacej, symObjAddr: 0x1A80, symBinAddr: 0x23D2, symSize: 0x4C4 }
  - { offsetInCU: 0x54F5, offset: 0x4AD43, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11outputStartEP18IONetworkInterfacej, symObjAddr: 0x1A80, symBinAddr: 0x23D2, symSize: 0x4C4 }
  - { offsetInCU: 0x54FD, offset: 0x4AD4B, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi26getPacketBufferConstraintsEP25IOPacketBufferConstraints, symObjAddr: 0x1F44, symBinAddr: 0x2896, symSize: 0x14 }
  - { offsetInCU: 0x571C, offset: 0x4AF6A, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi26getPacketBufferConstraintsEP25IOPacketBufferConstraints, symObjAddr: 0x1F44, symBinAddr: 0x2896, symSize: 0x14 }
  - { offsetInCU: 0x5762, offset: 0x4AFB0, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17createOutputQueueEv, symObjAddr: 0x1F58, symBinAddr: 0x28AA, symSize: 0xC }
  - { offsetInCU: 0x576A, offset: 0x4AFB8, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi15newVendorStringEv, symObjAddr: 0x1F64, symBinAddr: 0x28B6, symSize: 0x12 }
  - { offsetInCU: 0x5797, offset: 0x4AFE5, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi15newVendorStringEv, symObjAddr: 0x1F64, symBinAddr: 0x28B6, symSize: 0x12 }
  - { offsetInCU: 0x57CA, offset: 0x4B018, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi14newModelStringEv, symObjAddr: 0x1F76, symBinAddr: 0x28C8, symSize: 0x20 }
  - { offsetInCU: 0x57D2, offset: 0x4B020, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18configureInterfaceEP18IONetworkInterface, symObjAddr: 0x1F96, symBinAddr: 0x28E8, symSize: 0x184 }
  - { offsetInCU: 0x5801, offset: 0x4B04F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18configureInterfaceEP18IONetworkInterface, symObjAddr: 0x1F96, symBinAddr: 0x28E8, symSize: 0x184 }
  - { offsetInCU: 0x5809, offset: 0x4B057, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14createWorkLoopEv, symObjAddr: 0x211A, symBinAddr: 0x2A6C, symSize: 0x22 }
  - { offsetInCU: 0x58B2, offset: 0x4B100, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14createWorkLoopEv, symObjAddr: 0x211A, symBinAddr: 0x2A6C, symSize: 0x22 }
  - { offsetInCU: 0x58BA, offset: 0x4B108, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi11getWorkLoopEv, symObjAddr: 0x213C, symBinAddr: 0x2A8E, symSize: 0xE }
  - { offsetInCU: 0x58E9, offset: 0x4B137, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi11getWorkLoopEv, symObjAddr: 0x213C, symBinAddr: 0x2A8E, symSize: 0xE }
  - { offsetInCU: 0x591E, offset: 0x4B16C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18setPromiscuousModeEb, symObjAddr: 0x214A, symBinAddr: 0x2A9C, symSize: 0x70 }
  - { offsetInCU: 0x5926, offset: 0x4B174, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16setMulticastModeEb, symObjAddr: 0x21BA, symBinAddr: 0x2B0C, symSize: 0x6C }
  - { offsetInCU: 0x59FA, offset: 0x4B248, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16setMulticastModeEb, symObjAddr: 0x21BA, symBinAddr: 0x2B0C, symSize: 0x6C }
  - { offsetInCU: 0x5AEA, offset: 0x4B338, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16setMulticastListEP17IOEthernetAddressj, symObjAddr: 0x2226, symBinAddr: 0x2B78, symSize: 0xF0 }
  - { offsetInCU: 0x5B9F, offset: 0x4B3ED, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18getChecksumSupportEPjjb, symObjAddr: 0x2316, symBinAddr: 0x2C68, symSize: 0x36 }
  - { offsetInCU: 0x5BA7, offset: 0x4B3F5, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi11getFeaturesEv, symObjAddr: 0x234C, symBinAddr: 0x2C9E, symSize: 0xC }
  - { offsetInCU: 0x5C20, offset: 0x4B46E, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi11getFeaturesEv, symObjAddr: 0x234C, symBinAddr: 0x2C9E, symSize: 0xC }
  - { offsetInCU: 0x5C65, offset: 0x4B4B3, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi20setWakeOnMagicPacketEb, symObjAddr: 0x2358, symBinAddr: 0x2CAA, symSize: 0x20 }
  - { offsetInCU: 0x5CC4, offset: 0x4B512, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi16getPacketFiltersEPK8OSSymbolPj, symObjAddr: 0x2378, symBinAddr: 0x2CCA, symSize: 0x42 }
  - { offsetInCU: 0x5CCC, offset: 0x4B51A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18setHardwareAddressEPK17IOEthernetAddress, symObjAddr: 0x23BA, symBinAddr: 0x2D0C, symSize: 0x64 }
  - { offsetInCU: 0x5D31, offset: 0x4B57F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18setHardwareAddressEPK17IOEthernetAddress, symObjAddr: 0x23BA, symBinAddr: 0x2D0C, symSize: 0x64 }
  - { offsetInCU: 0x5DFD, offset: 0x4B64B, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18getHardwareAddressEP17IOEthernetAddress, symObjAddr: 0x241E, symBinAddr: 0x2D70, symSize: 0x50 }
  - { offsetInCU: 0x5E05, offset: 0x4B653, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12selectMediumEPK15IONetworkMedium, symObjAddr: 0x246E, symBinAddr: 0x2DC0, symSize: 0x50 }
  - { offsetInCU: 0x5EB4, offset: 0x4B702, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12selectMediumEPK15IONetworkMedium, symObjAddr: 0x246E, symBinAddr: 0x2DC0, symSize: 0x50 }
  - { offsetInCU: 0x5EBC, offset: 0x4B70A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16updateStatisticsEP13e1000_adapter, symObjAddr: 0x24BE, symBinAddr: 0x2E10, symSize: 0x2B8 }
  - { offsetInCU: 0x5F23, offset: 0x4B771, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16updateStatisticsEP13e1000_adapter, symObjAddr: 0x24BE, symBinAddr: 0x2E10, symSize: 0x2B8 }
  - { offsetInCU: 0x5F2B, offset: 0x4B779, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi16getMaxPacketSizeEPj, symObjAddr: 0x2776, symBinAddr: 0x30C8, symSize: 0xE }
  - { offsetInCU: 0x65B8, offset: 0x4BE06, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi16getMaxPacketSizeEPj, symObjAddr: 0x2776, symBinAddr: 0x30C8, symSize: 0xE }
  - { offsetInCU: 0x65C0, offset: 0x4BE0E, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16setMaxPacketSizeEj, symObjAddr: 0x2784, symBinAddr: 0x30D6, symSize: 0x5C }
  - { offsetInCU: 0x65FE, offset: 0x4BE4C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16setMaxPacketSizeEj, symObjAddr: 0x2784, symBinAddr: 0x30D6, symSize: 0x5C }
  - { offsetInCU: 0x6606, offset: 0x4BE54, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11setLinkDownEv, symObjAddr: 0x27E0, symBinAddr: 0x3132, symSize: 0x92 }
  - { offsetInCU: 0x668E, offset: 0x4BEDC, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11setLinkDownEv, symObjAddr: 0x27E0, symBinAddr: 0x3132, symSize: 0x92 }
  - { offsetInCU: 0x6764, offset: 0x4BFB2, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11rxInterruptEP18IONetworkInterfacejP11IOMbufQueuePv, symObjAddr: 0x2872, symBinAddr: 0x31C4, symSize: 0x3AC }
  - { offsetInCU: 0x676C, offset: 0x4BFBA, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi15checkLinkStatusEv, symObjAddr: 0x2C1E, symBinAddr: 0x3570, symSize: 0xF0 }
  - { offsetInCU: 0x69D9, offset: 0x4C227, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi15checkLinkStatusEv, symObjAddr: 0x2C1E, symBinAddr: 0x3570, symSize: 0xF0 }
  - { offsetInCU: 0x6ABB, offset: 0x4C309, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9setLinkUpEv, symObjAddr: 0x2D0E, symBinAddr: 0x3660, symSize: 0x370 }
  - { offsetInCU: 0x6D9F, offset: 0x4C5ED, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17interruptOccurredEP8OSObjectP22IOInterruptEventSourcei, symObjAddr: 0x307E, symBinAddr: 0x39D0, symSize: 0xEC }
  - { offsetInCU: 0x6DA7, offset: 0x4C5F5, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi27setInputPacketPollingEnableEP18IONetworkInterfaceb, symObjAddr: 0x316A, symBinAddr: 0x3ABC, symSize: 0x4C }
  - { offsetInCU: 0x6F2D, offset: 0x4C77B, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi27setInputPacketPollingEnableEP18IONetworkInterfaceb, symObjAddr: 0x316A, symBinAddr: 0x3ABC, symSize: 0x4C }
  - { offsetInCU: 0x6F35, offset: 0x4C783, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16pollInputPacketsEP18IONetworkInterfacejP11IOMbufQueuePv, symObjAddr: 0x31B6, symBinAddr: 0x3B08, symSize: 0x2E }
  - { offsetInCU: 0x700D, offset: 0x4C85B, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16pollInputPacketsEP18IONetworkInterfacejP11IOMbufQueuePv, symObjAddr: 0x31B6, symBinAddr: 0x3B08, symSize: 0x2E }
  - { offsetInCU: 0x7015, offset: 0x4C863, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17intelIdentifyChipEv, symObjAddr: 0x31E4, symBinAddr: 0x3B36, symSize: 0xCA }
  - { offsetInCU: 0x7094, offset: 0x4C8E2, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17intelIdentifyChipEv, symObjAddr: 0x31E4, symBinAddr: 0x3B36, symSize: 0xCA }
  - { offsetInCU: 0x7157, offset: 0x4C9A5, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11timerActionEP18IOTimerEventSource, symObjAddr: 0x32AE, symBinAddr: 0x3C00, symSize: 0xAC }
  - { offsetInCU: 0x71F4, offset: 0x4CA42, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16checkForDeadlockEv, symObjAddr: 0x335A, symBinAddr: 0x3CAC, symSize: 0x11C }
  - { offsetInCU: 0x71FC, offset: 0x4CA4A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassD0Ev, symObjAddr: 0x3476, symBinAddr: 0x3DC8, symSize: 0xA }
  - { offsetInCU: 0x7310, offset: 0x4CB5E, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassD0Ev, symObjAddr: 0x3476, symBinAddr: 0x3DC8, symSize: 0xA }
  - { offsetInCU: 0x7318, offset: 0x4CB66, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_IntelMausiEthernet.cpp, symObjAddr: 0x3480, symBinAddr: 0x3DD2, symSize: 0x33 }
  - { offsetInCU: 0x7354, offset: 0x4CBA2, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_IntelMausiEthernet.cpp, symObjAddr: 0x3480, symBinAddr: 0x3DD2, symSize: 0x33 }
  - { offsetInCU: 0x737E, offset: 0x4CBCC, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_IntelMausiEthernet.cpp, symObjAddr: 0x3480, symBinAddr: 0x3DD2, symSize: 0x33 }
  - { offsetInCU: 0x73CD, offset: 0x4CC1B, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_IntelMausiEthernet.cpp, symObjAddr: 0x3480, symBinAddr: 0x3DD2, symSize: 0x33 }
  - { offsetInCU: 0x73D5, offset: 0x4CC23, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x34B3, symBinAddr: 0x3E05, symSize: 0x11 }
  - { offsetInCU: 0x73F3, offset: 0x4CC41, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x34B3, symBinAddr: 0x3E05, symSize: 0x11 }
  - { offsetInCU: 0x740F, offset: 0x4CC5D, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x34B3, symBinAddr: 0x3E05, symSize: 0x11 }
  - { offsetInCU: 0x743A, offset: 0x4CC88, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x34B3, symBinAddr: 0x3E05, symSize: 0x11 }
  - { offsetInCU: 0x745D, offset: 0x4CCAB, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x34B3, symBinAddr: 0x3E05, symSize: 0x11 }
  - { offsetInCU: 0x27, offset: 0x4D036, size: 0x8, addend: 0x0, symName: _e1000e_get_bus_info_pcie, symObjAddr: 0x0, symBinAddr: 0x3E18, symSize: 0x8 }
  - { offsetInCU: 0x29D, offset: 0x4D2AC, size: 0x8, addend: 0x0, symName: _e1000e_get_bus_info_pcie, symObjAddr: 0x0, symBinAddr: 0x3E18, symSize: 0x8 }
  - { offsetInCU: 0x2A5, offset: 0x4D2B4, size: 0x8, addend: 0x0, symName: _e1000_set_lan_id_multi_port_pcie, symObjAddr: 0x8, symBinAddr: 0x3E20, symSize: 0x1A }
  - { offsetInCU: 0x30D, offset: 0x4D31C, size: 0x8, addend: 0x0, symName: _e1000_set_lan_id_multi_port_pcie, symObjAddr: 0x8, symBinAddr: 0x3E20, symSize: 0x1A }
  - { offsetInCU: 0x315, offset: 0x4D324, size: 0x8, addend: 0x0, symName: _e1000_set_lan_id_single_port, symObjAddr: 0x22, symBinAddr: 0x3E3A, symSize: 0xF }
  - { offsetInCU: 0x383, offset: 0x4D392, size: 0x8, addend: 0x0, symName: _e1000_set_lan_id_single_port, symObjAddr: 0x22, symBinAddr: 0x3E3A, symSize: 0xF }
  - { offsetInCU: 0x38B, offset: 0x4D39A, size: 0x8, addend: 0x0, symName: _e1000_clear_vfta_generic, symObjAddr: 0x31, symBinAddr: 0x3E49, symSize: 0x27 }
  - { offsetInCU: 0x3F9, offset: 0x4D408, size: 0x8, addend: 0x0, symName: _e1000_clear_vfta_generic, symObjAddr: 0x31, symBinAddr: 0x3E49, symSize: 0x27 }
  - { offsetInCU: 0x401, offset: 0x4D410, size: 0x8, addend: 0x0, symName: _e1000_write_vfta_generic, symObjAddr: 0x58, symBinAddr: 0x3E70, symSize: 0x17 }
  - { offsetInCU: 0x491, offset: 0x4D4A0, size: 0x8, addend: 0x0, symName: _e1000_write_vfta_generic, symObjAddr: 0x58, symBinAddr: 0x3E70, symSize: 0x17 }
  - { offsetInCU: 0x499, offset: 0x4D4A8, size: 0x8, addend: 0x0, symName: _e1000e_init_rx_addrs, symObjAddr: 0x6F, symBinAddr: 0x3E87, symSize: 0x65 }
  - { offsetInCU: 0x53C, offset: 0x4D54B, size: 0x8, addend: 0x0, symName: _e1000e_init_rx_addrs, symObjAddr: 0x6F, symBinAddr: 0x3E87, symSize: 0x65 }
  - { offsetInCU: 0x544, offset: 0x4D553, size: 0x8, addend: 0x0, symName: _e1000_check_alt_mac_addr_generic, symObjAddr: 0xD4, symBinAddr: 0x3EEC, symSize: 0x115 }
  - { offsetInCU: 0x1E11, offset: 0x4EE20, size: 0x8, addend: 0x0, symName: _e1000_check_alt_mac_addr_generic, symObjAddr: 0xD4, symBinAddr: 0x3EEC, symSize: 0x115 }
  - { offsetInCU: 0x1E19, offset: 0x4EE28, size: 0x8, addend: 0x0, symName: _e1000e_rar_get_count_generic, symObjAddr: 0x1E9, symBinAddr: 0x4001, symSize: 0xD }
  - { offsetInCU: 0x1F55, offset: 0x4EF64, size: 0x8, addend: 0x0, symName: _e1000e_rar_get_count_generic, symObjAddr: 0x1E9, symBinAddr: 0x4001, symSize: 0xD }
  - { offsetInCU: 0x1F5D, offset: 0x4EF6C, size: 0x8, addend: 0x0, symName: _e1000e_rar_set_generic, symObjAddr: 0x1F6, symBinAddr: 0x400E, symSize: 0x79 }
  - { offsetInCU: 0x1F89, offset: 0x4EF98, size: 0x8, addend: 0x0, symName: _e1000e_rar_set_generic, symObjAddr: 0x1F6, symBinAddr: 0x400E, symSize: 0x79 }
  - { offsetInCU: 0x1F91, offset: 0x4EFA0, size: 0x8, addend: 0x0, symName: _e1000e_update_mc_addr_list_generic, symObjAddr: 0x26F, symBinAddr: 0x4087, symSize: 0xF9 }
  - { offsetInCU: 0x20BB, offset: 0x4F0CA, size: 0x8, addend: 0x0, symName: _e1000e_update_mc_addr_list_generic, symObjAddr: 0x26F, symBinAddr: 0x4087, symSize: 0xF9 }
  - { offsetInCU: 0x20C3, offset: 0x4F0D2, size: 0x8, addend: 0x0, symName: _e1000e_clear_hw_cntrs_base, symObjAddr: 0x368, symBinAddr: 0x4180, symSize: 0xE8 }
  - { offsetInCU: 0x21FD, offset: 0x4F20C, size: 0x8, addend: 0x0, symName: _e1000e_clear_hw_cntrs_base, symObjAddr: 0x368, symBinAddr: 0x4180, symSize: 0xE8 }
  - { offsetInCU: 0x2205, offset: 0x4F214, size: 0x8, addend: 0x0, symName: _e1000e_check_for_copper_link, symObjAddr: 0x450, symBinAddr: 0x4268, symSize: 0x74 }
  - { offsetInCU: 0x2887, offset: 0x4F896, size: 0x8, addend: 0x0, symName: _e1000e_check_for_copper_link, symObjAddr: 0x450, symBinAddr: 0x4268, symSize: 0x74 }
  - { offsetInCU: 0x288F, offset: 0x4F89E, size: 0x8, addend: 0x0, symName: _e1000e_config_fc_after_link_up, symObjAddr: 0x4C4, symBinAddr: 0x42DC, symSize: 0x278 }
  - { offsetInCU: 0x2943, offset: 0x4F952, size: 0x8, addend: 0x0, symName: _e1000e_config_fc_after_link_up, symObjAddr: 0x4C4, symBinAddr: 0x42DC, symSize: 0x278 }
  - { offsetInCU: 0x294B, offset: 0x4F95A, size: 0x8, addend: 0x0, symName: _e1000e_check_for_fiber_link, symObjAddr: 0x73C, symBinAddr: 0x4554, symSize: 0xB6 }
  - { offsetInCU: 0x2B72, offset: 0x4FB81, size: 0x8, addend: 0x0, symName: _e1000e_check_for_fiber_link, symObjAddr: 0x73C, symBinAddr: 0x4554, symSize: 0xB6 }
  - { offsetInCU: 0x2B7A, offset: 0x4FB89, size: 0x8, addend: 0x0, symName: _e1000e_check_for_serdes_link, symObjAddr: 0x7F2, symBinAddr: 0x460A, symSize: 0x138 }
  - { offsetInCU: 0x2CBA, offset: 0x4FCC9, size: 0x8, addend: 0x0, symName: _e1000e_check_for_serdes_link, symObjAddr: 0x7F2, symBinAddr: 0x460A, symSize: 0x138 }
  - { offsetInCU: 0x2CC2, offset: 0x4FCD1, size: 0x8, addend: 0x0, symName: _e1000e_setup_link_generic, symObjAddr: 0x92A, symBinAddr: 0x4742, symSize: 0xE8 }
  - { offsetInCU: 0x2E9A, offset: 0x4FEA9, size: 0x8, addend: 0x0, symName: _e1000e_setup_link_generic, symObjAddr: 0x92A, symBinAddr: 0x4742, symSize: 0xE8 }
  - { offsetInCU: 0x2EA2, offset: 0x4FEB1, size: 0x8, addend: 0x0, symName: _e1000e_set_fc_watermarks, symObjAddr: 0xA12, symBinAddr: 0x482A, symSize: 0x5E }
  - { offsetInCU: 0x2F55, offset: 0x4FF64, size: 0x8, addend: 0x0, symName: _e1000e_set_fc_watermarks, symObjAddr: 0xA12, symBinAddr: 0x482A, symSize: 0x5E }
  - { offsetInCU: 0x2F5D, offset: 0x4FF6C, size: 0x8, addend: 0x0, symName: _e1000e_setup_fiber_serdes_link, symObjAddr: 0xA70, symBinAddr: 0x4888, symSize: 0xDC }
  - { offsetInCU: 0x305E, offset: 0x5006D, size: 0x8, addend: 0x0, symName: _e1000e_setup_fiber_serdes_link, symObjAddr: 0xA70, symBinAddr: 0x4888, symSize: 0xDC }
  - { offsetInCU: 0x3066, offset: 0x50075, size: 0x8, addend: 0x0, symName: _e1000e_config_collision_dist_generic, symObjAddr: 0xB4C, symBinAddr: 0x4964, symSize: 0x36 }
  - { offsetInCU: 0x3172, offset: 0x50181, size: 0x8, addend: 0x0, symName: _e1000e_config_collision_dist_generic, symObjAddr: 0xB4C, symBinAddr: 0x4964, symSize: 0x36 }
  - { offsetInCU: 0x317A, offset: 0x50189, size: 0x8, addend: 0x0, symName: _e1000e_force_mac_fc, symObjAddr: 0xB82, symBinAddr: 0x499A, symSize: 0x7E }
  - { offsetInCU: 0x31E1, offset: 0x501F0, size: 0x8, addend: 0x0, symName: _e1000e_force_mac_fc, symObjAddr: 0xB82, symBinAddr: 0x499A, symSize: 0x7E }
  - { offsetInCU: 0x3258, offset: 0x50267, size: 0x8, addend: 0x0, symName: _e1000e_get_speed_and_duplex_copper, symObjAddr: 0xC00, symBinAddr: 0x4A18, symSize: 0x33 }
  - { offsetInCU: 0x3260, offset: 0x5026F, size: 0x8, addend: 0x0, symName: _e1000e_get_speed_and_duplex_fiber_serdes, symObjAddr: 0xC33, symBinAddr: 0x4A4B, symSize: 0x12 }
  - { offsetInCU: 0x32ED, offset: 0x502FC, size: 0x8, addend: 0x0, symName: _e1000e_get_speed_and_duplex_fiber_serdes, symObjAddr: 0xC33, symBinAddr: 0x4A4B, symSize: 0x12 }
  - { offsetInCU: 0x32F5, offset: 0x50304, size: 0x8, addend: 0x0, symName: _e1000e_get_hw_semaphore, symObjAddr: 0xC45, symBinAddr: 0x4A5D, symSize: 0xB2 }
  - { offsetInCU: 0x336D, offset: 0x5037C, size: 0x8, addend: 0x0, symName: _e1000e_get_hw_semaphore, symObjAddr: 0xC45, symBinAddr: 0x4A5D, symSize: 0xB2 }
  - { offsetInCU: 0x3375, offset: 0x50384, size: 0x8, addend: 0x0, symName: _e1000e_put_hw_semaphore, symObjAddr: 0xCF7, symBinAddr: 0x4B0F, symSize: 0x1C }
  - { offsetInCU: 0x3469, offset: 0x50478, size: 0x8, addend: 0x0, symName: _e1000e_put_hw_semaphore, symObjAddr: 0xCF7, symBinAddr: 0x4B0F, symSize: 0x1C }
  - { offsetInCU: 0x3471, offset: 0x50480, size: 0x8, addend: 0x0, symName: _e1000e_get_auto_rd_done, symObjAddr: 0xD13, symBinAddr: 0x4B2B, symSize: 0x3D }
  - { offsetInCU: 0x34C0, offset: 0x504CF, size: 0x8, addend: 0x0, symName: _e1000e_get_auto_rd_done, symObjAddr: 0xD13, symBinAddr: 0x4B2B, symSize: 0x3D }
  - { offsetInCU: 0x34C8, offset: 0x504D7, size: 0x8, addend: 0x0, symName: _e1000e_valid_led_default, symObjAddr: 0xD50, symBinAddr: 0x4B68, symSize: 0x3B }
  - { offsetInCU: 0x3509, offset: 0x50518, size: 0x8, addend: 0x0, symName: _e1000e_valid_led_default, symObjAddr: 0xD50, symBinAddr: 0x4B68, symSize: 0x3B }
  - { offsetInCU: 0x3511, offset: 0x50520, size: 0x8, addend: 0x0, symName: _e1000e_id_led_init_generic, symObjAddr: 0xD8B, symBinAddr: 0x4BA3, symSize: 0x161 }
  - { offsetInCU: 0x35A2, offset: 0x505B1, size: 0x8, addend: 0x0, symName: _e1000e_id_led_init_generic, symObjAddr: 0xD8B, symBinAddr: 0x4BA3, symSize: 0x161 }
  - { offsetInCU: 0x36A2, offset: 0x506B1, size: 0x8, addend: 0x0, symName: _e1000e_setup_led_generic, symObjAddr: 0xEEC, symBinAddr: 0x4D04, symSize: 0x61 }
  - { offsetInCU: 0x36AA, offset: 0x506B9, size: 0x8, addend: 0x0, symName: _e1000e_cleanup_led_generic, symObjAddr: 0xF4D, symBinAddr: 0x4D65, symSize: 0x18 }
  - { offsetInCU: 0x371A, offset: 0x50729, size: 0x8, addend: 0x0, symName: _e1000e_cleanup_led_generic, symObjAddr: 0xF4D, symBinAddr: 0x4D65, symSize: 0x18 }
  - { offsetInCU: 0x3722, offset: 0x50731, size: 0x8, addend: 0x0, symName: _e1000e_blink_led_generic, symObjAddr: 0xF65, symBinAddr: 0x4D7D, symSize: 0x7E }
  - { offsetInCU: 0x3752, offset: 0x50761, size: 0x8, addend: 0x0, symName: _e1000e_blink_led_generic, symObjAddr: 0xF65, symBinAddr: 0x4D7D, symSize: 0x7E }
  - { offsetInCU: 0x375A, offset: 0x50769, size: 0x8, addend: 0x0, symName: _e1000e_led_on_generic, symObjAddr: 0xFE3, symBinAddr: 0x4DFB, symSize: 0x3D }
  - { offsetInCU: 0x37F1, offset: 0x50800, size: 0x8, addend: 0x0, symName: _e1000e_led_on_generic, symObjAddr: 0xFE3, symBinAddr: 0x4DFB, symSize: 0x3D }
  - { offsetInCU: 0x37F9, offset: 0x50808, size: 0x8, addend: 0x0, symName: _e1000e_led_off_generic, symObjAddr: 0x1020, symBinAddr: 0x4E38, symSize: 0x37 }
  - { offsetInCU: 0x383E, offset: 0x5084D, size: 0x8, addend: 0x0, symName: _e1000e_led_off_generic, symObjAddr: 0x1020, symBinAddr: 0x4E38, symSize: 0x37 }
  - { offsetInCU: 0x3846, offset: 0x50855, size: 0x8, addend: 0x0, symName: _e1000e_set_pcie_no_snoop, symObjAddr: 0x1057, symBinAddr: 0x4E6F, symSize: 0x23 }
  - { offsetInCU: 0x388B, offset: 0x5089A, size: 0x8, addend: 0x0, symName: _e1000e_set_pcie_no_snoop, symObjAddr: 0x1057, symBinAddr: 0x4E6F, symSize: 0x23 }
  - { offsetInCU: 0x3893, offset: 0x508A2, size: 0x8, addend: 0x0, symName: _e1000e_disable_pcie_master, symObjAddr: 0x107A, symBinAddr: 0x4E92, symSize: 0x51 }
  - { offsetInCU: 0x390F, offset: 0x5091E, size: 0x8, addend: 0x0, symName: _e1000e_disable_pcie_master, symObjAddr: 0x107A, symBinAddr: 0x4E92, symSize: 0x51 }
  - { offsetInCU: 0x3917, offset: 0x50926, size: 0x8, addend: 0x0, symName: _e1000e_reset_adaptive, symObjAddr: 0x10CB, symBinAddr: 0x4EE3, symSize: 0x40 }
  - { offsetInCU: 0x399B, offset: 0x509AA, size: 0x8, addend: 0x0, symName: _e1000e_reset_adaptive, symObjAddr: 0x10CB, symBinAddr: 0x4EE3, symSize: 0x40 }
  - { offsetInCU: 0x39A3, offset: 0x509B2, size: 0x8, addend: 0x0, symName: _e1000e_update_adaptive, symObjAddr: 0x110B, symBinAddr: 0x4F23, symSize: 0x93 }
  - { offsetInCU: 0x39DA, offset: 0x509E9, size: 0x8, addend: 0x0, symName: _e1000e_update_adaptive, symObjAddr: 0x110B, symBinAddr: 0x4F23, symSize: 0x93 }
  - { offsetInCU: 0x27, offset: 0x50A63, size: 0x8, addend: 0x0, symName: _e1000e_check_reset_block_generic, symObjAddr: 0x0, symBinAddr: 0x4FB8, symSize: 0x19 }
  - { offsetInCU: 0x2F, offset: 0x50A6B, size: 0x8, addend: 0x0, symName: _e1000_m88_cable_length_table, symObjAddr: 0x2920, symBinAddr: 0x11E50, symSize: 0x0 }
  - { offsetInCU: 0x39, offset: 0x50A75, size: 0x8, addend: 0x0, symName: _e1000e_get_cable_length_igp_2, symObjAddr: 0x1500, symBinAddr: 0x64B8, symSize: 0x113 }
  - { offsetInCU: 0x41, offset: 0x50A7D, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_info_m88, symObjAddr: 0x1613, symBinAddr: 0x65CB, symSize: 0x134 }
  - { offsetInCU: 0x6C, offset: 0x50AA8, size: 0x8, addend: 0x0, symName: _e1000e_get_cable_length_igp_2.agc_reg_array, symObjAddr: 0x292E, symBinAddr: 0x11E5E, symSize: 0x0 }
  - { offsetInCU: 0x189, offset: 0x50BC5, size: 0x8, addend: 0x0, symName: _e1000_m88_cable_length_table, symObjAddr: 0x2920, symBinAddr: 0x11E50, symSize: 0x0 }
  - { offsetInCU: 0x1AB, offset: 0x50BE7, size: 0x8, addend: 0x0, symName: _e1000_igp_2_cable_length_table, symObjAddr: 0x2940, symBinAddr: 0x11E70, symSize: 0x0 }
  - { offsetInCU: 0x453, offset: 0x50E8F, size: 0x8, addend: 0x0, symName: _e1000e_check_reset_block_generic, symObjAddr: 0x0, symBinAddr: 0x4FB8, symSize: 0x19 }
  - { offsetInCU: 0x45B, offset: 0x50E97, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_id, symObjAddr: 0x19, symBinAddr: 0x4FD1, symSize: 0xAB }
  - { offsetInCU: 0x1D2A, offset: 0x52766, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_id, symObjAddr: 0x19, symBinAddr: 0x4FD1, symSize: 0xAB }
  - { offsetInCU: 0x1D32, offset: 0x5276E, size: 0x8, addend: 0x0, symName: _e1000e_phy_reset_dsp, symObjAddr: 0xC4, symBinAddr: 0x507C, symSize: 0x3D }
  - { offsetInCU: 0x1E4A, offset: 0x52886, size: 0x8, addend: 0x0, symName: _e1000e_phy_reset_dsp, symObjAddr: 0xC4, symBinAddr: 0x507C, symSize: 0x3D }
  - { offsetInCU: 0x1E52, offset: 0x5288E, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_mdic, symObjAddr: 0x101, symBinAddr: 0x50B9, symSize: 0xB4 }
  - { offsetInCU: 0x1EB3, offset: 0x528EF, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_mdic, symObjAddr: 0x101, symBinAddr: 0x50B9, symSize: 0xB4 }
  - { offsetInCU: 0x1EDA, offset: 0x52916, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_mdic, symObjAddr: 0x101, symBinAddr: 0x50B9, symSize: 0xB4 }
  - { offsetInCU: 0x1EE2, offset: 0x5291E, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_mdic, symObjAddr: 0x1B5, symBinAddr: 0x516D, symSize: 0xB1 }
  - { offsetInCU: 0x1F97, offset: 0x529D3, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_mdic, symObjAddr: 0x1B5, symBinAddr: 0x516D, symSize: 0xB1 }
  - { offsetInCU: 0x1F9F, offset: 0x529DB, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_m88, symObjAddr: 0x266, symBinAddr: 0x521E, symSize: 0x49 }
  - { offsetInCU: 0x2058, offset: 0x52A94, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_m88, symObjAddr: 0x266, symBinAddr: 0x521E, symSize: 0x49 }
  - { offsetInCU: 0x2060, offset: 0x52A9C, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_m88, symObjAddr: 0x2AF, symBinAddr: 0x5267, symSize: 0x4A }
  - { offsetInCU: 0x20C6, offset: 0x52B02, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_m88, symObjAddr: 0x2AF, symBinAddr: 0x5267, symSize: 0x4A }
  - { offsetInCU: 0x20CE, offset: 0x52B0A, size: 0x8, addend: 0x0, symName: _e1000_set_page_igp, symObjAddr: 0x2F9, symBinAddr: 0x52B1, symSize: 0x1B }
  - { offsetInCU: 0x213D, offset: 0x52B79, size: 0x8, addend: 0x0, symName: _e1000_set_page_igp, symObjAddr: 0x2F9, symBinAddr: 0x52B1, symSize: 0x1B }
  - { offsetInCU: 0x2145, offset: 0x52B81, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_igp, symObjAddr: 0x314, symBinAddr: 0x52CC, symSize: 0xC }
  - { offsetInCU: 0x2168, offset: 0x52BA4, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_igp, symObjAddr: 0x314, symBinAddr: 0x52CC, symSize: 0xC }
  - { offsetInCU: 0x2170, offset: 0x52BAC, size: 0x8, addend: 0x0, symName: ___e1000e_read_phy_reg_igp, symObjAddr: 0x320, symBinAddr: 0x52D8, symSize: 0x90 }
  - { offsetInCU: 0x21C0, offset: 0x52BFC, size: 0x8, addend: 0x0, symName: ___e1000e_read_phy_reg_igp, symObjAddr: 0x320, symBinAddr: 0x52D8, symSize: 0x90 }
  - { offsetInCU: 0x21C8, offset: 0x52C04, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_igp_locked, symObjAddr: 0x3B0, symBinAddr: 0x5368, symSize: 0xF }
  - { offsetInCU: 0x223F, offset: 0x52C7B, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_igp_locked, symObjAddr: 0x3B0, symBinAddr: 0x5368, symSize: 0xF }
  - { offsetInCU: 0x2247, offset: 0x52C83, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_igp, symObjAddr: 0x3BF, symBinAddr: 0x5377, symSize: 0xC }
  - { offsetInCU: 0x2297, offset: 0x52CD3, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_igp, symObjAddr: 0x3BF, symBinAddr: 0x5377, symSize: 0xC }
  - { offsetInCU: 0x229F, offset: 0x52CDB, size: 0x8, addend: 0x0, symName: ___e1000e_write_phy_reg_igp, symObjAddr: 0x3CB, symBinAddr: 0x5383, symSize: 0x91 }
  - { offsetInCU: 0x22F3, offset: 0x52D2F, size: 0x8, addend: 0x0, symName: ___e1000e_write_phy_reg_igp, symObjAddr: 0x3CB, symBinAddr: 0x5383, symSize: 0x91 }
  - { offsetInCU: 0x22FB, offset: 0x52D37, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_igp_locked, symObjAddr: 0x45C, symBinAddr: 0x5414, symSize: 0xF }
  - { offsetInCU: 0x2376, offset: 0x52DB2, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_igp_locked, symObjAddr: 0x45C, symBinAddr: 0x5414, symSize: 0xF }
  - { offsetInCU: 0x237E, offset: 0x52DBA, size: 0x8, addend: 0x0, symName: _e1000e_read_kmrn_reg, symObjAddr: 0x46B, symBinAddr: 0x5423, symSize: 0xC }
  - { offsetInCU: 0x23D2, offset: 0x52E0E, size: 0x8, addend: 0x0, symName: _e1000e_read_kmrn_reg, symObjAddr: 0x46B, symBinAddr: 0x5423, symSize: 0xC }
  - { offsetInCU: 0x23DA, offset: 0x52E16, size: 0x8, addend: 0x0, symName: ___e1000_read_kmrn_reg, symObjAddr: 0x477, symBinAddr: 0x542F, symSize: 0x9B }
  - { offsetInCU: 0x242A, offset: 0x52E66, size: 0x8, addend: 0x0, symName: ___e1000_read_kmrn_reg, symObjAddr: 0x477, symBinAddr: 0x542F, symSize: 0x9B }
  - { offsetInCU: 0x2432, offset: 0x52E6E, size: 0x8, addend: 0x0, symName: _e1000e_read_kmrn_reg_locked, symObjAddr: 0x512, symBinAddr: 0x54CA, symSize: 0xF }
  - { offsetInCU: 0x2525, offset: 0x52F61, size: 0x8, addend: 0x0, symName: _e1000e_read_kmrn_reg_locked, symObjAddr: 0x512, symBinAddr: 0x54CA, symSize: 0xF }
  - { offsetInCU: 0x252D, offset: 0x52F69, size: 0x8, addend: 0x0, symName: _e1000e_write_kmrn_reg, symObjAddr: 0x521, symBinAddr: 0x54D9, symSize: 0xC }
  - { offsetInCU: 0x257D, offset: 0x52FB9, size: 0x8, addend: 0x0, symName: _e1000e_write_kmrn_reg, symObjAddr: 0x521, symBinAddr: 0x54D9, symSize: 0xC }
  - { offsetInCU: 0x2585, offset: 0x52FC1, size: 0x8, addend: 0x0, symName: ___e1000_write_kmrn_reg, symObjAddr: 0x52D, symBinAddr: 0x54E5, symSize: 0xA0 }
  - { offsetInCU: 0x25D9, offset: 0x53015, size: 0x8, addend: 0x0, symName: ___e1000_write_kmrn_reg, symObjAddr: 0x52D, symBinAddr: 0x54E5, symSize: 0xA0 }
  - { offsetInCU: 0x25E1, offset: 0x5301D, size: 0x8, addend: 0x0, symName: _e1000e_write_kmrn_reg_locked, symObjAddr: 0x5CD, symBinAddr: 0x5585, symSize: 0x35 }
  - { offsetInCU: 0x2707, offset: 0x53143, size: 0x8, addend: 0x0, symName: _e1000e_write_kmrn_reg_locked, symObjAddr: 0x5CD, symBinAddr: 0x5585, symSize: 0x35 }
  - { offsetInCU: 0x270F, offset: 0x5314B, size: 0x8, addend: 0x0, symName: _e1000_copper_link_setup_82577, symObjAddr: 0x602, symBinAddr: 0x55BA, symSize: 0xAD }
  - { offsetInCU: 0x27DB, offset: 0x53217, size: 0x8, addend: 0x0, symName: _e1000_copper_link_setup_82577, symObjAddr: 0x602, symBinAddr: 0x55BA, symSize: 0xAD }
  - { offsetInCU: 0x27E3, offset: 0x5321F, size: 0x8, addend: 0x0, symName: _e1000_set_master_slave_mode, symObjAddr: 0x6AF, symBinAddr: 0x5667, symSize: 0x8C }
  - { offsetInCU: 0x28F7, offset: 0x53333, size: 0x8, addend: 0x0, symName: _e1000_set_master_slave_mode, symObjAddr: 0x6AF, symBinAddr: 0x5667, symSize: 0x8C }
  - { offsetInCU: 0x28FF, offset: 0x5333B, size: 0x8, addend: 0x0, symName: _e1000e_copper_link_setup_m88, symObjAddr: 0x73B, symBinAddr: 0x56F3, symSize: 0x237 }
  - { offsetInCU: 0x29BA, offset: 0x533F6, size: 0x8, addend: 0x0, symName: _e1000e_copper_link_setup_m88, symObjAddr: 0x73B, symBinAddr: 0x56F3, symSize: 0x237 }
  - { offsetInCU: 0x29C2, offset: 0x533FE, size: 0x8, addend: 0x0, symName: _e1000e_copper_link_setup_igp, symObjAddr: 0x972, symBinAddr: 0x592A, symSize: 0x148 }
  - { offsetInCU: 0x2C0B, offset: 0x53647, size: 0x8, addend: 0x0, symName: _e1000e_copper_link_setup_igp, symObjAddr: 0x972, symBinAddr: 0x592A, symSize: 0x148 }
  - { offsetInCU: 0x2C13, offset: 0x5364F, size: 0x8, addend: 0x0, symName: _e1000e_setup_copper_link, symObjAddr: 0xABA, symBinAddr: 0x5A72, symSize: 0x23E }
  - { offsetInCU: 0x2EBC, offset: 0x538F8, size: 0x8, addend: 0x0, symName: _e1000e_setup_copper_link, symObjAddr: 0xABA, symBinAddr: 0x5A72, symSize: 0x23E }
  - { offsetInCU: 0x313F, offset: 0x53B7B, size: 0x8, addend: 0x0, symName: _e1000e_phy_has_link_generic, symObjAddr: 0xCF8, symBinAddr: 0x5CB0, symSize: 0xD8 }
  - { offsetInCU: 0x3147, offset: 0x53B83, size: 0x8, addend: 0x0, symName: _e1000e_phy_force_speed_duplex_igp, symObjAddr: 0xDD0, symBinAddr: 0x5D88, symSize: 0xCD }
  - { offsetInCU: 0x3251, offset: 0x53C8D, size: 0x8, addend: 0x0, symName: _e1000e_phy_force_speed_duplex_igp, symObjAddr: 0xDD0, symBinAddr: 0x5D88, symSize: 0xCD }
  - { offsetInCU: 0x3259, offset: 0x53C95, size: 0x8, addend: 0x0, symName: _e1000e_phy_force_speed_duplex_setup, symObjAddr: 0xE9D, symBinAddr: 0x5E55, symSize: 0x81 }
  - { offsetInCU: 0x3383, offset: 0x53DBF, size: 0x8, addend: 0x0, symName: _e1000e_phy_force_speed_duplex_setup, symObjAddr: 0xE9D, symBinAddr: 0x5E55, symSize: 0x81 }
  - { offsetInCU: 0x338B, offset: 0x53DC7, size: 0x8, addend: 0x0, symName: _e1000e_phy_force_speed_duplex_m88, symObjAddr: 0xF1E, symBinAddr: 0x5ED6, symSize: 0x1BD }
  - { offsetInCU: 0x341B, offset: 0x53E57, size: 0x8, addend: 0x0, symName: _e1000e_phy_force_speed_duplex_m88, symObjAddr: 0xF1E, symBinAddr: 0x5ED6, symSize: 0x1BD }
  - { offsetInCU: 0x3423, offset: 0x53E5F, size: 0x8, addend: 0x0, symName: _e1000_phy_force_speed_duplex_ife, symObjAddr: 0x10DB, symBinAddr: 0x6093, symSize: 0xD1 }
  - { offsetInCU: 0x36BC, offset: 0x540F8, size: 0x8, addend: 0x0, symName: _e1000_phy_force_speed_duplex_ife, symObjAddr: 0x10DB, symBinAddr: 0x6093, symSize: 0xD1 }
  - { offsetInCU: 0x36C4, offset: 0x54100, size: 0x8, addend: 0x0, symName: _e1000e_set_d3_lplu_state, symObjAddr: 0x11AC, symBinAddr: 0x6164, symSize: 0x156 }
  - { offsetInCU: 0x37EE, offset: 0x5422A, size: 0x8, addend: 0x0, symName: _e1000e_set_d3_lplu_state, symObjAddr: 0x11AC, symBinAddr: 0x6164, symSize: 0x156 }
  - { offsetInCU: 0x37F6, offset: 0x54232, size: 0x8, addend: 0x0, symName: _e1000e_check_downshift, symObjAddr: 0x1302, symBinAddr: 0x62BA, symSize: 0x76 }
  - { offsetInCU: 0x3A07, offset: 0x54443, size: 0x8, addend: 0x0, symName: _e1000e_check_downshift, symObjAddr: 0x1302, symBinAddr: 0x62BA, symSize: 0x76 }
  - { offsetInCU: 0x3A0F, offset: 0x5444B, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_m88, symObjAddr: 0x1378, symBinAddr: 0x6330, symSize: 0x37 }
  - { offsetInCU: 0x3AD1, offset: 0x5450D, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_m88, symObjAddr: 0x1378, symBinAddr: 0x6330, symSize: 0x37 }
  - { offsetInCU: 0x3AD9, offset: 0x54515, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_igp, symObjAddr: 0x13AF, symBinAddr: 0x6367, symSize: 0x87 }
  - { offsetInCU: 0x3B3E, offset: 0x5457A, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_igp, symObjAddr: 0x13AF, symBinAddr: 0x6367, symSize: 0x87 }
  - { offsetInCU: 0x3B46, offset: 0x54582, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_ife, symObjAddr: 0x1436, symBinAddr: 0x63EE, symSize: 0x5F }
  - { offsetInCU: 0x3C38, offset: 0x54674, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_ife, symObjAddr: 0x1436, symBinAddr: 0x63EE, symSize: 0x5F }
  - { offsetInCU: 0x3C40, offset: 0x5467C, size: 0x8, addend: 0x0, symName: _e1000e_get_cable_length_m88, symObjAddr: 0x1495, symBinAddr: 0x644D, symSize: 0x6B }
  - { offsetInCU: 0x3D02, offset: 0x5473E, size: 0x8, addend: 0x0, symName: _e1000e_get_cable_length_m88, symObjAddr: 0x1495, symBinAddr: 0x644D, symSize: 0x6B }
  - { offsetInCU: 0x3D0A, offset: 0x54746, size: 0x8, addend: 0x0, symName: _e1000e_get_cable_length_igp_2, symObjAddr: 0x1500, symBinAddr: 0x64B8, symSize: 0x113 }
  - { offsetInCU: 0x3E08, offset: 0x54844, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_info_m88, symObjAddr: 0x1613, symBinAddr: 0x65CB, symSize: 0x134 }
  - { offsetInCU: 0x3E10, offset: 0x5484C, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_info_igp, symObjAddr: 0x1747, symBinAddr: 0x66FF, symSize: 0xE1 }
  - { offsetInCU: 0x3F85, offset: 0x549C1, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_info_igp, symObjAddr: 0x1747, symBinAddr: 0x66FF, symSize: 0xE1 }
  - { offsetInCU: 0x3F8D, offset: 0x549C9, size: 0x8, addend: 0x0, symName: _e1000_get_phy_info_ife, symObjAddr: 0x1828, symBinAddr: 0x67E0, symSize: 0xCA }
  - { offsetInCU: 0x4065, offset: 0x54AA1, size: 0x8, addend: 0x0, symName: _e1000_get_phy_info_ife, symObjAddr: 0x1828, symBinAddr: 0x67E0, symSize: 0xCA }
  - { offsetInCU: 0x406D, offset: 0x54AA9, size: 0x8, addend: 0x0, symName: _e1000e_phy_sw_reset, symObjAddr: 0x18F2, symBinAddr: 0x68AA, symSize: 0x4E }
  - { offsetInCU: 0x4145, offset: 0x54B81, size: 0x8, addend: 0x0, symName: _e1000e_phy_sw_reset, symObjAddr: 0x18F2, symBinAddr: 0x68AA, symSize: 0x4E }
  - { offsetInCU: 0x414D, offset: 0x54B89, size: 0x8, addend: 0x0, symName: _e1000e_phy_hw_reset_generic, symObjAddr: 0x1940, symBinAddr: 0x68F8, symSize: 0x94 }
  - { offsetInCU: 0x41FF, offset: 0x54C3B, size: 0x8, addend: 0x0, symName: _e1000e_phy_hw_reset_generic, symObjAddr: 0x1940, symBinAddr: 0x68F8, symSize: 0x94 }
  - { offsetInCU: 0x4207, offset: 0x54C43, size: 0x8, addend: 0x0, symName: _e1000e_get_cfg_done_generic, symObjAddr: 0x19D4, symBinAddr: 0x698C, symSize: 0x12 }
  - { offsetInCU: 0x42ED, offset: 0x54D29, size: 0x8, addend: 0x0, symName: _e1000e_get_cfg_done_generic, symObjAddr: 0x19D4, symBinAddr: 0x698C, symSize: 0x12 }
  - { offsetInCU: 0x42F5, offset: 0x54D31, size: 0x8, addend: 0x0, symName: _e1000e_phy_init_script_igp3, symObjAddr: 0x19E6, symBinAddr: 0x699E, symSize: 0x263 }
  - { offsetInCU: 0x4325, offset: 0x54D61, size: 0x8, addend: 0x0, symName: _e1000e_phy_init_script_igp3, symObjAddr: 0x19E6, symBinAddr: 0x699E, symSize: 0x263 }
  - { offsetInCU: 0x432D, offset: 0x54D69, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_type_from_id, symObjAddr: 0x1C49, symBinAddr: 0x6C01, symSize: 0xF2 }
  - { offsetInCU: 0x49EE, offset: 0x5542A, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_type_from_id, symObjAddr: 0x1C49, symBinAddr: 0x6C01, symSize: 0xF2 }
  - { offsetInCU: 0x49F6, offset: 0x55432, size: 0x8, addend: 0x0, symName: _e1000e_determine_phy_address, symObjAddr: 0x1D3B, symBinAddr: 0x6CF3, symSize: 0x6A }
  - { offsetInCU: 0x4A35, offset: 0x55471, size: 0x8, addend: 0x0, symName: _e1000e_determine_phy_address, symObjAddr: 0x1D3B, symBinAddr: 0x6CF3, symSize: 0x6A }
  - { offsetInCU: 0x4A3D, offset: 0x55479, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_bm, symObjAddr: 0x1DA5, symBinAddr: 0x6D5D, symSize: 0xD8 }
  - { offsetInCU: 0x4AF0, offset: 0x5552C, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_bm, symObjAddr: 0x1DA5, symBinAddr: 0x6D5D, symSize: 0xD8 }
  - { offsetInCU: 0x4AF8, offset: 0x55534, size: 0x8, addend: 0x0, symName: _e1000_access_phy_wakeup_reg_bm, symObjAddr: 0x1E7D, symBinAddr: 0x6E35, symSize: 0xAA }
  - { offsetInCU: 0x4BF8, offset: 0x55634, size: 0x8, addend: 0x0, symName: _e1000_access_phy_wakeup_reg_bm, symObjAddr: 0x1E7D, symBinAddr: 0x6E35, symSize: 0xAA }
  - { offsetInCU: 0x4C00, offset: 0x5563C, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_bm, symObjAddr: 0x1F27, symBinAddr: 0x6EDF, symSize: 0xCC }
  - { offsetInCU: 0x4CAE, offset: 0x556EA, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_bm, symObjAddr: 0x1F27, symBinAddr: 0x6EDF, symSize: 0xCC }
  - { offsetInCU: 0x4CB6, offset: 0x556F2, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_bm2, symObjAddr: 0x1FF3, symBinAddr: 0x6FAB, symSize: 0x9F }
  - { offsetInCU: 0x4DB2, offset: 0x557EE, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_bm2, symObjAddr: 0x1FF3, symBinAddr: 0x6FAB, symSize: 0x9F }
  - { offsetInCU: 0x4DBA, offset: 0x557F6, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_bm2, symObjAddr: 0x2092, symBinAddr: 0x704A, symSize: 0xAB }
  - { offsetInCU: 0x4E4B, offset: 0x55887, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_bm2, symObjAddr: 0x2092, symBinAddr: 0x704A, symSize: 0xAB }
  - { offsetInCU: 0x4E53, offset: 0x5588F, size: 0x8, addend: 0x0, symName: _e1000_enable_phy_wakeup_reg_access_bm, symObjAddr: 0x213D, symBinAddr: 0x70F5, symSize: 0x81 }
  - { offsetInCU: 0x4F1C, offset: 0x55958, size: 0x8, addend: 0x0, symName: _e1000_enable_phy_wakeup_reg_access_bm, symObjAddr: 0x213D, symBinAddr: 0x70F5, symSize: 0x81 }
  - { offsetInCU: 0x4F24, offset: 0x55960, size: 0x8, addend: 0x0, symName: _e1000_disable_phy_wakeup_reg_access_bm, symObjAddr: 0x21BE, symBinAddr: 0x7176, symSize: 0x44 }
  - { offsetInCU: 0x4FC6, offset: 0x55A02, size: 0x8, addend: 0x0, symName: _e1000_disable_phy_wakeup_reg_access_bm, symObjAddr: 0x21BE, symBinAddr: 0x7176, symSize: 0x44 }
  - { offsetInCU: 0x4FE9, offset: 0x55A25, size: 0x8, addend: 0x0, symName: _e1000_disable_phy_wakeup_reg_access_bm, symObjAddr: 0x21BE, symBinAddr: 0x7176, symSize: 0x44 }
  - { offsetInCU: 0x4FF1, offset: 0x55A2D, size: 0x8, addend: 0x0, symName: _e1000_power_up_phy_copper, symObjAddr: 0x2202, symBinAddr: 0x71BA, symSize: 0x45 }
  - { offsetInCU: 0x5073, offset: 0x55AAF, size: 0x8, addend: 0x0, symName: _e1000_power_up_phy_copper, symObjAddr: 0x2202, symBinAddr: 0x71BA, symSize: 0x45 }
  - { offsetInCU: 0x507B, offset: 0x55AB7, size: 0x8, addend: 0x0, symName: _e1000_power_down_phy_copper, symObjAddr: 0x2247, symBinAddr: 0x71FF, symSize: 0x4F }
  - { offsetInCU: 0x5110, offset: 0x55B4C, size: 0x8, addend: 0x0, symName: _e1000_power_down_phy_copper, symObjAddr: 0x2247, symBinAddr: 0x71FF, symSize: 0x4F }
  - { offsetInCU: 0x5118, offset: 0x55B54, size: 0x8, addend: 0x0, symName: _e1000_read_phy_reg_hv, symObjAddr: 0x2296, symBinAddr: 0x724E, symSize: 0xF }
  - { offsetInCU: 0x51AD, offset: 0x55BE9, size: 0x8, addend: 0x0, symName: _e1000_read_phy_reg_hv, symObjAddr: 0x2296, symBinAddr: 0x724E, symSize: 0xF }
  - { offsetInCU: 0x51B5, offset: 0x55BF1, size: 0x8, addend: 0x0, symName: ___e1000_read_phy_reg_hv, symObjAddr: 0x22A5, symBinAddr: 0x725D, symSize: 0x11F }
  - { offsetInCU: 0x523C, offset: 0x55C78, size: 0x8, addend: 0x0, symName: ___e1000_read_phy_reg_hv, symObjAddr: 0x22A5, symBinAddr: 0x725D, symSize: 0x11F }
  - { offsetInCU: 0x5244, offset: 0x55C80, size: 0x8, addend: 0x0, symName: _e1000_read_phy_reg_hv_locked, symObjAddr: 0x23C4, symBinAddr: 0x737C, symSize: 0x12 }
  - { offsetInCU: 0x536E, offset: 0x55DAA, size: 0x8, addend: 0x0, symName: _e1000_read_phy_reg_hv_locked, symObjAddr: 0x23C4, symBinAddr: 0x737C, symSize: 0x12 }
  - { offsetInCU: 0x5376, offset: 0x55DB2, size: 0x8, addend: 0x0, symName: _e1000_read_phy_reg_page_hv, symObjAddr: 0x23D6, symBinAddr: 0x738E, symSize: 0x15 }
  - { offsetInCU: 0x53C6, offset: 0x55E02, size: 0x8, addend: 0x0, symName: _e1000_read_phy_reg_page_hv, symObjAddr: 0x23D6, symBinAddr: 0x738E, symSize: 0x15 }
  - { offsetInCU: 0x53CE, offset: 0x55E0A, size: 0x8, addend: 0x0, symName: _e1000_write_phy_reg_hv, symObjAddr: 0x23EB, symBinAddr: 0x73A3, symSize: 0xF }
  - { offsetInCU: 0x541E, offset: 0x55E5A, size: 0x8, addend: 0x0, symName: _e1000_write_phy_reg_hv, symObjAddr: 0x23EB, symBinAddr: 0x73A3, symSize: 0xF }
  - { offsetInCU: 0x5426, offset: 0x55E62, size: 0x8, addend: 0x0, symName: ___e1000_write_phy_reg_hv, symObjAddr: 0x23FA, symBinAddr: 0x73B2, symSize: 0x19B }
  - { offsetInCU: 0x547A, offset: 0x55EB6, size: 0x8, addend: 0x0, symName: ___e1000_write_phy_reg_hv, symObjAddr: 0x23FA, symBinAddr: 0x73B2, symSize: 0x19B }
  - { offsetInCU: 0x5482, offset: 0x55EBE, size: 0x8, addend: 0x0, symName: _e1000_write_phy_reg_hv_locked, symObjAddr: 0x2595, symBinAddr: 0x754D, symSize: 0x12 }
  - { offsetInCU: 0x55D8, offset: 0x56014, size: 0x8, addend: 0x0, symName: _e1000_write_phy_reg_hv_locked, symObjAddr: 0x2595, symBinAddr: 0x754D, symSize: 0x12 }
  - { offsetInCU: 0x55E0, offset: 0x5601C, size: 0x8, addend: 0x0, symName: _e1000_write_phy_reg_page_hv, symObjAddr: 0x25A7, symBinAddr: 0x755F, symSize: 0x15 }
  - { offsetInCU: 0x5634, offset: 0x56070, size: 0x8, addend: 0x0, symName: _e1000_write_phy_reg_page_hv, symObjAddr: 0x25A7, symBinAddr: 0x755F, symSize: 0x15 }
  - { offsetInCU: 0x563C, offset: 0x56078, size: 0x8, addend: 0x0, symName: _e1000_link_stall_workaround_hv, symObjAddr: 0x25BC, symBinAddr: 0x7574, symSize: 0xB7 }
  - { offsetInCU: 0x5690, offset: 0x560CC, size: 0x8, addend: 0x0, symName: _e1000_link_stall_workaround_hv, symObjAddr: 0x25BC, symBinAddr: 0x7574, symSize: 0xB7 }
  - { offsetInCU: 0x5698, offset: 0x560D4, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_82577, symObjAddr: 0x2673, symBinAddr: 0x762B, symSize: 0x38 }
  - { offsetInCU: 0x57BA, offset: 0x561F6, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_82577, symObjAddr: 0x2673, symBinAddr: 0x762B, symSize: 0x38 }
  - { offsetInCU: 0x57C2, offset: 0x561FE, size: 0x8, addend: 0x0, symName: _e1000_phy_force_speed_duplex_82577, symObjAddr: 0x26AB, symBinAddr: 0x7663, symSize: 0x98 }
  - { offsetInCU: 0x5827, offset: 0x56263, size: 0x8, addend: 0x0, symName: _e1000_phy_force_speed_duplex_82577, symObjAddr: 0x26AB, symBinAddr: 0x7663, symSize: 0x98 }
  - { offsetInCU: 0x582F, offset: 0x5626B, size: 0x8, addend: 0x0, symName: _e1000_get_phy_info_82577, symObjAddr: 0x2743, symBinAddr: 0x76FB, symSize: 0x113 }
  - { offsetInCU: 0x5953, offset: 0x5638F, size: 0x8, addend: 0x0, symName: _e1000_get_phy_info_82577, symObjAddr: 0x2743, symBinAddr: 0x76FB, symSize: 0x113 }
  - { offsetInCU: 0x595B, offset: 0x56397, size: 0x8, addend: 0x0, symName: _e1000_get_cable_length_82577, symObjAddr: 0x2856, symBinAddr: 0x780E, symSize: 0x48 }
  - { offsetInCU: 0x5AA0, offset: 0x564DC, size: 0x8, addend: 0x0, symName: _e1000_get_cable_length_82577, symObjAddr: 0x2856, symBinAddr: 0x780E, symSize: 0x48 }
  - { offsetInCU: 0x5AA8, offset: 0x564E4, size: 0x8, addend: 0x0, symName: _e1000_access_phy_debug_regs_hv, symObjAddr: 0x289E, symBinAddr: 0x7856, symSize: 0x82 }
  - { offsetInCU: 0x5B54, offset: 0x56590, size: 0x8, addend: 0x0, symName: _e1000_access_phy_debug_regs_hv, symObjAddr: 0x289E, symBinAddr: 0x7856, symSize: 0x82 }
  - { offsetInCU: 0x5B5C, offset: 0x56598, size: 0x8, addend: 0x0, symName: _e1000_m88_cable_length_table, symObjAddr: 0x2920, symBinAddr: 0x11E50, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x5666A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18initPCIConfigSpaceEP11IOPCIDevice, symObjAddr: 0x0, symBinAddr: 0x78D8, symSize: 0x236 }
  - { offsetInCU: 0x142, offset: 0x56785, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18initPCIConfigSpaceEP11IOPCIDevice, symObjAddr: 0x0, symBinAddr: 0x78D8, symSize: 0x236 }
  - { offsetInCU: 0x14A, offset: 0x5678D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21initPCIPowerManagmentEP11IOPCIDevicePK10e1000_info, symObjAddr: 0x236, symBinAddr: 0x7B0E, symSize: 0x100 }
  - { offsetInCU: 0x22C, offset: 0x5686F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21initPCIPowerManagmentEP11IOPCIDevicePK10e1000_info, symObjAddr: 0x236, symBinAddr: 0x7B0E, symSize: 0x100 }
  - { offsetInCU: 0x234, offset: 0x56877, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi23setPowerStateWakeActionEP8OSObjectPvS2_S2_S2_, symObjAddr: 0x336, symBinAddr: 0x7C0E, symSize: 0x86 }
  - { offsetInCU: 0x2DA, offset: 0x5691D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi23setPowerStateWakeActionEP8OSObjectPvS2_S2_S2_, symObjAddr: 0x336, symBinAddr: 0x7C0E, symSize: 0x86 }
  - { offsetInCU: 0x3E5, offset: 0x56A28, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi24setPowerStateSleepActionEP8OSObjectPvS2_S2_S2_, symObjAddr: 0x3BC, symBinAddr: 0x7C94, symSize: 0x7A }
  - { offsetInCU: 0x3ED, offset: 0x56A30, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17intelEEPROMChecksEP13e1000_adapter, symObjAddr: 0x436, symBinAddr: 0x7D0E, symSize: 0x54 }
  - { offsetInCU: 0x514, offset: 0x56B57, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17intelEEPROMChecksEP13e1000_adapter, symObjAddr: 0x436, symBinAddr: 0x7D0E, symSize: 0x54 }
  - { offsetInCU: 0x62D, offset: 0x56C70, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14intelEnableIRQEP13e1000_adapter, symObjAddr: 0x48A, symBinAddr: 0x7D62, symSize: 0x2A }
  - { offsetInCU: 0x635, offset: 0x56C78, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi15intelDisableIRQEv, symObjAddr: 0x4B4, symBinAddr: 0x7D8C, symSize: 0x1A }
  - { offsetInCU: 0x6DA, offset: 0x56D1D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi15intelDisableIRQEv, symObjAddr: 0x4B4, symBinAddr: 0x7D8C, symSize: 0x1A }
  - { offsetInCU: 0x6E2, offset: 0x56D25, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11intelEnableEv, symObjAddr: 0x4CE, symBinAddr: 0x7DA6, symSize: 0x23E }
  - { offsetInCU: 0x8A1, offset: 0x56EE4, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11intelEnableEv, symObjAddr: 0x4CE, symBinAddr: 0x7DA6, symSize: 0x23E }
  - { offsetInCU: 0xADC, offset: 0x5711F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14intelCheckLinkEP13e1000_adapter, symObjAddr: 0x70C, symBinAddr: 0x7FE4, symSize: 0xBE }
  - { offsetInCU: 0xB9D, offset: 0x571E0, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi22intelSetupAdvForMediumEPK15IONetworkMedium, symObjAddr: 0x7CA, symBinAddr: 0x80A2, symSize: 0x18E }
  - { offsetInCU: 0xC89, offset: 0x572CC, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10intelResetEP13e1000_adapter, symObjAddr: 0x958, symBinAddr: 0x8230, symSize: 0x326 }
  - { offsetInCU: 0x117E, offset: 0x577C1, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi24intelInitManageabilityPtEP13e1000_adapter, symObjAddr: 0xC7E, symBinAddr: 0x8556, symSize: 0xF6 }
  - { offsetInCU: 0x13C6, offset: 0x57A09, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14intelConfigureEP13e1000_adapter, symObjAddr: 0xD74, symBinAddr: 0x864C, symSize: 0x60 }
  - { offsetInCU: 0x13CE, offset: 0x57A11, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12intelDisableEv, symObjAddr: 0xDD4, symBinAddr: 0x86AC, symSize: 0x270 }
  - { offsetInCU: 0x159A, offset: 0x57BDD, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12intelDisableEv, symObjAddr: 0xDD4, symBinAddr: 0x86AC, symSize: 0x270 }
  - { offsetInCU: 0x15A2, offset: 0x57BE5, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14intelFlushLPICEv, symObjAddr: 0x1044, symBinAddr: 0x891C, symSize: 0x36 }
  - { offsetInCU: 0x195A, offset: 0x57F9D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14intelFlushLPICEv, symObjAddr: 0x1044, symBinAddr: 0x891C, symSize: 0x36 }
  - { offsetInCU: 0x1962, offset: 0x57FA5, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9intelDownEP13e1000_adapterb, symObjAddr: 0x107A, symBinAddr: 0x8952, symSize: 0x140 }
  - { offsetInCU: 0x1A18, offset: 0x5805B, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9intelDownEP13e1000_adapterb, symObjAddr: 0x107A, symBinAddr: 0x8952, symSize: 0x140 }
  - { offsetInCU: 0x1DFC, offset: 0x5843F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19intelSetupRxControlEP13e1000_adapter, symObjAddr: 0x11BA, symBinAddr: 0x8A92, symSize: 0x1C0 }
  - { offsetInCU: 0x2114, offset: 0x58757, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18intelInitPhyWakeupEjP13IntelAddrData, symObjAddr: 0x137A, symBinAddr: 0x8C52, symSize: 0x544 }
  - { offsetInCU: 0x211C, offset: 0x5875F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18intelInitMacWakeupEjP13IntelAddrData, symObjAddr: 0x18BE, symBinAddr: 0x9196, symSize: 0xCC }
  - { offsetInCU: 0x233E, offset: 0x58981, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18intelInitMacWakeupEjP13IntelAddrData, symObjAddr: 0x18BE, symBinAddr: 0x9196, symSize: 0xCC }
  - { offsetInCU: 0x2500, offset: 0x58B43, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17intelPowerDownPhyEP13e1000_adapter, symObjAddr: 0x198A, symBinAddr: 0x9262, symSize: 0x20 }
  - { offsetInCU: 0x253D, offset: 0x58B80, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelConfigureTxEP13e1000_adapter, symObjAddr: 0x19AA, symBinAddr: 0x9282, symSize: 0x138 }
  - { offsetInCU: 0x29A7, offset: 0x58FEA, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17intelSetupRssHashEP13e1000_adapter, symObjAddr: 0x1AE2, symBinAddr: 0x93BA, symSize: 0xAA }
  - { offsetInCU: 0x29AF, offset: 0x58FF2, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi20intelVlanStripEnableEP13e1000_adapter, symObjAddr: 0x1B8C, symBinAddr: 0x9464, symSize: 0x14 }
  - { offsetInCU: 0x2B43, offset: 0x59186, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi20intelVlanStripEnableEP13e1000_adapter, symObjAddr: 0x1B8C, symBinAddr: 0x9464, symSize: 0x14 }
  - { offsetInCU: 0x2BC0, offset: 0x59203, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelConfigureRxEP13e1000_adapter, symObjAddr: 0x1BA0, symBinAddr: 0x9478, symSize: 0x13C }
  - { offsetInCU: 0x2BC8, offset: 0x5920B, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21intelUpdateTxDescTailEj, symObjAddr: 0x1CDC, symBinAddr: 0x95B4, symSize: 0x7E }
  - { offsetInCU: 0x30EB, offset: 0x5972E, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21intelUpdateTxDescTailEj, symObjAddr: 0x1CDC, symBinAddr: 0x95B4, symSize: 0x7E }
  - { offsetInCU: 0x30F3, offset: 0x59736, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21intelUpdateRxDescTailEj, symObjAddr: 0x1D5A, symBinAddr: 0x9632, symSize: 0x58 }
  - { offsetInCU: 0x328C, offset: 0x598CF, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21intelUpdateRxDescTailEj, symObjAddr: 0x1D5A, symBinAddr: 0x9632, symSize: 0x58 }
  - { offsetInCU: 0x33D7, offset: 0x59A1A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21intelFlushDescriptorsEv, symObjAddr: 0x1DB2, symBinAddr: 0x968A, symSize: 0x4E }
  - { offsetInCU: 0x33DF, offset: 0x59A22, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19intelFlushDescRingsEP13e1000_adapter, symObjAddr: 0x1E00, symBinAddr: 0x96D8, symSize: 0x8A }
  - { offsetInCU: 0x3558, offset: 0x59B9B, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19intelFlushDescRingsEP13e1000_adapter, symObjAddr: 0x1E00, symBinAddr: 0x96D8, symSize: 0x8A }
  - { offsetInCU: 0x3560, offset: 0x59BA3, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18intelResetAdaptiveEP8e1000_hw, symObjAddr: 0x1E8A, symBinAddr: 0x9762, symSize: 0x44 }
  - { offsetInCU: 0x362F, offset: 0x59C72, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18intelResetAdaptiveEP8e1000_hw, symObjAddr: 0x1E8A, symBinAddr: 0x9762, symSize: 0x44 }
  - { offsetInCU: 0x3637, offset: 0x59C7A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi22intelEnableMngPassThruEP8e1000_hw, symObjAddr: 0x1ECE, symBinAddr: 0x97A6, symSize: 0x4C }
  - { offsetInCU: 0x36B8, offset: 0x59CFB, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi22intelEnableMngPassThruEP8e1000_hw, symObjAddr: 0x1ECE, symBinAddr: 0x97A6, symSize: 0x4C }
  - { offsetInCU: 0x36C0, offset: 0x59D03, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19intelUpdateAdaptiveEP8e1000_hw, symObjAddr: 0x1F1A, symBinAddr: 0x97F2, symSize: 0x9A }
  - { offsetInCU: 0x37AF, offset: 0x59DF2, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19intelUpdateAdaptiveEP8e1000_hw, symObjAddr: 0x1F1A, symBinAddr: 0x97F2, symSize: 0x9A }
  - { offsetInCU: 0x3809, offset: 0x59E4C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21intelVlanStripDisableEP13e1000_adapter, symObjAddr: 0x1FB4, symBinAddr: 0x988C, symSize: 0x14 }
  - { offsetInCU: 0x389C, offset: 0x59EDF, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi15intelRssKeyFillEPvm, symObjAddr: 0x1FC8, symBinAddr: 0x98A0, symSize: 0x66 }
  - { offsetInCU: 0x38A4, offset: 0x59EE7, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12intelRestartEv, symObjAddr: 0x202E, symBinAddr: 0x9906, symSize: 0xEA }
  - { offsetInCU: 0x38FD, offset: 0x59F40, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12intelRestartEv, symObjAddr: 0x202E, symBinAddr: 0x9906, symSize: 0xEA }
  - { offsetInCU: 0x3A81, offset: 0x5A0C4, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelFlushTxRingEP13e1000_adapter, symObjAddr: 0x2118, symBinAddr: 0x99F0, symSize: 0xAA }
  - { offsetInCU: 0x3A89, offset: 0x5A0CC, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelFlushRxRingEP13e1000_adapter, symObjAddr: 0x21C2, symBinAddr: 0x9A9A, symSize: 0x86 }
  - { offsetInCU: 0x3C17, offset: 0x5A25A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelFlushRxRingEP13e1000_adapter, symObjAddr: 0x21C2, symBinAddr: 0x9A9A, symSize: 0x86 }
  - { offsetInCU: 0x3E38, offset: 0x5A47B, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18intelPhyReadStatusEP13e1000_adapter, symObjAddr: 0x2248, symBinAddr: 0x9B20, symSize: 0x158 }
  - { offsetInCU: 0x40F4, offset: 0x5A737, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi13setMaxLatencyEj, symObjAddr: 0x23A0, symBinAddr: 0x9C78, symSize: 0x4C }
  - { offsetInCU: 0x40FC, offset: 0x5A73F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelSupportsEEEEP13e1000_adapter, symObjAddr: 0x23EC, symBinAddr: 0x9CC4, symSize: 0xC8 }
  - { offsetInCU: 0x41B0, offset: 0x5A7F3, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelSupportsEEEEP13e1000_adapter, symObjAddr: 0x23EC, symBinAddr: 0x9CC4, symSize: 0xC8 }
  - { offsetInCU: 0x41B8, offset: 0x5A7FB, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14intelEnableEEEEP8e1000_hwt, symObjAddr: 0x24B4, symBinAddr: 0x9D8C, symSize: 0xFE }
  - { offsetInCU: 0x42B1, offset: 0x5A8F4, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14intelEnableEEEEP8e1000_hwt, symObjAddr: 0x24B4, symBinAddr: 0x9D8C, symSize: 0xFE }
  - { offsetInCU: 0x27, offset: 0x5AA9C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9getParamsEv, symObjAddr: 0x0, symBinAddr: 0x9E8A, symSize: 0x3E4 }
  - { offsetInCU: 0x53, offset: 0x5AAC8, size: 0x8, addend: 0x0, symName: __ZL15mediumTypeArray, symObjAddr: 0x14B0, symBinAddr: 0x11FA0, symSize: 0x0 }
  - { offsetInCU: 0x8D, offset: 0x5AB02, size: 0x8, addend: 0x0, symName: __ZL16mediumSpeedArray, symObjAddr: 0x14E0, symBinAddr: 0x11FD0, symSize: 0x0 }
  - { offsetInCU: 0x13D, offset: 0x5ABB2, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9getParamsEv, symObjAddr: 0x0, symBinAddr: 0x9E8A, symSize: 0x3E4 }
  - { offsetInCU: 0x213, offset: 0x5AC88, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi15setupMediumDictEv, symObjAddr: 0x3E4, symBinAddr: 0xA26E, symSize: 0x140 }
  - { offsetInCU: 0x21B, offset: 0x5AC90, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16initEventSourcesEP9IOService, symObjAddr: 0x524, symBinAddr: 0xA3AE, symSize: 0x18C }
  - { offsetInCU: 0x2BB, offset: 0x5AD30, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16initEventSourcesEP9IOService, symObjAddr: 0x524, symBinAddr: 0xA3AE, symSize: 0x18C }
  - { offsetInCU: 0x2C3, offset: 0x5AD38, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19setupDMADescriptorsEv, symObjAddr: 0x6B0, symBinAddr: 0xA53A, symSize: 0x614 }
  - { offsetInCU: 0x3F2, offset: 0x5AE67, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19setupDMADescriptorsEv, symObjAddr: 0x6B0, symBinAddr: 0xA53A, symSize: 0x614 }
  - { offsetInCU: 0x3FA, offset: 0x5AE6F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18freeDMADescriptorsEv, symObjAddr: 0xCC4, symBinAddr: 0xAB4E, symSize: 0x150 }
  - { offsetInCU: 0x62B, offset: 0x5B0A0, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18freeDMADescriptorsEv, symObjAddr: 0xCC4, symBinAddr: 0xAB4E, symSize: 0x150 }
  - { offsetInCU: 0x633, offset: 0x5B0A8, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16clearDescriptorsEv, symObjAddr: 0xE14, symBinAddr: 0xAC9E, symSize: 0xE2 }
  - { offsetInCU: 0x6B3, offset: 0x5B128, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16clearDescriptorsEv, symObjAddr: 0xE14, symBinAddr: 0xAC9E, symSize: 0xE2 }
  - { offsetInCU: 0x745, offset: 0x5B1BA, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21discardPacketFragmentEb, symObjAddr: 0xEF6, symBinAddr: 0xAD80, symSize: 0x56 }
  - { offsetInCU: 0x74D, offset: 0x5B1C2, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14getAddressListEP13IntelAddrData, symObjAddr: 0xF4C, symBinAddr: 0xADD6, symSize: 0x185 }
  - { offsetInCU: 0x77E, offset: 0x5B1F3, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14getAddressListEP13IntelAddrData, symObjAddr: 0xF4C, symBinAddr: 0xADD6, symSize: 0x185 }
  - { offsetInCU: 0x27, offset: 0x5B32B, size: 0x8, addend: 0x0, symName: _e1000e_poll_eerd_eewr_done, symObjAddr: 0x0, symBinAddr: 0xAF5B, symSize: 0x55 }
  - { offsetInCU: 0x29D, offset: 0x5B5A1, size: 0x8, addend: 0x0, symName: _e1000e_poll_eerd_eewr_done, symObjAddr: 0x0, symBinAddr: 0xAF5B, symSize: 0x55 }
  - { offsetInCU: 0x2A5, offset: 0x5B5A9, size: 0x8, addend: 0x0, symName: _e1000e_acquire_nvm, symObjAddr: 0x55, symBinAddr: 0xAFB0, symSize: 0x88 }
  - { offsetInCU: 0x323, offset: 0x5B627, size: 0x8, addend: 0x0, symName: _e1000e_acquire_nvm, symObjAddr: 0x55, symBinAddr: 0xAFB0, symSize: 0x88 }
  - { offsetInCU: 0x32B, offset: 0x5B62F, size: 0x8, addend: 0x0, symName: _e1000e_release_nvm, symObjAddr: 0xDD, symBinAddr: 0xB038, symSize: 0x5F }
  - { offsetInCU: 0x1C23, offset: 0x5CF27, size: 0x8, addend: 0x0, symName: _e1000e_release_nvm, symObjAddr: 0xDD, symBinAddr: 0xB038, symSize: 0x5F }
  - { offsetInCU: 0x1C2B, offset: 0x5CF2F, size: 0x8, addend: 0x0, symName: _e1000e_read_nvm_eerd, symObjAddr: 0x13C, symBinAddr: 0xB097, symSize: 0xA2 }
  - { offsetInCU: 0x1D85, offset: 0x5D089, size: 0x8, addend: 0x0, symName: _e1000e_read_nvm_eerd, symObjAddr: 0x13C, symBinAddr: 0xB097, symSize: 0xA2 }
  - { offsetInCU: 0x1D8D, offset: 0x5D091, size: 0x8, addend: 0x0, symName: _e1000e_write_nvm_spi, symObjAddr: 0x1DE, symBinAddr: 0xB139, symSize: 0x279 }
  - { offsetInCU: 0x1F90, offset: 0x5D294, size: 0x8, addend: 0x0, symName: _e1000e_write_nvm_spi, symObjAddr: 0x1DE, symBinAddr: 0xB139, symSize: 0x279 }
  - { offsetInCU: 0x1F98, offset: 0x5D29C, size: 0x8, addend: 0x0, symName: _e1000_standby_nvm, symObjAddr: 0x457, symBinAddr: 0xB3B2, symSize: 0x6E }
  - { offsetInCU: 0x225E, offset: 0x5D562, size: 0x8, addend: 0x0, symName: _e1000_standby_nvm, symObjAddr: 0x457, symBinAddr: 0xB3B2, symSize: 0x6E }
  - { offsetInCU: 0x2266, offset: 0x5D56A, size: 0x8, addend: 0x0, symName: _e1000_shift_out_eec_bits, symObjAddr: 0x4C5, symBinAddr: 0xB420, symSize: 0xF4 }
  - { offsetInCU: 0x232A, offset: 0x5D62E, size: 0x8, addend: 0x0, symName: _e1000_shift_out_eec_bits, symObjAddr: 0x4C5, symBinAddr: 0xB420, symSize: 0xF4 }
  - { offsetInCU: 0x2332, offset: 0x5D636, size: 0x8, addend: 0x0, symName: _e1000_read_pba_string_generic, symObjAddr: 0x5B9, symBinAddr: 0xB514, symSize: 0x1BB }
  - { offsetInCU: 0x24F2, offset: 0x5D7F6, size: 0x8, addend: 0x0, symName: _e1000_read_pba_string_generic, symObjAddr: 0x5B9, symBinAddr: 0xB514, symSize: 0x1BB }
  - { offsetInCU: 0x24FA, offset: 0x5D7FE, size: 0x8, addend: 0x0, symName: _e1000_read_mac_addr_generic, symObjAddr: 0x774, symBinAddr: 0xB6CF, symSize: 0x58 }
  - { offsetInCU: 0x2687, offset: 0x5D98B, size: 0x8, addend: 0x0, symName: _e1000_read_mac_addr_generic, symObjAddr: 0x774, symBinAddr: 0xB6CF, symSize: 0x58 }
  - { offsetInCU: 0x268F, offset: 0x5D993, size: 0x8, addend: 0x0, symName: _e1000e_validate_nvm_checksum_generic, symObjAddr: 0x7CC, symBinAddr: 0xB727, symSize: 0x68 }
  - { offsetInCU: 0x2754, offset: 0x5DA58, size: 0x8, addend: 0x0, symName: _e1000e_validate_nvm_checksum_generic, symObjAddr: 0x7CC, symBinAddr: 0xB727, symSize: 0x68 }
  - { offsetInCU: 0x275C, offset: 0x5DA60, size: 0x8, addend: 0x0, symName: _e1000e_update_nvm_checksum_generic, symObjAddr: 0x834, symBinAddr: 0xB78F, symSize: 0x79 }
  - { offsetInCU: 0x2869, offset: 0x5DB6D, size: 0x8, addend: 0x0, symName: _e1000e_update_nvm_checksum_generic, symObjAddr: 0x834, symBinAddr: 0xB78F, symSize: 0x79 }
  - { offsetInCU: 0x2871, offset: 0x5DB75, size: 0x8, addend: 0x0, symName: _e1000e_reload_nvm_generic, symObjAddr: 0x8AD, symBinAddr: 0xB808, symSize: 0x3A }
  - { offsetInCU: 0x2964, offset: 0x5DC68, size: 0x8, addend: 0x0, symName: _e1000e_reload_nvm_generic, symObjAddr: 0x8AD, symBinAddr: 0xB808, symSize: 0x3A }
  - { offsetInCU: 0x27, offset: 0x5DCFE, size: 0x8, addend: 0x0, symName: _e1000e_check_mng_mode_generic, symObjAddr: 0x0, symBinAddr: 0xB842, symSize: 0x19 }
  - { offsetInCU: 0x2E4, offset: 0x5DFBB, size: 0x8, addend: 0x0, symName: _e1000e_check_mng_mode_generic, symObjAddr: 0x0, symBinAddr: 0xB842, symSize: 0x19 }
  - { offsetInCU: 0x2EC, offset: 0x5DFC3, size: 0x8, addend: 0x0, symName: _e1000e_enable_tx_pkt_filtering, symObjAddr: 0x19, symBinAddr: 0xB85B, symSize: 0xA2 }
  - { offsetInCU: 0x3A3, offset: 0x5E07A, size: 0x8, addend: 0x0, symName: _e1000e_enable_tx_pkt_filtering, symObjAddr: 0x19, symBinAddr: 0xB85B, symSize: 0xA2 }
  - { offsetInCU: 0x3AB, offset: 0x5E082, size: 0x8, addend: 0x0, symName: _e1000_mng_enable_host_if, symObjAddr: 0xBB, symBinAddr: 0xB8FD, symSize: 0x5D }
  - { offsetInCU: 0x4C8, offset: 0x5E19F, size: 0x8, addend: 0x0, symName: _e1000_mng_enable_host_if, symObjAddr: 0xBB, symBinAddr: 0xB8FD, symSize: 0x5D }
  - { offsetInCU: 0x4D0, offset: 0x5E1A7, size: 0x8, addend: 0x0, symName: _e1000e_mng_write_dhcp_info, symObjAddr: 0x118, symBinAddr: 0xB95A, symSize: 0x179 }
  - { offsetInCU: 0x1EFE, offset: 0x5FBD5, size: 0x8, addend: 0x0, symName: _e1000e_mng_write_dhcp_info, symObjAddr: 0x118, symBinAddr: 0xB95A, symSize: 0x179 }
  - { offsetInCU: 0x27, offset: 0x5FE64, size: 0x8, addend: 0x0, symName: _e1000_read_emi_reg_locked, symObjAddr: 0x0, symBinAddr: 0xBAD4, symSize: 0x3B }
  - { offsetInCU: 0x47, offset: 0x5FE84, size: 0x8, addend: 0x0, symName: _e1000_ich8_info, symObjAddr: 0x5508, symBinAddr: 0x13680, symSize: 0x0 }
  - { offsetInCU: 0x1AE9, offset: 0x61926, size: 0x8, addend: 0x0, symName: _e1000_ich9_info, symObjAddr: 0x5540, symBinAddr: 0x136B8, symSize: 0x0 }
  - { offsetInCU: 0x1B01, offset: 0x6193E, size: 0x8, addend: 0x0, symName: _e1000_ich10_info, symObjAddr: 0x5578, symBinAddr: 0x136F0, symSize: 0x0 }
  - { offsetInCU: 0x1B19, offset: 0x61956, size: 0x8, addend: 0x0, symName: _e1000_pch_info, symObjAddr: 0x55B0, symBinAddr: 0x13728, symSize: 0x0 }
  - { offsetInCU: 0x1B31, offset: 0x6196E, size: 0x8, addend: 0x0, symName: _e1000_pch2_info, symObjAddr: 0x55E8, symBinAddr: 0x13760, symSize: 0x0 }
  - { offsetInCU: 0x1B49, offset: 0x61986, size: 0x8, addend: 0x0, symName: _e1000_pch_lpt_info, symObjAddr: 0x5620, symBinAddr: 0x13798, symSize: 0x0 }
  - { offsetInCU: 0x1B61, offset: 0x6199E, size: 0x8, addend: 0x0, symName: _e1000_pch_spt_info, symObjAddr: 0x5698, symBinAddr: 0x13810, symSize: 0x0 }
  - { offsetInCU: 0x1B79, offset: 0x619B6, size: 0x8, addend: 0x0, symName: _e1000_pch_cnp_info, symObjAddr: 0x56D0, symBinAddr: 0x13848, symSize: 0x0 }
  - { offsetInCU: 0x1B90, offset: 0x619CD, size: 0x8, addend: 0x0, symName: _ich8_mac_ops, symObjAddr: 0x5360, symBinAddr: 0x134D8, symSize: 0x0 }
  - { offsetInCU: 0x1BA7, offset: 0x619E4, size: 0x8, addend: 0x0, symName: _ich8_phy_ops, symObjAddr: 0x5418, symBinAddr: 0x13590, symSize: 0x0 }
  - { offsetInCU: 0x1BBE, offset: 0x619FB, size: 0x8, addend: 0x0, symName: _ich8_nvm_ops, symObjAddr: 0x54C8, symBinAddr: 0x13640, symSize: 0x0 }
  - { offsetInCU: 0x1BD5, offset: 0x61A12, size: 0x8, addend: 0x0, symName: _spt_nvm_ops, symObjAddr: 0x5658, symBinAddr: 0x137D0, symSize: 0x0 }
  - { offsetInCU: 0x1CC1, offset: 0x61AFE, size: 0x8, addend: 0x0, symName: _e1000_read_emi_reg_locked, symObjAddr: 0x0, symBinAddr: 0xBAD4, symSize: 0x3B }
  - { offsetInCU: 0x1CC9, offset: 0x61B06, size: 0x8, addend: 0x0, symName: _e1000_write_emi_reg_locked, symObjAddr: 0x3B, symBinAddr: 0xBB0F, symSize: 0x3C }
  - { offsetInCU: 0x1D77, offset: 0x61BB4, size: 0x8, addend: 0x0, symName: _e1000_write_emi_reg_locked, symObjAddr: 0x3B, symBinAddr: 0xBB0F, symSize: 0x3C }
  - { offsetInCU: 0x1DA3, offset: 0x61BE0, size: 0x8, addend: 0x0, symName: _e1000_write_emi_reg_locked, symObjAddr: 0x3B, symBinAddr: 0xBB0F, symSize: 0x3C }
  - { offsetInCU: 0x1DAB, offset: 0x61BE8, size: 0x8, addend: 0x0, symName: _e1000_set_eee_pchlan, symObjAddr: 0x77, symBinAddr: 0xBB4B, symSize: 0x22F }
  - { offsetInCU: 0x1E4F, offset: 0x61C8C, size: 0x8, addend: 0x0, symName: _e1000_set_eee_pchlan, symObjAddr: 0x77, symBinAddr: 0xBB4B, symSize: 0x22F }
  - { offsetInCU: 0x1F01, offset: 0x61D3E, size: 0x8, addend: 0x0, symName: _e1000_set_eee_pchlan, symObjAddr: 0x77, symBinAddr: 0xBB4B, symSize: 0x22F }
  - { offsetInCU: 0x1F09, offset: 0x61D46, size: 0x8, addend: 0x0, symName: _e1000_enable_ulp_lpt_lp, symObjAddr: 0x2A6, symBinAddr: 0xBD7A, symSize: 0x29B }
  - { offsetInCU: 0x24DA, offset: 0x62317, size: 0x8, addend: 0x0, symName: _e1000_enable_ulp_lpt_lp, symObjAddr: 0x2A6, symBinAddr: 0xBD7A, symSize: 0x29B }
  - { offsetInCU: 0x24E2, offset: 0x6231F, size: 0x8, addend: 0x0, symName: _e1000_configure_k1_ich8lan, symObjAddr: 0x541, symBinAddr: 0xC015, symSize: 0xF6 }
  - { offsetInCU: 0x25E4, offset: 0x62421, size: 0x8, addend: 0x0, symName: _e1000_configure_k1_ich8lan, symObjAddr: 0x541, symBinAddr: 0xC015, symSize: 0xF6 }
  - { offsetInCU: 0x25EC, offset: 0x62429, size: 0x8, addend: 0x0, symName: _e1000_copy_rx_addrs_to_phy_ich8lan, symObjAddr: 0x637, symBinAddr: 0xC10B, symSize: 0x1A2 }
  - { offsetInCU: 0x273A, offset: 0x62577, size: 0x8, addend: 0x0, symName: _e1000_copy_rx_addrs_to_phy_ich8lan, symObjAddr: 0x637, symBinAddr: 0xC10B, symSize: 0x1A2 }
  - { offsetInCU: 0x2742, offset: 0x6257F, size: 0x8, addend: 0x0, symName: _e1000_lv_jumbo_workaround_ich8lan, symObjAddr: 0x7D9, symBinAddr: 0xC2AD, symSize: 0x4E1 }
  - { offsetInCU: 0x28EF, offset: 0x6272C, size: 0x8, addend: 0x0, symName: _e1000_lv_jumbo_workaround_ich8lan, symObjAddr: 0x7D9, symBinAddr: 0xC2AD, symSize: 0x4E1 }
  - { offsetInCU: 0x28F7, offset: 0x62734, size: 0x8, addend: 0x0, symName: _e1000e_write_protect_nvm_ich8lan, symObjAddr: 0xCBA, symBinAddr: 0xC78E, symSize: 0x49 }
  - { offsetInCU: 0x2E7C, offset: 0x62CB9, size: 0x8, addend: 0x0, symName: _e1000e_write_protect_nvm_ich8lan, symObjAddr: 0xCBA, symBinAddr: 0xC78E, symSize: 0x49 }
  - { offsetInCU: 0x2F05, offset: 0x62D42, size: 0x8, addend: 0x0, symName: _e1000e_write_protect_nvm_ich8lan, symObjAddr: 0xCBA, symBinAddr: 0xC78E, symSize: 0x49 }
  - { offsetInCU: 0x2F0D, offset: 0x62D4A, size: 0x8, addend: 0x0, symName: _e1000e_set_kmrn_lock_loss_workaround_ich8lan, symObjAddr: 0xD03, symBinAddr: 0xC7D7, symSize: 0x16 }
  - { offsetInCU: 0x3007, offset: 0x62E44, size: 0x8, addend: 0x0, symName: _e1000e_set_kmrn_lock_loss_workaround_ich8lan, symObjAddr: 0xD03, symBinAddr: 0xC7D7, symSize: 0x16 }
  - { offsetInCU: 0x300F, offset: 0x62E4C, size: 0x8, addend: 0x0, symName: _e1000e_igp3_phy_powerdown_workaround_ich8lan, symObjAddr: 0xD19, symBinAddr: 0xC7ED, symSize: 0xE9 }
  - { offsetInCU: 0x303C, offset: 0x62E79, size: 0x8, addend: 0x0, symName: _e1000e_igp3_phy_powerdown_workaround_ich8lan, symObjAddr: 0xD19, symBinAddr: 0xC7ED, symSize: 0xE9 }
  - { offsetInCU: 0x3044, offset: 0x62E81, size: 0x8, addend: 0x0, symName: _e1000e_gig_downshift_workaround_ich8lan, symObjAddr: 0xE02, symBinAddr: 0xC8D6, symSize: 0x77 }
  - { offsetInCU: 0x3166, offset: 0x62FA3, size: 0x8, addend: 0x0, symName: _e1000e_gig_downshift_workaround_ich8lan, symObjAddr: 0xE02, symBinAddr: 0xC8D6, symSize: 0x77 }
  - { offsetInCU: 0x316E, offset: 0x62FAB, size: 0x8, addend: 0x0, symName: _e1000_suspend_workarounds_ich8lan, symObjAddr: 0xE79, symBinAddr: 0xC94D, symSize: 0x265 }
  - { offsetInCU: 0x31C0, offset: 0x62FFD, size: 0x8, addend: 0x0, symName: _e1000_suspend_workarounds_ich8lan, symObjAddr: 0xE79, symBinAddr: 0xC94D, symSize: 0x265 }
  - { offsetInCU: 0x31C8, offset: 0x63005, size: 0x8, addend: 0x0, symName: _e1000_oem_bits_config_ich8lan, symObjAddr: 0x10DE, symBinAddr: 0xCBB2, symSize: 0x12D }
  - { offsetInCU: 0x35A1, offset: 0x633DE, size: 0x8, addend: 0x0, symName: _e1000_oem_bits_config_ich8lan, symObjAddr: 0x10DE, symBinAddr: 0xCBB2, symSize: 0x12D }
  - { offsetInCU: 0x35A9, offset: 0x633E6, size: 0x8, addend: 0x0, symName: _e1000_write_smbus_addr, symObjAddr: 0x120B, symBinAddr: 0xCCDF, symSize: 0x9E }
  - { offsetInCU: 0x36C0, offset: 0x634FD, size: 0x8, addend: 0x0, symName: _e1000_write_smbus_addr, symObjAddr: 0x120B, symBinAddr: 0xCCDF, symSize: 0x9E }
  - { offsetInCU: 0x36C8, offset: 0x63505, size: 0x8, addend: 0x0, symName: _e1000_resume_workarounds_pchlan, symObjAddr: 0x12A9, symBinAddr: 0xCD7D, symSize: 0x10B }
  - { offsetInCU: 0x3771, offset: 0x635AE, size: 0x8, addend: 0x0, symName: _e1000_resume_workarounds_pchlan, symObjAddr: 0x12A9, symBinAddr: 0xCD7D, symSize: 0x10B }
  - { offsetInCU: 0x3779, offset: 0x635B6, size: 0x8, addend: 0x0, symName: _e1000_init_phy_workarounds_pchlan, symObjAddr: 0x13B4, symBinAddr: 0xCE88, symSize: 0x427 }
  - { offsetInCU: 0x3A12, offset: 0x6384F, size: 0x8, addend: 0x0, symName: _e1000_init_phy_workarounds_pchlan, symObjAddr: 0x13B4, symBinAddr: 0xCE88, symSize: 0x427 }
  - { offsetInCU: 0x3A1A, offset: 0x63857, size: 0x8, addend: 0x0, symName: _e1000_get_variants_ich8lan, symObjAddr: 0x17DB, symBinAddr: 0xD2AF, symSize: 0x6EF }
  - { offsetInCU: 0x3D76, offset: 0x63BB3, size: 0x8, addend: 0x0, symName: _e1000_get_variants_ich8lan, symObjAddr: 0x17DB, symBinAddr: 0xD2AF, symSize: 0x6EF }
  - { offsetInCU: 0x3D7E, offset: 0x63BBB, size: 0x8, addend: 0x0, symName: _e1000_phy_is_accessible_pchlan, symObjAddr: 0x1ECA, symBinAddr: 0xD99E, symSize: 0x168 }
  - { offsetInCU: 0x3F19, offset: 0x63D56, size: 0x8, addend: 0x0, symName: _e1000_phy_is_accessible_pchlan, symObjAddr: 0x1ECA, symBinAddr: 0xD99E, symSize: 0x168 }
  - { offsetInCU: 0x3F21, offset: 0x63D5E, size: 0x8, addend: 0x0, symName: _e1000_toggle_lanphypc_pch_lpt, symObjAddr: 0x2032, symBinAddr: 0xDB06, symSize: 0xAE }
  - { offsetInCU: 0x4072, offset: 0x63EAF, size: 0x8, addend: 0x0, symName: _e1000_toggle_lanphypc_pch_lpt, symObjAddr: 0x2032, symBinAddr: 0xDB06, symSize: 0xAE }
  - { offsetInCU: 0x407A, offset: 0x63EB7, size: 0x8, addend: 0x0, symName: _e1000_set_mdio_slow_mode_hv, symObjAddr: 0x20E0, symBinAddr: 0xDBB4, symSize: 0x44 }
  - { offsetInCU: 0x412D, offset: 0x63F6A, size: 0x8, addend: 0x0, symName: _e1000_set_mdio_slow_mode_hv, symObjAddr: 0x20E0, symBinAddr: 0xDBB4, symSize: 0x44 }
  - { offsetInCU: 0x4135, offset: 0x63F72, size: 0x8, addend: 0x0, symName: _e1000_check_mng_mode_ich8lan, symObjAddr: 0x2124, symBinAddr: 0xDBF8, symSize: 0x1E }
  - { offsetInCU: 0x41EA, offset: 0x64027, size: 0x8, addend: 0x0, symName: _e1000_check_mng_mode_ich8lan, symObjAddr: 0x2124, symBinAddr: 0xDBF8, symSize: 0x1E }
  - { offsetInCU: 0x41F2, offset: 0x6402F, size: 0x8, addend: 0x0, symName: _e1000_cleanup_led_ich8lan, symObjAddr: 0x2142, symBinAddr: 0xDC16, symSize: 0x32 }
  - { offsetInCU: 0x4230, offset: 0x6406D, size: 0x8, addend: 0x0, symName: _e1000_cleanup_led_ich8lan, symObjAddr: 0x2142, symBinAddr: 0xDC16, symSize: 0x32 }
  - { offsetInCU: 0x4238, offset: 0x64075, size: 0x8, addend: 0x0, symName: _e1000_led_on_ich8lan, symObjAddr: 0x2174, symBinAddr: 0xDC48, symSize: 0x35 }
  - { offsetInCU: 0x4298, offset: 0x640D5, size: 0x8, addend: 0x0, symName: _e1000_led_on_ich8lan, symObjAddr: 0x2174, symBinAddr: 0xDC48, symSize: 0x35 }
  - { offsetInCU: 0x42A0, offset: 0x640DD, size: 0x8, addend: 0x0, symName: _e1000_led_off_ich8lan, symObjAddr: 0x21A9, symBinAddr: 0xDC7D, symSize: 0x35 }
  - { offsetInCU: 0x4300, offset: 0x6413D, size: 0x8, addend: 0x0, symName: _e1000_led_off_ich8lan, symObjAddr: 0x21A9, symBinAddr: 0xDC7D, symSize: 0x35 }
  - { offsetInCU: 0x4308, offset: 0x64145, size: 0x8, addend: 0x0, symName: _e1000_rar_set_pch2lan, symObjAddr: 0x21DE, symBinAddr: 0xDCB2, symSize: 0xF9 }
  - { offsetInCU: 0x4368, offset: 0x641A5, size: 0x8, addend: 0x0, symName: _e1000_rar_set_pch2lan, symObjAddr: 0x21DE, symBinAddr: 0xDCB2, symSize: 0xF9 }
  - { offsetInCU: 0x4370, offset: 0x641AD, size: 0x8, addend: 0x0, symName: _e1000_check_mng_mode_pchlan, symObjAddr: 0x22D7, symBinAddr: 0xDDAB, symSize: 0x1A }
  - { offsetInCU: 0x451C, offset: 0x64359, size: 0x8, addend: 0x0, symName: _e1000_check_mng_mode_pchlan, symObjAddr: 0x22D7, symBinAddr: 0xDDAB, symSize: 0x1A }
  - { offsetInCU: 0x4524, offset: 0x64361, size: 0x8, addend: 0x0, symName: _e1000_id_led_init_pchlan, symObjAddr: 0x22F1, symBinAddr: 0xDDC5, symSize: 0x13F }
  - { offsetInCU: 0x458E, offset: 0x643CB, size: 0x8, addend: 0x0, symName: _e1000_id_led_init_pchlan, symObjAddr: 0x22F1, symBinAddr: 0xDDC5, symSize: 0x13F }
  - { offsetInCU: 0x4682, offset: 0x644BF, size: 0x8, addend: 0x0, symName: _e1000_setup_led_pchlan, symObjAddr: 0x2430, symBinAddr: 0xDF04, symSize: 0x1A }
  - { offsetInCU: 0x468A, offset: 0x644C7, size: 0x8, addend: 0x0, symName: _e1000_cleanup_led_pchlan, symObjAddr: 0x244A, symBinAddr: 0xDF1E, symSize: 0x1A }
  - { offsetInCU: 0x46C2, offset: 0x644FF, size: 0x8, addend: 0x0, symName: _e1000_cleanup_led_pchlan, symObjAddr: 0x244A, symBinAddr: 0xDF1E, symSize: 0x1A }
  - { offsetInCU: 0x46E3, offset: 0x64520, size: 0x8, addend: 0x0, symName: _e1000_cleanup_led_pchlan, symObjAddr: 0x244A, symBinAddr: 0xDF1E, symSize: 0x1A }
  - { offsetInCU: 0x46EB, offset: 0x64528, size: 0x8, addend: 0x0, symName: _e1000_led_on_pchlan, symObjAddr: 0x2464, symBinAddr: 0xDF38, symSize: 0x58 }
  - { offsetInCU: 0x4723, offset: 0x64560, size: 0x8, addend: 0x0, symName: _e1000_led_on_pchlan, symObjAddr: 0x2464, symBinAddr: 0xDF38, symSize: 0x58 }
  - { offsetInCU: 0x4744, offset: 0x64581, size: 0x8, addend: 0x0, symName: _e1000_led_on_pchlan, symObjAddr: 0x2464, symBinAddr: 0xDF38, symSize: 0x58 }
  - { offsetInCU: 0x474C, offset: 0x64589, size: 0x8, addend: 0x0, symName: _e1000_led_off_pchlan, symObjAddr: 0x24BC, symBinAddr: 0xDF90, symSize: 0x58 }
  - { offsetInCU: 0x47C3, offset: 0x64600, size: 0x8, addend: 0x0, symName: _e1000_led_off_pchlan, symObjAddr: 0x24BC, symBinAddr: 0xDF90, symSize: 0x58 }
  - { offsetInCU: 0x47EC, offset: 0x64629, size: 0x8, addend: 0x0, symName: _e1000_led_off_pchlan, symObjAddr: 0x24BC, symBinAddr: 0xDF90, symSize: 0x58 }
  - { offsetInCU: 0x47F4, offset: 0x64631, size: 0x8, addend: 0x0, symName: _e1000_rar_set_pch_lpt, symObjAddr: 0x2514, symBinAddr: 0xDFE8, symSize: 0x11D }
  - { offsetInCU: 0x486B, offset: 0x646A8, size: 0x8, addend: 0x0, symName: _e1000_rar_set_pch_lpt, symObjAddr: 0x2514, symBinAddr: 0xDFE8, symSize: 0x11D }
  - { offsetInCU: 0x4894, offset: 0x646D1, size: 0x8, addend: 0x0, symName: _e1000_rar_set_pch_lpt, symObjAddr: 0x2514, symBinAddr: 0xDFE8, symSize: 0x11D }
  - { offsetInCU: 0x489C, offset: 0x646D9, size: 0x8, addend: 0x0, symName: _e1000_setup_copper_link_pch_lpt, symObjAddr: 0x2631, symBinAddr: 0xE105, symSize: 0x3F }
  - { offsetInCU: 0x4A89, offset: 0x648C6, size: 0x8, addend: 0x0, symName: _e1000_setup_copper_link_pch_lpt, symObjAddr: 0x2631, symBinAddr: 0xE105, symSize: 0x3F }
  - { offsetInCU: 0x4A91, offset: 0x648CE, size: 0x8, addend: 0x0, symName: _e1000_rar_get_count_pch_lpt, symObjAddr: 0x2670, symBinAddr: 0xE144, symSize: 0x2A }
  - { offsetInCU: 0x4AEA, offset: 0x64927, size: 0x8, addend: 0x0, symName: _e1000_rar_get_count_pch_lpt, symObjAddr: 0x2670, symBinAddr: 0xE144, symSize: 0x2A }
  - { offsetInCU: 0x4AF2, offset: 0x6492F, size: 0x8, addend: 0x0, symName: _e1000_acquire_swflag_ich8lan, symObjAddr: 0x269A, symBinAddr: 0xE16E, symSize: 0xC2 }
  - { offsetInCU: 0x4BCC, offset: 0x64A09, size: 0x8, addend: 0x0, symName: _e1000_acquire_swflag_ich8lan, symObjAddr: 0x269A, symBinAddr: 0xE16E, symSize: 0xC2 }
  - { offsetInCU: 0x4BD4, offset: 0x64A11, size: 0x8, addend: 0x0, symName: _e1000_release_swflag_ich8lan, symObjAddr: 0x275C, symBinAddr: 0xE230, symSize: 0x3F }
  - { offsetInCU: 0x4D01, offset: 0x64B3E, size: 0x8, addend: 0x0, symName: _e1000_release_swflag_ich8lan, symObjAddr: 0x275C, symBinAddr: 0xE230, symSize: 0x3F }
  - { offsetInCU: 0x4D09, offset: 0x64B46, size: 0x8, addend: 0x0, symName: _e1000_power_down_phy_copper_ich8lan, symObjAddr: 0x279B, symBinAddr: 0xE26F, symSize: 0x32 }
  - { offsetInCU: 0x4D7B, offset: 0x64BB8, size: 0x8, addend: 0x0, symName: _e1000_power_down_phy_copper_ich8lan, symObjAddr: 0x279B, symBinAddr: 0xE26F, symSize: 0x32 }
  - { offsetInCU: 0x4D9A, offset: 0x64BD7, size: 0x8, addend: 0x0, symName: _e1000_power_down_phy_copper_ich8lan, symObjAddr: 0x279B, symBinAddr: 0xE26F, symSize: 0x32 }
  - { offsetInCU: 0x4DA2, offset: 0x64BDF, size: 0x8, addend: 0x0, symName: _e1000_set_lplu_state_pchlan, symObjAddr: 0x27CD, symBinAddr: 0xE2A1, symSize: 0x70 }
  - { offsetInCU: 0x4DC9, offset: 0x64C06, size: 0x8, addend: 0x0, symName: _e1000_set_lplu_state_pchlan, symObjAddr: 0x27CD, symBinAddr: 0xE2A1, symSize: 0x70 }
  - { offsetInCU: 0x4DD1, offset: 0x64C0E, size: 0x8, addend: 0x0, symName: _e1000_check_for_copper_link_ich8lan, symObjAddr: 0x283D, symBinAddr: 0xE311, symSize: 0x6E6 }
  - { offsetInCU: 0x5023, offset: 0x64E60, size: 0x8, addend: 0x0, symName: _e1000_check_for_copper_link_ich8lan, symObjAddr: 0x283D, symBinAddr: 0xE311, symSize: 0x6E6 }
  - { offsetInCU: 0x502B, offset: 0x64E68, size: 0x8, addend: 0x0, symName: _e1000_clear_hw_cntrs_ich8lan, symObjAddr: 0x2F23, symBinAddr: 0xE9F7, symSize: 0x19C }
  - { offsetInCU: 0x56A3, offset: 0x654E0, size: 0x8, addend: 0x0, symName: _e1000_clear_hw_cntrs_ich8lan, symObjAddr: 0x2F23, symBinAddr: 0xE9F7, symSize: 0x19C }
  - { offsetInCU: 0x56AB, offset: 0x654E8, size: 0x8, addend: 0x0, symName: _e1000_get_bus_info_ich8lan, symObjAddr: 0x30BF, symBinAddr: 0xEB93, symSize: 0x28 }
  - { offsetInCU: 0x58F1, offset: 0x6572E, size: 0x8, addend: 0x0, symName: _e1000_get_bus_info_ich8lan, symObjAddr: 0x30BF, symBinAddr: 0xEB93, symSize: 0x28 }
  - { offsetInCU: 0x58F9, offset: 0x65736, size: 0x8, addend: 0x0, symName: _e1000_get_link_up_info_ich8lan, symObjAddr: 0x30E7, symBinAddr: 0xEBBB, symSize: 0x11F }
  - { offsetInCU: 0x59CA, offset: 0x65807, size: 0x8, addend: 0x0, symName: _e1000_get_link_up_info_ich8lan, symObjAddr: 0x30E7, symBinAddr: 0xEBBB, symSize: 0x11F }
  - { offsetInCU: 0x59D2, offset: 0x6580F, size: 0x8, addend: 0x0, symName: _e1000_reset_hw_ich8lan, symObjAddr: 0x3206, symBinAddr: 0xECDA, symSize: 0x1EC }
  - { offsetInCU: 0x5B96, offset: 0x659D3, size: 0x8, addend: 0x0, symName: _e1000_reset_hw_ich8lan, symObjAddr: 0x3206, symBinAddr: 0xECDA, symSize: 0x1EC }
  - { offsetInCU: 0x5B9E, offset: 0x659DB, size: 0x8, addend: 0x0, symName: _e1000_init_hw_ich8lan, symObjAddr: 0x33F2, symBinAddr: 0xEEC6, symSize: 0x28B }
  - { offsetInCU: 0x5D6E, offset: 0x65BAB, size: 0x8, addend: 0x0, symName: _e1000_init_hw_ich8lan, symObjAddr: 0x33F2, symBinAddr: 0xEEC6, symSize: 0x28B }
  - { offsetInCU: 0x5D76, offset: 0x65BB3, size: 0x8, addend: 0x0, symName: _e1000_setup_link_ich8lan, symObjAddr: 0x367D, symBinAddr: 0xF151, symSize: 0xB1 }
  - { offsetInCU: 0x5F21, offset: 0x65D5E, size: 0x8, addend: 0x0, symName: _e1000_setup_link_ich8lan, symObjAddr: 0x367D, symBinAddr: 0xF151, symSize: 0xB1 }
  - { offsetInCU: 0x5F29, offset: 0x65D66, size: 0x8, addend: 0x0, symName: _e1000_setup_copper_link_ich8lan, symObjAddr: 0x372E, symBinAddr: 0xF202, symSize: 0x13A }
  - { offsetInCU: 0x5F99, offset: 0x65DD6, size: 0x8, addend: 0x0, symName: _e1000_setup_copper_link_ich8lan, symObjAddr: 0x372E, symBinAddr: 0xF202, symSize: 0x13A }
  - { offsetInCU: 0x6071, offset: 0x65EAE, size: 0x8, addend: 0x0, symName: _e1000_k1_gig_workaround_hv, symObjAddr: 0x3868, symBinAddr: 0xF33C, symSize: 0x121 }
  - { offsetInCU: 0x6079, offset: 0x65EB6, size: 0x8, addend: 0x0, symName: _e1000_k1_workaround_lv, symObjAddr: 0x3989, symBinAddr: 0xF45D, symSize: 0xA8 }
  - { offsetInCU: 0x61CA, offset: 0x66007, size: 0x8, addend: 0x0, symName: _e1000_k1_workaround_lv, symObjAddr: 0x3989, symBinAddr: 0xF45D, symSize: 0xA8 }
  - { offsetInCU: 0x61D2, offset: 0x6600F, size: 0x8, addend: 0x0, symName: _e1000_post_phy_reset_ich8lan, symObjAddr: 0x3A31, symBinAddr: 0xF505, symSize: 0x527 }
  - { offsetInCU: 0x649C, offset: 0x662D9, size: 0x8, addend: 0x0, symName: _e1000_post_phy_reset_ich8lan, symObjAddr: 0x3A31, symBinAddr: 0xF505, symSize: 0x527 }
  - { offsetInCU: 0x64A4, offset: 0x662E1, size: 0x8, addend: 0x0, symName: _e1000_phy_hw_reset_ich8lan, symObjAddr: 0x3F58, symBinAddr: 0xFA2C, symSize: 0x59 }
  - { offsetInCU: 0x6BE1, offset: 0x66A1E, size: 0x8, addend: 0x0, symName: _e1000_phy_hw_reset_ich8lan, symObjAddr: 0x3F58, symBinAddr: 0xFA2C, symSize: 0x59 }
  - { offsetInCU: 0x6BE9, offset: 0x66A26, size: 0x8, addend: 0x0, symName: _e1000_check_reset_block_ich8lan, symObjAddr: 0x3FB1, symBinAddr: 0xFA85, symSize: 0x58 }
  - { offsetInCU: 0x6C62, offset: 0x66A9F, size: 0x8, addend: 0x0, symName: _e1000_check_reset_block_ich8lan, symObjAddr: 0x3FB1, symBinAddr: 0xFA85, symSize: 0x58 }
  - { offsetInCU: 0x6C6A, offset: 0x66AA7, size: 0x8, addend: 0x0, symName: _e1000_get_cfg_done_ich8lan, symObjAddr: 0x4009, symBinAddr: 0xFADD, symSize: 0xDC }
  - { offsetInCU: 0x6CF7, offset: 0x66B34, size: 0x8, addend: 0x0, symName: _e1000_get_cfg_done_ich8lan, symObjAddr: 0x4009, symBinAddr: 0xFADD, symSize: 0xDC }
  - { offsetInCU: 0x6CFF, offset: 0x66B3C, size: 0x8, addend: 0x0, symName: _e1000_set_d0_lplu_state_ich8lan, symObjAddr: 0x40E5, symBinAddr: 0xFBB9, symSize: 0x105 }
  - { offsetInCU: 0x6DCA, offset: 0x66C07, size: 0x8, addend: 0x0, symName: _e1000_set_d0_lplu_state_ich8lan, symObjAddr: 0x40E5, symBinAddr: 0xFBB9, symSize: 0x105 }
  - { offsetInCU: 0x6DD2, offset: 0x66C0F, size: 0x8, addend: 0x0, symName: _e1000_set_d3_lplu_state_ich8lan, symObjAddr: 0x41EA, symBinAddr: 0xFCBE, symSize: 0x14E }
  - { offsetInCU: 0x6EEA, offset: 0x66D27, size: 0x8, addend: 0x0, symName: _e1000_set_d3_lplu_state_ich8lan, symObjAddr: 0x41EA, symBinAddr: 0xFCBE, symSize: 0x14E }
  - { offsetInCU: 0x6EF2, offset: 0x66D2F, size: 0x8, addend: 0x0, symName: _e1000_valid_nvm_bank_detect_ich8lan, symObjAddr: 0x4338, symBinAddr: 0xFE0C, symSize: 0x171 }
  - { offsetInCU: 0x7123, offset: 0x66F60, size: 0x8, addend: 0x0, symName: _e1000_valid_nvm_bank_detect_ich8lan, symObjAddr: 0x4338, symBinAddr: 0xFE0C, symSize: 0x171 }
  - { offsetInCU: 0x712B, offset: 0x66F68, size: 0x8, addend: 0x0, symName: _e1000_read_flash_dword_ich8lan, symObjAddr: 0x44A9, symBinAddr: 0xFF7D, symSize: 0xBD }
  - { offsetInCU: 0x74BE, offset: 0x672FB, size: 0x8, addend: 0x0, symName: _e1000_read_flash_dword_ich8lan, symObjAddr: 0x44A9, symBinAddr: 0xFF7D, symSize: 0xBD }
  - { offsetInCU: 0x74C6, offset: 0x67303, size: 0x8, addend: 0x0, symName: _e1000_flash_cycle_init_ich8lan, symObjAddr: 0x4566, symBinAddr: 0x1003A, symSize: 0x92 }
  - { offsetInCU: 0x7662, offset: 0x6749F, size: 0x8, addend: 0x0, symName: _e1000_flash_cycle_init_ich8lan, symObjAddr: 0x4566, symBinAddr: 0x1003A, symSize: 0x92 }
  - { offsetInCU: 0x766A, offset: 0x674A7, size: 0x8, addend: 0x0, symName: _e1000_flash_cycle_ich8lan, symObjAddr: 0x45F8, symBinAddr: 0x100CC, symSize: 0x64 }
  - { offsetInCU: 0x779B, offset: 0x675D8, size: 0x8, addend: 0x0, symName: _e1000_flash_cycle_ich8lan, symObjAddr: 0x45F8, symBinAddr: 0x100CC, symSize: 0x64 }
  - { offsetInCU: 0x77A3, offset: 0x675E0, size: 0x8, addend: 0x0, symName: _e1000_read_flash_data_ich8lan, symObjAddr: 0x465C, symBinAddr: 0x10130, symSize: 0xC9 }
  - { offsetInCU: 0x78A5, offset: 0x676E2, size: 0x8, addend: 0x0, symName: _e1000_read_flash_data_ich8lan, symObjAddr: 0x465C, symBinAddr: 0x10130, symSize: 0xC9 }
  - { offsetInCU: 0x78AD, offset: 0x676EA, size: 0x8, addend: 0x0, symName: _e1000_acquire_nvm_ich8lan, symObjAddr: 0x4725, symBinAddr: 0x101F9, symSize: 0x8 }
  - { offsetInCU: 0x7A6F, offset: 0x678AC, size: 0x8, addend: 0x0, symName: _e1000_acquire_nvm_ich8lan, symObjAddr: 0x4725, symBinAddr: 0x101F9, symSize: 0x8 }
  - { offsetInCU: 0x7A77, offset: 0x678B4, size: 0x8, addend: 0x0, symName: _e1000_read_nvm_ich8lan, symObjAddr: 0x472D, symBinAddr: 0x10201, symSize: 0x10D }
  - { offsetInCU: 0x7AE2, offset: 0x6791F, size: 0x8, addend: 0x0, symName: _e1000_read_nvm_ich8lan, symObjAddr: 0x472D, symBinAddr: 0x10201, symSize: 0x10D }
  - { offsetInCU: 0x7AEA, offset: 0x67927, size: 0x8, addend: 0x0, symName: _e1000_release_nvm_ich8lan, symObjAddr: 0x483A, symBinAddr: 0x1030E, symSize: 0x6 }
  - { offsetInCU: 0x7C1A, offset: 0x67A57, size: 0x8, addend: 0x0, symName: _e1000_release_nvm_ich8lan, symObjAddr: 0x483A, symBinAddr: 0x1030E, symSize: 0x6 }
  - { offsetInCU: 0x7C22, offset: 0x67A5F, size: 0x8, addend: 0x0, symName: _e1000_update_nvm_checksum_ich8lan, symObjAddr: 0x4840, symBinAddr: 0x10314, symSize: 0x22E }
  - { offsetInCU: 0x7C45, offset: 0x67A82, size: 0x8, addend: 0x0, symName: _e1000_update_nvm_checksum_ich8lan, symObjAddr: 0x4840, symBinAddr: 0x10314, symSize: 0x22E }
  - { offsetInCU: 0x7C4D, offset: 0x67A8A, size: 0x8, addend: 0x0, symName: _e1000_valid_led_default_ich8lan, symObjAddr: 0x4A6E, symBinAddr: 0x10542, symSize: 0x3B }
  - { offsetInCU: 0x7DAD, offset: 0x67BEA, size: 0x8, addend: 0x0, symName: _e1000_valid_led_default_ich8lan, symObjAddr: 0x4A6E, symBinAddr: 0x10542, symSize: 0x3B }
  - { offsetInCU: 0x7DB5, offset: 0x67BF2, size: 0x8, addend: 0x0, symName: _e1000_validate_nvm_checksum_ich8lan, symObjAddr: 0x4AA9, symBinAddr: 0x1057D, symSize: 0x9D }
  - { offsetInCU: 0x7EB8, offset: 0x67CF5, size: 0x8, addend: 0x0, symName: _e1000_validate_nvm_checksum_ich8lan, symObjAddr: 0x4AA9, symBinAddr: 0x1057D, symSize: 0x9D }
  - { offsetInCU: 0x7EC0, offset: 0x67CFD, size: 0x8, addend: 0x0, symName: _e1000_write_nvm_ich8lan, symObjAddr: 0x4B46, symBinAddr: 0x1061A, symSize: 0x77 }
  - { offsetInCU: 0x7FD6, offset: 0x67E13, size: 0x8, addend: 0x0, symName: _e1000_write_nvm_ich8lan, symObjAddr: 0x4B46, symBinAddr: 0x1061A, symSize: 0x77 }
  - { offsetInCU: 0x7FDE, offset: 0x67E1B, size: 0x8, addend: 0x0, symName: _e1000_erase_flash_bank_ich8lan, symObjAddr: 0x4BBD, symBinAddr: 0x10691, symSize: 0x157 }
  - { offsetInCU: 0x807F, offset: 0x67EBC, size: 0x8, addend: 0x0, symName: _e1000_erase_flash_bank_ich8lan, symObjAddr: 0x4BBD, symBinAddr: 0x10691, symSize: 0x157 }
  - { offsetInCU: 0x8255, offset: 0x68092, size: 0x8, addend: 0x0, symName: _e1000_retry_write_flash_byte_ich8lan, symObjAddr: 0x4D14, symBinAddr: 0x107E8, symSize: 0x66 }
  - { offsetInCU: 0x825D, offset: 0x6809A, size: 0x8, addend: 0x0, symName: _e1000_write_flash_byte_ich8lan, symObjAddr: 0x4D7A, symBinAddr: 0x1084E, symSize: 0xCC }
  - { offsetInCU: 0x8393, offset: 0x681D0, size: 0x8, addend: 0x0, symName: _e1000_write_flash_byte_ich8lan, symObjAddr: 0x4D7A, symBinAddr: 0x1084E, symSize: 0xCC }
  - { offsetInCU: 0x839B, offset: 0x681D8, size: 0x8, addend: 0x0, symName: _e1000_read_nvm_spt, symObjAddr: 0x4E46, symBinAddr: 0x1091A, symSize: 0x1C1 }
  - { offsetInCU: 0x858A, offset: 0x683C7, size: 0x8, addend: 0x0, symName: _e1000_read_nvm_spt, symObjAddr: 0x4E46, symBinAddr: 0x1091A, symSize: 0x1C1 }
  - { offsetInCU: 0x8592, offset: 0x683CF, size: 0x8, addend: 0x0, symName: _e1000_update_nvm_checksum_spt, symObjAddr: 0x5007, symBinAddr: 0x10ADB, symSize: 0x219 }
  - { offsetInCU: 0x86A7, offset: 0x684E4, size: 0x8, addend: 0x0, symName: _e1000_update_nvm_checksum_spt, symObjAddr: 0x5007, symBinAddr: 0x10ADB, symSize: 0x219 }
  - { offsetInCU: 0x86AF, offset: 0x684EC, size: 0x8, addend: 0x0, symName: _e1000_retry_write_flash_dword_ich8lan, symObjAddr: 0x5220, symBinAddr: 0x10CF4, symSize: 0x68 }
  - { offsetInCU: 0x87AD, offset: 0x685EA, size: 0x8, addend: 0x0, symName: _e1000_retry_write_flash_dword_ich8lan, symObjAddr: 0x5220, symBinAddr: 0x10CF4, symSize: 0x68 }
  - { offsetInCU: 0x87B5, offset: 0x685F2, size: 0x8, addend: 0x0, symName: _e1000_write_flash_data32_ich8lan, symObjAddr: 0x5288, symBinAddr: 0x10D5C, symSize: 0xD7 }
  - { offsetInCU: 0x8838, offset: 0x68675, size: 0x8, addend: 0x0, symName: _e1000_write_flash_data32_ich8lan, symObjAddr: 0x5288, symBinAddr: 0x10D5C, symSize: 0xD7 }
...
