# BIOS Settings for ASUS ROG Strix Z390-E Gaming

## Required BIOS Settings for macOS Big Sur Hackintosh

### Access BIOS
1. Power on the system and press **DEL** or **F2** during boot
2. Enter **Advanced Mode** (F7)
3. Load **Optimized Defaults** first, then apply the following changes

---

## Settings to DISABLE ❌

### Boot Menu
- **Fast Boot** → Disabled
- **Launch CSM** → Disabled

### Security
- **Secure Boot** → Disabled
  - Navigate to **Key Management**
  - Select **Clear Secure Boot Keys**
  - Confirm the action

### Advanced → System Agent (SA) Configuration
- **VT-d** → Disabled

### Advanced → Onboard Devices Configuration
- **Serial Port Configuration**
  - **Serial Port** → Off

### Advanced → CPU Configuration (if available)
- **CFG Lock** → Disabled
  - *Note: This option may not be visible in all BIOS versions*
  - *If not available, the OpenCore configuration handles this*

---

## Settings to ENABLE ✅

### Advanced → System Agent (SA) Configuration
- **Above 4G Decoding** → Enabled

### Advanced → System Agent (SA) Configuration → Graphics Configuration
- **Primary Display** → CPU Graphics (for iGPU) or PCIe Slot 1 (for dGPU only)
- **DVMT Pre-Allocated** → 64MB
- **Internal Graphics** → Enabled (even if using dGPU for compute tasks)

### Advanced → CPU Configuration
- **Hyper-Threading** → Enabled
- **Intel Virtualization Technology** → Enabled (optional, for VMs)

### Advanced → USB Configuration
- **XHCI Hand-off** → Enabled

### Boot → Secure Boot
- **OS Type** → Windows UEFI Mode
  - *This setting is required even though Secure Boot is disabled*

---

## Optional Settings ⚙️

### Advanced → CPU Configuration
- **Intel VT-x** → Enabled (if you plan to use virtualization)

### Advanced → System Agent (SA) Configuration
- **Memory Remap** → Enabled (if you have >4GB RAM)

### Boot
- **Boot Option Priorities** → Set your macOS drive as first priority after installation

---

## Memory/XMP Settings 🚀

### AI Tweaker (for RAM overclocking)
- **AI Overclock Tuner** → XMP I or XMP II
  - *Only if your RAM supports XMP profiles*
  - *Test stability after enabling*

---

## Save and Exit

1. Press **F10** to save changes
2. Confirm **Yes** to save and exit
3. System will reboot with new settings

---

## Verification Checklist ✓

After applying settings, verify:
- [ ] Fast Boot is disabled
- [ ] CSM is disabled  
- [ ] Secure Boot is disabled
- [ ] VT-d is disabled
- [ ] Above 4G Decoding is enabled
- [ ] DVMT Pre-Allocated is 64MB
- [ ] XHCI Hand-off is enabled
- [ ] Serial Port is off

---

## Troubleshooting 🔧

**If system won't boot after changes:**
1. Clear CMOS (remove battery for 30 seconds)
2. Load Optimized Defaults
3. Apply only essential settings first
4. Test boot, then add remaining settings

**If OpenCore doesn't appear:**
- Check that CSM is disabled
- Verify USB drive is formatted correctly (GUID/GPT)
- Try different USB port (preferably USB 2.0)

**If macOS installer hangs:**
- Verify VT-d is disabled
- Check Above 4G Decoding is enabled
- Ensure DVMT Pre-Allocated is 64MB

---

## Notes 📝

- BIOS version may affect available options
- Some settings may be in different locations depending on BIOS version
- Always save settings before exiting BIOS
- Keep a record of original settings before making changes
