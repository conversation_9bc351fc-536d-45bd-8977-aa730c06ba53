---
triple:          'i386-apple-darwin'
binary-path:     '/Users/<USER>/work/AppleALC/AppleALC/build/Release/AppleALCU.kext/Contents/MacOS/AppleALCU'
relocations:
  - { offsetInCU: 0x35, offset: 0x35, size: 0x4, addend: 0x0, symName: _kmod_info, symObjAddr: 0x0, symBinAddr: 0x4408, symSize: 0x0 }
  - { offsetInCU: 0x206, offset: 0x206, size: 0x4, addend: 0x0, symName: __realmain, symObjAddr: 0xA8, symBinAddr: 0x44B0, symSize: 0x0 }
  - { offsetInCU: 0x218, offset: 0x218, size: 0x4, addend: 0x0, symName: __antimain, symObjAddr: 0xAC, symBinAddr: 0x44B4, symSize: 0x0 }
  - { offsetInCU: 0x22A, offset: 0x22A, size: 0x4, addend: 0x0, symName: __kext_apple_cc, symObjAddr: 0xB0, symBinAddr: 0x44B8, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x256, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler12createSharedEv, symObjAddr: 0x0, symBinAddr: 0x0, symSize: 0x40 }
  - { offsetInCU: 0x36, offset: 0x265, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler11callbackAlcE, symObjAddr: 0x30C98, symBinAddr: 0x54E4, symSize: 0x0 }
  - { offsetInCU: 0xCE77, offset: 0xD0A6, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler18eraseRedundantLogsER13KernelPatcherm, symObjAddr: 0x1360, symBinAddr: 0x1360, symSize: 0x80 }
  - { offsetInCU: 0xCE97, offset: 0xD0C6, size: 0x4, addend: 0x0, symName: __ZZN10AlcEnabler18eraseRedundantLogsER13KernelPatchermE13logAssertFind, symObjAddr: 0x2142, symBinAddr: 0x324C, symSize: 0x0 }
  - { offsetInCU: 0xCEA9, offset: 0xD0D8, size: 0x4, addend: 0x0, symName: __ZZN10AlcEnabler18eraseRedundantLogsER13KernelPatchermE11nullReplace, symObjAddr: 0x214A, symBinAddr: 0x3254, symSize: 0x0 }
  - { offsetInCU: 0xCEE4, offset: 0xD113, size: 0x4, addend: 0x0, symName: __ZL10alcEnabler, symObjAddr: 0x2154, symBinAddr: 0x44BC, symSize: 0x0 }
  - { offsetInCU: 0xFE5D, offset: 0x1008C, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler12createSharedEv, symObjAddr: 0x0, symBinAddr: 0x0, symSize: 0x40 }
  - { offsetInCU: 0xFFC0, offset: 0x101EF, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler4initEv, symObjAddr: 0x40, symBinAddr: 0x40, symSize: 0x120 }
  - { offsetInCU: 0xFFC4, offset: 0x101F3, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler6deinitEv, symObjAddr: 0x160, symBinAddr: 0x160, symSize: 0x80 }
  - { offsetInCU: 0x10226, offset: 0x10455, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler6deinitEv, symObjAddr: 0x160, symBinAddr: 0x160, symSize: 0x80 }
  - { offsetInCU: 0x102E3, offset: 0x10512, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler16updatePropertiesEv, symObjAddr: 0x1E0, symBinAddr: 0x1E0, symSize: 0xC20 }
  - { offsetInCU: 0x10809, offset: 0x10A38, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler17validateInjectionEP15IORegistryEntry, symObjAddr: 0xE00, symBinAddr: 0xE00, symSize: 0x60 }
  - { offsetInCU: 0x1084B, offset: 0x10A7A, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler22updateDevicePropertiesEP15IORegistryEntryP10DeviceInfoPKcb, symObjAddr: 0xE60, symBinAddr: 0xE60, symSize: 0x120 }
  - { offsetInCU: 0x10A71, offset: 0x10CA0, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler16insertControllerEjjjbjjP15IORegistryEntry, symObjAddr: 0xF80, symBinAddr: 0xF80, symSize: 0xE0 }
  - { offsetInCU: 0x10C7E, offset: 0x10EAD, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler8gfxProbeEP9IOServiceS1_Pl, symObjAddr: 0x1060, symBinAddr: 0x1060, symSize: 0x50 }
  - { offsetInCU: 0x10CD3, offset: 0x10F02, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler24AppleHDAController_startEP9IOServiceS1_, symObjAddr: 0x10B0, symBinAddr: 0x10B0, symSize: 0x120 }
  - { offsetInCU: 0x10D8F, offset: 0x10FBE, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler28IOHDACodecDevice_executeVerbEPvtttPjb, symObjAddr: 0x11D0, symBinAddr: 0x11D0, symSize: 0x10 }
  - { offsetInCU: 0x10DFF, offset: 0x1102E, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler14getAudioLayoutEP9IOService, symObjAddr: 0x11E0, symBinAddr: 0x11E0, symSize: 0x130 }
  - { offsetInCU: 0x10EBF, offset: 0x110EE, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler28handleAudioClientEntitlementEP4taskPKcRP8OSObject, symObjAddr: 0x1310, symBinAddr: 0x1310, symSize: 0x50 }
  - { offsetInCU: 0x110AD, offset: 0x112DC, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler11processKextER13KernelPatchermym, symObjAddr: 0x13E0, symBinAddr: 0x13E0, symSize: 0x320 }
  - { offsetInCU: 0x11345, offset: 0x11574, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler15grabControllersEv, symObjAddr: 0x1700, symBinAddr: 0x1700, symSize: 0x3A0 }
  - { offsetInCU: 0x11575, offset: 0x117A4, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler12applyPatchesER13KernelPatchermPK9KextPatchm, symObjAddr: 0x1AA0, symBinAddr: 0x1AA0, symSize: 0x70 }
  - { offsetInCU: 0x115D4, offset: 0x11803, size: 0x4, addend: 0x0, symName: __ZN10AlcEnabler19validateControllersEv, symObjAddr: 0x1B10, symBinAddr: 0x1B10, symSize: 0x130 }
  - { offsetInCU: 0x11677, offset: 0x118A6, size: 0x4, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_08__invokeEPvR13KernelPatcher', symObjAddr: 0x1C40, symBinAddr: 0x1C40, symSize: 0x10 }
  - { offsetInCU: 0x1172C, offset: 0x1195B, size: 0x4, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_18__invokeEPvR13KernelPatchermym', symObjAddr: 0x1C50, symBinAddr: 0x1C50, symSize: 0x10 }
  - { offsetInCU: 0x11847, offset: 0x11A76, size: 0x4, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_28__invokeEPvP4taskPKcRP8OSObject', symObjAddr: 0x1C60, symBinAddr: 0x1C60, symSize: 0x44 }
  - { offsetInCU: 0x27, offset: 0x11B66, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x1CB0, symSize: 0x30 }
  - { offsetInCU: 0x36, offset: 0x11B75, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider10gMetaClassE, symObjAddr: 0x21C38, symBinAddr: 0x54E8, symSize: 0x0 }
  - { offsetInCU: 0x6943, offset: 0x18482, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider9metaClassE, symObjAddr: 0x438, symBinAddr: 0x325C, symSize: 0x0 }
  - { offsetInCU: 0x6952, offset: 0x18491, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider10superClassE, symObjAddr: 0x43C, symBinAddr: 0x3260, symSize: 0x0 }
  - { offsetInCU: 0x7B4E, offset: 0x1968D, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x1CB0, symSize: 0x30 }
  - { offsetInCU: 0x7BA0, offset: 0x196DF, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassD1Ev, symObjAddr: 0x30, symBinAddr: 0x1CE0, symSize: 0x10 }
  - { offsetInCU: 0x7BD6, offset: 0x19715, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderC2EPK11OSMetaClass, symObjAddr: 0x40, symBinAddr: 0x1CF0, symSize: 0x30 }
  - { offsetInCU: 0x7C26, offset: 0x19765, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderC1EPK11OSMetaClass, symObjAddr: 0x70, symBinAddr: 0x1D20, symSize: 0x30 }
  - { offsetInCU: 0x7C7F, offset: 0x197BE, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderD2Ev, symObjAddr: 0xA0, symBinAddr: 0x1D50, symSize: 0x10 }
  - { offsetInCU: 0x7CB7, offset: 0x197F6, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderD1Ev, symObjAddr: 0xB0, symBinAddr: 0x1D60, symSize: 0x10 }
  - { offsetInCU: 0x7D09, offset: 0x19848, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderD0Ev, symObjAddr: 0xC0, symBinAddr: 0x1D70, symSize: 0x30 }
  - { offsetInCU: 0x7D65, offset: 0x198A4, size: 0x4, addend: 0x0, symName: __ZNK21ALCUserClientProvider12getMetaClassEv, symObjAddr: 0xF0, symBinAddr: 0x1DA0, symSize: 0x10 }
  - { offsetInCU: 0x7D85, offset: 0x198C4, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassC2Ev, symObjAddr: 0x100, symBinAddr: 0x1DB0, symSize: 0x30 }
  - { offsetInCU: 0x7DD9, offset: 0x19918, size: 0x4, addend: 0x0, symName: __ZNK21ALCUserClientProvider9MetaClass5allocEv, symObjAddr: 0x130, symBinAddr: 0x1DE0, symSize: 0x50 }
  - { offsetInCU: 0x7E2B, offset: 0x1996A, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderC1Ev, symObjAddr: 0x180, symBinAddr: 0x1E30, symSize: 0x40 }
  - { offsetInCU: 0x7E61, offset: 0x199A0, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProviderC2Ev, symObjAddr: 0x1C0, symBinAddr: 0x1E70, symSize: 0x40 }
  - { offsetInCU: 0x7F44, offset: 0x19A83, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider5probeEP9IOServicePl, symObjAddr: 0x200, symBinAddr: 0x1EB0, symSize: 0x100 }
  - { offsetInCU: 0x802E, offset: 0x19B6D, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider5startEP9IOService, symObjAddr: 0x300, symBinAddr: 0x1FB0, symSize: 0x60 }
  - { offsetInCU: 0x8060, offset: 0x19B9F, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider4stopEP9IOService, symObjAddr: 0x360, symBinAddr: 0x2010, symSize: 0x10 }
  - { offsetInCU: 0x8092, offset: 0x19BD1, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider14sendHdaCommandEttt, symObjAddr: 0x370, symBinAddr: 0x2020, symSize: 0x70 }
  - { offsetInCU: 0x811E, offset: 0x19C5D, size: 0x4, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassD0Ev, symObjAddr: 0x3E0, symBinAddr: 0x2090, symSize: 0x10 }
  - { offsetInCU: 0x819F, offset: 0x19CDE, size: 0x4, addend: 0x0, symName: __GLOBAL__sub_I_ALCUserClientProvider.cpp, symObjAddr: 0x3F0, symBinAddr: 0x20A0, symSize: 0x30 }
  - { offsetInCU: 0x81F1, offset: 0x19D30, size: 0x4, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x420, symBinAddr: 0x20D0, symSize: 0x17 }
  - { offsetInCU: 0x27, offset: 0x1A20A, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x20F0, symSize: 0x30 }
  - { offsetInCU: 0x36, offset: 0x1A219, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient10gMetaClassE, symObjAddr: 0x1B9E0, symBinAddr: 0x5500, symSize: 0x0 }
  - { offsetInCU: 0x6297, offset: 0x2047A, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient9metaClassE, symObjAddr: 0x3E8, symBinAddr: 0x3784, symSize: 0x0 }
  - { offsetInCU: 0x62A6, offset: 0x20489, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient10superClassE, symObjAddr: 0x3EC, symBinAddr: 0x3788, symSize: 0x0 }
  - { offsetInCU: 0x62B5, offset: 0x20498, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient14sMethodsLegacyE, symObjAddr: 0x9A0, symBinAddr: 0x4540, symSize: 0x0 }
  - { offsetInCU: 0x62DF, offset: 0x204C2, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x20F0, symSize: 0x30 }
  - { offsetInCU: 0x6331, offset: 0x20514, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassD1Ev, symObjAddr: 0x30, symBinAddr: 0x2120, symSize: 0x10 }
  - { offsetInCU: 0x6367, offset: 0x2054A, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientC2EPK11OSMetaClass, symObjAddr: 0x40, symBinAddr: 0x2130, symSize: 0x40 }
  - { offsetInCU: 0x63B2, offset: 0x20595, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientC1EPK11OSMetaClass, symObjAddr: 0x80, symBinAddr: 0x2170, symSize: 0x40 }
  - { offsetInCU: 0x640B, offset: 0x205EE, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientD2Ev, symObjAddr: 0xC0, symBinAddr: 0x21B0, symSize: 0x10 }
  - { offsetInCU: 0x6443, offset: 0x20626, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientD1Ev, symObjAddr: 0xD0, symBinAddr: 0x21C0, symSize: 0x10 }
  - { offsetInCU: 0x6495, offset: 0x20678, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientD0Ev, symObjAddr: 0xE0, symBinAddr: 0x21D0, symSize: 0x30 }
  - { offsetInCU: 0x64F1, offset: 0x206D4, size: 0x4, addend: 0x0, symName: __ZNK13ALCUserClient12getMetaClassEv, symObjAddr: 0x110, symBinAddr: 0x2200, symSize: 0x10 }
  - { offsetInCU: 0x6511, offset: 0x206F4, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassC2Ev, symObjAddr: 0x120, symBinAddr: 0x2210, symSize: 0x30 }
  - { offsetInCU: 0x6565, offset: 0x20748, size: 0x4, addend: 0x0, symName: __ZNK13ALCUserClient9MetaClass5allocEv, symObjAddr: 0x150, symBinAddr: 0x2240, symSize: 0x50 }
  - { offsetInCU: 0x65B7, offset: 0x2079A, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientC1Ev, symObjAddr: 0x1A0, symBinAddr: 0x2290, symSize: 0x40 }
  - { offsetInCU: 0x65BB, offset: 0x2079E, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientC2Ev, symObjAddr: 0x1E0, symBinAddr: 0x22D0, symSize: 0x40 }
  - { offsetInCU: 0x65ED, offset: 0x207D0, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClientC2Ev, symObjAddr: 0x1E0, symBinAddr: 0x22D0, symSize: 0x40 }
  - { offsetInCU: 0x65F1, offset: 0x207D4, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient22sendHdaCommandInternalEPS_tttPy, symObjAddr: 0x220, symBinAddr: 0x2310, symSize: 0x40 }
  - { offsetInCU: 0x6609, offset: 0x207EC, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient22sendHdaCommandInternalEPS_tttPy, symObjAddr: 0x220, symBinAddr: 0x2310, symSize: 0x40 }
  - { offsetInCU: 0x6667, offset: 0x2084A, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient26getTargetAndMethodForIndexEPP9IOServicem, symObjAddr: 0x260, symBinAddr: 0x2350, symSize: 0x20 }
  - { offsetInCU: 0x66A8, offset: 0x2088B, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient12initWithTaskEP4taskPvmP12OSDictionary, symObjAddr: 0x280, symBinAddr: 0x2370, symSize: 0x40 }
  - { offsetInCU: 0x6703, offset: 0x208E6, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient5startEP9IOService, symObjAddr: 0x2C0, symBinAddr: 0x23B0, symSize: 0x40 }
  - { offsetInCU: 0x6745, offset: 0x20928, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient11clientCloseEv, symObjAddr: 0x300, symBinAddr: 0x23F0, symSize: 0x40 }
  - { offsetInCU: 0x6768, offset: 0x2094B, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient17methodExecuteVerbEP21ALCUserClientProviderPvP25IOExternalMethodArguments, symObjAddr: 0x340, symBinAddr: 0x2430, symSize: 0x40 }
  - { offsetInCU: 0x67E5, offset: 0x209C8, size: 0x4, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassD0Ev, symObjAddr: 0x380, symBinAddr: 0x2470, symSize: 0x10 }
  - { offsetInCU: 0x6866, offset: 0x20A49, size: 0x4, addend: 0x0, symName: __GLOBAL__sub_I_ALCUserClient.cpp, symObjAddr: 0x390, symBinAddr: 0x2480, symSize: 0x40 }
  - { offsetInCU: 0x68B8, offset: 0x20A9B, size: 0x4, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x3D0, symBinAddr: 0x24C0, symSize: 0x17 }
  - { offsetInCU: 0x27, offset: 0x20B0C, size: 0x4, addend: 0x0, symName: __ZN9AppleALCU9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x24E0, symSize: 0x30 }
  - { offsetInCU: 0x3D, offset: 0x20B22, size: 0x4, addend: 0x0, symName: _AppleALCU_startSuccess, symObjAddr: 0x26D74, symBinAddr: 0x5518, symSize: 0x0 }
  - { offsetInCU: 0x57, offset: 0x20B3C, size: 0x4, addend: 0x0, symName: _AppleALCU_debugEnabled, symObjAddr: 0x26D75, symBinAddr: 0x5519, symSize: 0x0 }
  - { offsetInCU: 0x6A, offset: 0x20B4F, size: 0x4, addend: 0x0, symName: _AppleALCU_debugPrintDelay, symObjAddr: 0x26D78, symBinAddr: 0x551C, symSize: 0x0 }
  - { offsetInCU: 0x7D, offset: 0x20B62, size: 0x4, addend: 0x0, symName: __ZN9AppleALCU10gMetaClassE, symObjAddr: 0x26D7C, symBinAddr: 0x5520, symSize: 0x0 }
  - { offsetInCU: 0x5266, offset: 0x25D4B, size: 0x4, addend: 0x0, symName: __ZN9AppleALCU9metaClassE, symObjAddr: 0x498, symBinAddr: 0x3D2C, symSize: 0x0 }
  - { offsetInCU: 0x5275, offset: 0x25D5A, size: 0x4, addend: 0x0, symName: __ZN9AppleALCU10superClassE, symObjAddr: 0x49C, symBinAddr: 0x3D30, symSize: 0x0 }
  - { offsetInCU: 0x528B, offset: 0x25D70, size: 0x4, addend: 0x0, symName: _AppleALCU_selfInstance, symObjAddr: 0x26D94, symBinAddr: 0x5538, symSize: 0x0 }
  - { offsetInCU: 0x52A2, offset: 0x25D87, size: 0x4, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0x9BC, symBinAddr: 0x4250, symSize: 0x0 }
  - { offsetInCU: 0x52EB, offset: 0x25DD0, size: 0x4, addend: 0x0, symName: __ZN9AppleALCU9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x24E0, symSize: 0x30 }
  - { offsetInCU: 0x533D, offset: 0x25E22, size: 0x4, addend: 0x0, symName: __ZN9AppleALCU9MetaClassD1Ev, symObjAddr: 0x30, symBinAddr: 0x2510, symSize: 0x10 }
  - { offsetInCU: 0x5373, offset: 0x25E58, size: 0x4, addend: 0x0, symName: __ZN9AppleALCUC2EPK11OSMetaClass, symObjAddr: 0x40, symBinAddr: 0x2520, symSize: 0x30 }
  - { offsetInCU: 0x53BE, offset: 0x25EA3, size: 0x4, addend: 0x0, symName: __ZN9AppleALCUC1EPK11OSMetaClass, symObjAddr: 0x70, symBinAddr: 0x2550, symSize: 0x30 }
  - { offsetInCU: 0x5417, offset: 0x25EFC, size: 0x4, addend: 0x0, symName: __ZN9AppleALCUD2Ev, symObjAddr: 0xA0, symBinAddr: 0x2580, symSize: 0x10 }
  - { offsetInCU: 0x544F, offset: 0x25F34, size: 0x4, addend: 0x0, symName: __ZN9AppleALCUD1Ev, symObjAddr: 0xB0, symBinAddr: 0x2590, symSize: 0x10 }
  - { offsetInCU: 0x54A1, offset: 0x25F86, size: 0x4, addend: 0x0, symName: __ZN9AppleALCUD0Ev, symObjAddr: 0xC0, symBinAddr: 0x25A0, symSize: 0x30 }
  - { offsetInCU: 0x54FD, offset: 0x25FE2, size: 0x4, addend: 0x0, symName: __ZNK9AppleALCU12getMetaClassEv, symObjAddr: 0xF0, symBinAddr: 0x25D0, symSize: 0x10 }
  - { offsetInCU: 0x551D, offset: 0x26002, size: 0x4, addend: 0x0, symName: __ZN9AppleALCU9MetaClassC2Ev, symObjAddr: 0x100, symBinAddr: 0x25E0, symSize: 0x30 }
  - { offsetInCU: 0x5571, offset: 0x26056, size: 0x4, addend: 0x0, symName: __ZNK9AppleALCU9MetaClass5allocEv, symObjAddr: 0x130, symBinAddr: 0x2610, symSize: 0x40 }
  - { offsetInCU: 0x55C3, offset: 0x260A8, size: 0x4, addend: 0x0, symName: __ZN9AppleALCUC1Ev, symObjAddr: 0x170, symBinAddr: 0x2650, symSize: 0x40 }
  - { offsetInCU: 0x55F9, offset: 0x260DE, size: 0x4, addend: 0x0, symName: __ZN9AppleALCUC2Ev, symObjAddr: 0x1B0, symBinAddr: 0x2690, symSize: 0x40 }
  - { offsetInCU: 0x5615, offset: 0x260FA, size: 0x4, addend: 0x0, symName: __ZN9AppleALCU5probeEP9IOServicePl, symObjAddr: 0x1F0, symBinAddr: 0x26D0, symSize: 0x60 }
  - { offsetInCU: 0x5666, offset: 0x2614B, size: 0x4, addend: 0x0, symName: __ZN9AppleALCU5startEP9IOService, symObjAddr: 0x250, symBinAddr: 0x2730, symSize: 0x60 }
  - { offsetInCU: 0x5698, offset: 0x2617D, size: 0x4, addend: 0x0, symName: __ZN9AppleALCU4stopEP9IOService, symObjAddr: 0x2B0, symBinAddr: 0x2790, symSize: 0x20 }
  - { offsetInCU: 0x5738, offset: 0x2621D, size: 0x4, addend: 0x0, symName: _AppleALCU_kern_start, symObjAddr: 0x2D0, symBinAddr: 0x27B0, symSize: 0x160 }
  - { offsetInCU: 0x5829, offset: 0x2630E, size: 0x4, addend: 0x0, symName: _AppleALCU_kern_stop, symObjAddr: 0x430, symBinAddr: 0x2910, symSize: 0x10 }
  - { offsetInCU: 0x5874, offset: 0x26359, size: 0x4, addend: 0x0, symName: __ZN9AppleALCU9MetaClassD0Ev, symObjAddr: 0x440, symBinAddr: 0x2920, symSize: 0x10 }
  - { offsetInCU: 0x58F5, offset: 0x263DA, size: 0x4, addend: 0x0, symName: __GLOBAL__sub_I_plugin_start.cpp, symObjAddr: 0x450, symBinAddr: 0x2930, symSize: 0x30 }
  - { offsetInCU: 0x5947, offset: 0x2642C, size: 0x4, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x480, symBinAddr: 0x2960, symSize: 0x17 }
  - { offsetInCU: 0x27, offset: 0x2649D, size: 0x4, addend: 0x0, symName: '__ZN3$_08__invokeEv', symObjAddr: 0x0, symBinAddr: 0x2980, symSize: 0x1D }
  - { offsetInCU: 0x3D, offset: 0x264B3, size: 0x4, addend: 0x0, symName: _AppleALCU_config, symObjAddr: 0x4C, symBinAddr: 0x4564, symSize: 0x0 }
  - { offsetInCU: 0x12B, offset: 0x265A1, size: 0x4, addend: 0x0, symName: __ZL10bootargOff, symObjAddr: 0x40, symBinAddr: 0x4558, symSize: 0x0 }
  - { offsetInCU: 0x153, offset: 0x265C9, size: 0x4, addend: 0x0, symName: __ZL12bootargDebug, symObjAddr: 0x44, symBinAddr: 0x455C, symSize: 0x0 }
  - { offsetInCU: 0x168, offset: 0x265DE, size: 0x4, addend: 0x0, symName: __ZL11bootargBeta, symObjAddr: 0x48, symBinAddr: 0x4560, symSize: 0x0 }
  - { offsetInCU: 0x209, offset: 0x2667F, size: 0x4, addend: 0x0, symName: '__ZN3$_08__invokeEv', symObjAddr: 0x0, symBinAddr: 0x2980, symSize: 0x1D }
  - { offsetInCU: 0x35, offset: 0x266EF, size: 0x4, addend: 0x0, symName: _KextIdAppleHDA, symObjAddr: 0x0, symBinAddr: 0x4264, symSize: 0x0 }
  - { offsetInCU: 0x54, offset: 0x2670E, size: 0x4, addend: 0x0, symName: _KextIdAppleHDAPlatformDriver, symObjAddr: 0x4, symBinAddr: 0x4268, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0x26721, size: 0x4, addend: 0x0, symName: _KextIdAppleHDAController, symObjAddr: 0x8, symBinAddr: 0x426C, symSize: 0x0 }
  - { offsetInCU: 0x7A, offset: 0x26734, size: 0x4, addend: 0x0, symName: _KextIdAppleGFXHDA, symObjAddr: 0xC, symBinAddr: 0x4270, symSize: 0x0 }
  - { offsetInCU: 0x8D, offset: 0x26747, size: 0x4, addend: 0x0, symName: _KextIdIOHDAFamily, symObjAddr: 0x10, symBinAddr: 0x4274, symSize: 0x0 }
  - { offsetInCU: 0xA0, offset: 0x2675A, size: 0x4, addend: 0x0, symName: _AppleALCU_kextList, symObjAddr: 0x420, symBinAddr: 0x45A8, symSize: 0x0 }
  - { offsetInCU: 0x10D, offset: 0x267C7, size: 0x4, addend: 0x0, symName: _AppleALCU_kextListSize, symObjAddr: 0x14, symBinAddr: 0x4278, symSize: 0x0 }
  - { offsetInCU: 0x123, offset: 0x267DD, size: 0x4, addend: 0x0, symName: _AppleALCU_controllerMod, symObjAddr: 0xD74, symBinAddr: 0x4EFC, symSize: 0x0 }
  - { offsetInCU: 0x145, offset: 0x267FF, size: 0x4, addend: 0x0, symName: _AppleALCU_controllerModSize, symObjAddr: 0x18, symBinAddr: 0x427C, symSize: 0x0 }
  - { offsetInCU: 0x157, offset: 0x26811, size: 0x4, addend: 0x0, symName: __ZL9kextPath0, symObjAddr: 0x40C, symBinAddr: 0x4594, symSize: 0x0 }
  - { offsetInCU: 0x178, offset: 0x26832, size: 0x4, addend: 0x0, symName: __ZL9kextPath1, symObjAddr: 0x410, symBinAddr: 0x4598, symSize: 0x0 }
  - { offsetInCU: 0x18D, offset: 0x26847, size: 0x4, addend: 0x0, symName: __ZL9kextPath2, symObjAddr: 0x414, symBinAddr: 0x459C, symSize: 0x0 }
  - { offsetInCU: 0x1A2, offset: 0x2685C, size: 0x4, addend: 0x0, symName: __ZL9kextPath3, symObjAddr: 0x418, symBinAddr: 0x45A0, symSize: 0x0 }
  - { offsetInCU: 0x1B7, offset: 0x26871, size: 0x4, addend: 0x0, symName: __ZL9kextPath4, symObjAddr: 0x41C, symBinAddr: 0x45A4, symSize: 0x0 }
  - { offsetInCU: 0x1CF, offset: 0x26889, size: 0x4, addend: 0x0, symName: __ZL10patches110, symObjAddr: 0x498, symBinAddr: 0x4620, symSize: 0x0 }
  - { offsetInCU: 0x1F4, offset: 0x268AE, size: 0x4, addend: 0x0, symName: __ZL11patchBuf146, symObjAddr: 0x1C, symBinAddr: 0x4280, symSize: 0x0 }
  - { offsetInCU: 0x219, offset: 0x268D3, size: 0x4, addend: 0x0, symName: __ZL11patchBuf147, symObjAddr: 0x20, symBinAddr: 0x4284, symSize: 0x0 }
  - { offsetInCU: 0x232, offset: 0x268EC, size: 0x4, addend: 0x0, symName: __ZL10patches111, symObjAddr: 0x4B4, symBinAddr: 0x463C, symSize: 0x0 }
  - { offsetInCU: 0x24B, offset: 0x26905, size: 0x4, addend: 0x0, symName: __ZL11patchBuf148, symObjAddr: 0x24, symBinAddr: 0x4288, symSize: 0x0 }
  - { offsetInCU: 0x264, offset: 0x2691E, size: 0x4, addend: 0x0, symName: __ZL10patches112, symObjAddr: 0x4D0, symBinAddr: 0x4658, symSize: 0x0 }
  - { offsetInCU: 0x27D, offset: 0x26937, size: 0x4, addend: 0x0, symName: __ZL11patchBuf149, symObjAddr: 0x28, symBinAddr: 0x428C, symSize: 0x0 }
  - { offsetInCU: 0x296, offset: 0x26950, size: 0x4, addend: 0x0, symName: __ZL10patches113, symObjAddr: 0x4EC, symBinAddr: 0x4674, symSize: 0x0 }
  - { offsetInCU: 0x2AF, offset: 0x26969, size: 0x4, addend: 0x0, symName: __ZL11patchBuf150, symObjAddr: 0x2C, symBinAddr: 0x4290, symSize: 0x0 }
  - { offsetInCU: 0x2C8, offset: 0x26982, size: 0x4, addend: 0x0, symName: __ZL10patches114, symObjAddr: 0x508, symBinAddr: 0x4690, symSize: 0x0 }
  - { offsetInCU: 0x2E1, offset: 0x2699B, size: 0x4, addend: 0x0, symName: __ZL11patchBuf151, symObjAddr: 0x30, symBinAddr: 0x4294, symSize: 0x0 }
  - { offsetInCU: 0x2FA, offset: 0x269B4, size: 0x4, addend: 0x0, symName: __ZL10patches115, symObjAddr: 0x524, symBinAddr: 0x46AC, symSize: 0x0 }
  - { offsetInCU: 0x313, offset: 0x269CD, size: 0x4, addend: 0x0, symName: __ZL11patchBuf152, symObjAddr: 0x34, symBinAddr: 0x4298, symSize: 0x0 }
  - { offsetInCU: 0x32C, offset: 0x269E6, size: 0x4, addend: 0x0, symName: __ZL10patches116, symObjAddr: 0x540, symBinAddr: 0x46C8, symSize: 0x0 }
  - { offsetInCU: 0x345, offset: 0x269FF, size: 0x4, addend: 0x0, symName: __ZL11patchBuf153, symObjAddr: 0x38, symBinAddr: 0x429C, symSize: 0x0 }
  - { offsetInCU: 0x35E, offset: 0x26A18, size: 0x4, addend: 0x0, symName: __ZL10patches117, symObjAddr: 0x55C, symBinAddr: 0x46E4, symSize: 0x0 }
  - { offsetInCU: 0x377, offset: 0x26A31, size: 0x4, addend: 0x0, symName: __ZL11patchBuf154, symObjAddr: 0x3C, symBinAddr: 0x42A0, symSize: 0x0 }
  - { offsetInCU: 0x390, offset: 0x26A4A, size: 0x4, addend: 0x0, symName: __ZL11patchBuf155, symObjAddr: 0x40, symBinAddr: 0x42A4, symSize: 0x0 }
  - { offsetInCU: 0x3A9, offset: 0x26A63, size: 0x4, addend: 0x0, symName: __ZL10patches118, symObjAddr: 0x578, symBinAddr: 0x4700, symSize: 0x0 }
  - { offsetInCU: 0x3C2, offset: 0x26A7C, size: 0x4, addend: 0x0, symName: __ZL11patchBuf156, symObjAddr: 0x44, symBinAddr: 0x42A8, symSize: 0x0 }
  - { offsetInCU: 0x3DB, offset: 0x26A95, size: 0x4, addend: 0x0, symName: __ZL10patches119, symObjAddr: 0x594, symBinAddr: 0x471C, symSize: 0x0 }
  - { offsetInCU: 0x3F4, offset: 0x26AAE, size: 0x4, addend: 0x0, symName: __ZL11patchBuf157, symObjAddr: 0x48, symBinAddr: 0x42AC, symSize: 0x0 }
  - { offsetInCU: 0x40D, offset: 0x26AC7, size: 0x4, addend: 0x0, symName: __ZL10patches120, symObjAddr: 0x5B0, symBinAddr: 0x4738, symSize: 0x0 }
  - { offsetInCU: 0x426, offset: 0x26AE0, size: 0x4, addend: 0x0, symName: __ZL11patchBuf158, symObjAddr: 0x4C, symBinAddr: 0x42B0, symSize: 0x0 }
  - { offsetInCU: 0x43F, offset: 0x26AF9, size: 0x4, addend: 0x0, symName: __ZL10patches121, symObjAddr: 0x5CC, symBinAddr: 0x4754, symSize: 0x0 }
  - { offsetInCU: 0x464, offset: 0x26B1E, size: 0x4, addend: 0x0, symName: __ZL11patchBuf159, symObjAddr: 0x50, symBinAddr: 0x42B4, symSize: 0x0 }
  - { offsetInCU: 0x47D, offset: 0x26B37, size: 0x4, addend: 0x0, symName: __ZL11patchBuf160, symObjAddr: 0x54, symBinAddr: 0x42B8, symSize: 0x0 }
  - { offsetInCU: 0x496, offset: 0x26B50, size: 0x4, addend: 0x0, symName: __ZL11patchBuf161, symObjAddr: 0x58, symBinAddr: 0x42BC, symSize: 0x0 }
  - { offsetInCU: 0x4AF, offset: 0x26B69, size: 0x4, addend: 0x0, symName: __ZL11patchBuf162, symObjAddr: 0x5C, symBinAddr: 0x42C0, symSize: 0x0 }
  - { offsetInCU: 0x4C8, offset: 0x26B82, size: 0x4, addend: 0x0, symName: __ZL11patchBuf163, symObjAddr: 0x60, symBinAddr: 0x42C4, symSize: 0x0 }
  - { offsetInCU: 0x4E1, offset: 0x26B9B, size: 0x4, addend: 0x0, symName: __ZL11patchBuf164, symObjAddr: 0x64, symBinAddr: 0x42C8, symSize: 0x0 }
  - { offsetInCU: 0x4FA, offset: 0x26BB4, size: 0x4, addend: 0x0, symName: __ZL11patchBuf165, symObjAddr: 0x68, symBinAddr: 0x42CC, symSize: 0x0 }
  - { offsetInCU: 0x513, offset: 0x26BCD, size: 0x4, addend: 0x0, symName: __ZL11patchBuf166, symObjAddr: 0x6C, symBinAddr: 0x42D0, symSize: 0x0 }
  - { offsetInCU: 0x52C, offset: 0x26BE6, size: 0x4, addend: 0x0, symName: __ZL10patches122, symObjAddr: 0x63C, symBinAddr: 0x47C4, symSize: 0x0 }
  - { offsetInCU: 0x545, offset: 0x26BFF, size: 0x4, addend: 0x0, symName: __ZL11patchBuf167, symObjAddr: 0x70, symBinAddr: 0x42D4, symSize: 0x0 }
  - { offsetInCU: 0x55E, offset: 0x26C18, size: 0x4, addend: 0x0, symName: __ZL11patchBuf168, symObjAddr: 0x74, symBinAddr: 0x42D8, symSize: 0x0 }
  - { offsetInCU: 0x577, offset: 0x26C31, size: 0x4, addend: 0x0, symName: __ZL11patchBuf169, symObjAddr: 0x78, symBinAddr: 0x42DC, symSize: 0x0 }
  - { offsetInCU: 0x590, offset: 0x26C4A, size: 0x4, addend: 0x0, symName: __ZL10patches123, symObjAddr: 0x6AC, symBinAddr: 0x4834, symSize: 0x0 }
  - { offsetInCU: 0x5B5, offset: 0x26C6F, size: 0x4, addend: 0x0, symName: __ZL11patchBuf170, symObjAddr: 0x7C, symBinAddr: 0x42E0, symSize: 0x0 }
  - { offsetInCU: 0x5CE, offset: 0x26C88, size: 0x4, addend: 0x0, symName: __ZL11patchBuf171, symObjAddr: 0x80, symBinAddr: 0x42E4, symSize: 0x0 }
  - { offsetInCU: 0x5E7, offset: 0x26CA1, size: 0x4, addend: 0x0, symName: __ZL10patches124, symObjAddr: 0x6E4, symBinAddr: 0x486C, symSize: 0x0 }
  - { offsetInCU: 0x60C, offset: 0x26CC6, size: 0x4, addend: 0x0, symName: __ZL11patchBuf172, symObjAddr: 0x84, symBinAddr: 0x42E8, symSize: 0x0 }
  - { offsetInCU: 0x625, offset: 0x26CDF, size: 0x4, addend: 0x0, symName: __ZL11patchBuf173, symObjAddr: 0x88, symBinAddr: 0x42EC, symSize: 0x0 }
  - { offsetInCU: 0x63E, offset: 0x26CF8, size: 0x4, addend: 0x0, symName: __ZL11patchBuf174, symObjAddr: 0x8C, symBinAddr: 0x42F0, symSize: 0x0 }
  - { offsetInCU: 0x663, offset: 0x26D1D, size: 0x4, addend: 0x0, symName: __ZL11patchBuf175, symObjAddr: 0x92, symBinAddr: 0x42F6, symSize: 0x0 }
  - { offsetInCU: 0x67C, offset: 0x26D36, size: 0x4, addend: 0x0, symName: __ZL11patchBuf176, symObjAddr: 0x98, symBinAddr: 0x42FC, symSize: 0x0 }
  - { offsetInCU: 0x6A1, offset: 0x26D5B, size: 0x4, addend: 0x0, symName: __ZL11patchBuf177, symObjAddr: 0xA4, symBinAddr: 0x4308, symSize: 0x0 }
  - { offsetInCU: 0x6BA, offset: 0x26D74, size: 0x4, addend: 0x0, symName: __ZL11patchBuf178, symObjAddr: 0xB0, symBinAddr: 0x4314, symSize: 0x0 }
  - { offsetInCU: 0x6DF, offset: 0x26D99, size: 0x4, addend: 0x0, symName: __ZL11patchBuf179, symObjAddr: 0xB5, symBinAddr: 0x4319, symSize: 0x0 }
  - { offsetInCU: 0x6F8, offset: 0x26DB2, size: 0x4, addend: 0x0, symName: __ZL11patchBuf180, symObjAddr: 0xBA, symBinAddr: 0x431E, symSize: 0x0 }
  - { offsetInCU: 0x71D, offset: 0x26DD7, size: 0x4, addend: 0x0, symName: __ZL11patchBuf181, symObjAddr: 0xC1, symBinAddr: 0x4325, symSize: 0x0 }
  - { offsetInCU: 0x736, offset: 0x26DF0, size: 0x4, addend: 0x0, symName: __ZL10patches125, symObjAddr: 0x770, symBinAddr: 0x48F8, symSize: 0x0 }
  - { offsetInCU: 0x74F, offset: 0x26E09, size: 0x4, addend: 0x0, symName: __ZL11patchBuf182, symObjAddr: 0xC8, symBinAddr: 0x432C, symSize: 0x0 }
  - { offsetInCU: 0x768, offset: 0x26E22, size: 0x4, addend: 0x0, symName: __ZL10patches126, symObjAddr: 0x7FC, symBinAddr: 0x4984, symSize: 0x0 }
  - { offsetInCU: 0x781, offset: 0x26E3B, size: 0x4, addend: 0x0, symName: __ZL11patchBuf183, symObjAddr: 0xCC, symBinAddr: 0x4330, symSize: 0x0 }
  - { offsetInCU: 0x79A, offset: 0x26E54, size: 0x4, addend: 0x0, symName: __ZL10patches127, symObjAddr: 0x888, symBinAddr: 0x4A10, symSize: 0x0 }
  - { offsetInCU: 0x7B3, offset: 0x26E6D, size: 0x4, addend: 0x0, symName: __ZL11patchBuf184, symObjAddr: 0xD0, symBinAddr: 0x4334, symSize: 0x0 }
  - { offsetInCU: 0x7CC, offset: 0x26E86, size: 0x4, addend: 0x0, symName: __ZL11patchBuf185, symObjAddr: 0xD4, symBinAddr: 0x4338, symSize: 0x0 }
  - { offsetInCU: 0x7E5, offset: 0x26E9F, size: 0x4, addend: 0x0, symName: __ZL11patchBuf186, symObjAddr: 0xD8, symBinAddr: 0x433C, symSize: 0x0 }
  - { offsetInCU: 0x7FE, offset: 0x26EB8, size: 0x4, addend: 0x0, symName: __ZL11patchBuf187, symObjAddr: 0xDC, symBinAddr: 0x4340, symSize: 0x0 }
  - { offsetInCU: 0x817, offset: 0x26ED1, size: 0x4, addend: 0x0, symName: __ZL11patchBuf188, symObjAddr: 0xE1, symBinAddr: 0x4345, symSize: 0x0 }
  - { offsetInCU: 0x830, offset: 0x26EEA, size: 0x4, addend: 0x0, symName: __ZL11patchBuf189, symObjAddr: 0xE6, symBinAddr: 0x434A, symSize: 0x0 }
  - { offsetInCU: 0x849, offset: 0x26F03, size: 0x4, addend: 0x0, symName: __ZL10patches128, symObjAddr: 0x914, symBinAddr: 0x4A9C, symSize: 0x0 }
  - { offsetInCU: 0x862, offset: 0x26F1C, size: 0x4, addend: 0x0, symName: __ZL11patchBuf190, symObjAddr: 0xEB, symBinAddr: 0x434F, symSize: 0x0 }
  - { offsetInCU: 0x87B, offset: 0x26F35, size: 0x4, addend: 0x0, symName: __ZL11patchBuf191, symObjAddr: 0xEF, symBinAddr: 0x4353, symSize: 0x0 }
  - { offsetInCU: 0x894, offset: 0x26F4E, size: 0x4, addend: 0x0, symName: __ZL11patchBuf192, symObjAddr: 0xF3, symBinAddr: 0x4357, symSize: 0x0 }
  - { offsetInCU: 0x8AD, offset: 0x26F67, size: 0x4, addend: 0x0, symName: __ZL10patches129, symObjAddr: 0x94C, symBinAddr: 0x4AD4, symSize: 0x0 }
  - { offsetInCU: 0x8C6, offset: 0x26F80, size: 0x4, addend: 0x0, symName: __ZL11patchBuf193, symObjAddr: 0xF7, symBinAddr: 0x435B, symSize: 0x0 }
  - { offsetInCU: 0x8DF, offset: 0x26F99, size: 0x4, addend: 0x0, symName: __ZL11patchBuf194, symObjAddr: 0xFB, symBinAddr: 0x435F, symSize: 0x0 }
  - { offsetInCU: 0x8F8, offset: 0x26FB2, size: 0x4, addend: 0x0, symName: __ZL11patchBuf195, symObjAddr: 0xFF, symBinAddr: 0x4363, symSize: 0x0 }
  - { offsetInCU: 0x911, offset: 0x26FCB, size: 0x4, addend: 0x0, symName: __ZL11patchBuf196, symObjAddr: 0x103, symBinAddr: 0x4367, symSize: 0x0 }
  - { offsetInCU: 0x92A, offset: 0x26FE4, size: 0x4, addend: 0x0, symName: __ZL10patches130, symObjAddr: 0x984, symBinAddr: 0x4B0C, symSize: 0x0 }
  - { offsetInCU: 0x943, offset: 0x26FFD, size: 0x4, addend: 0x0, symName: __ZL11patchBuf197, symObjAddr: 0x107, symBinAddr: 0x436B, symSize: 0x0 }
  - { offsetInCU: 0x95C, offset: 0x27016, size: 0x4, addend: 0x0, symName: __ZL11patchBuf198, symObjAddr: 0x10B, symBinAddr: 0x436F, symSize: 0x0 }
  - { offsetInCU: 0x975, offset: 0x2702F, size: 0x4, addend: 0x0, symName: __ZL10patches131, symObjAddr: 0x9F4, symBinAddr: 0x4B7C, symSize: 0x0 }
  - { offsetInCU: 0x98E, offset: 0x27048, size: 0x4, addend: 0x0, symName: __ZL11patchBuf199, symObjAddr: 0x10F, symBinAddr: 0x4373, symSize: 0x0 }
  - { offsetInCU: 0x9A7, offset: 0x27061, size: 0x4, addend: 0x0, symName: __ZL11patchBuf200, symObjAddr: 0x113, symBinAddr: 0x4377, symSize: 0x0 }
  - { offsetInCU: 0x9C0, offset: 0x2707A, size: 0x4, addend: 0x0, symName: __ZL10patches132, symObjAddr: 0xA2C, symBinAddr: 0x4BB4, symSize: 0x0 }
  - { offsetInCU: 0x9D9, offset: 0x27093, size: 0x4, addend: 0x0, symName: __ZL11patchBuf201, symObjAddr: 0x117, symBinAddr: 0x437B, symSize: 0x0 }
  - { offsetInCU: 0x9FE, offset: 0x270B8, size: 0x4, addend: 0x0, symName: __ZL11patchBuf202, symObjAddr: 0x122, symBinAddr: 0x4386, symSize: 0x0 }
  - { offsetInCU: 0xA17, offset: 0x270D1, size: 0x4, addend: 0x0, symName: __ZL10patches133, symObjAddr: 0xA48, symBinAddr: 0x4BD0, symSize: 0x0 }
  - { offsetInCU: 0xA30, offset: 0x270EA, size: 0x4, addend: 0x0, symName: __ZL11patchBuf203, symObjAddr: 0x12D, symBinAddr: 0x4391, symSize: 0x0 }
  - { offsetInCU: 0xA49, offset: 0x27103, size: 0x4, addend: 0x0, symName: __ZL11patchBuf204, symObjAddr: 0x131, symBinAddr: 0x4395, symSize: 0x0 }
  - { offsetInCU: 0xA62, offset: 0x2711C, size: 0x4, addend: 0x0, symName: __ZL11patchBuf205, symObjAddr: 0x135, symBinAddr: 0x4399, symSize: 0x0 }
  - { offsetInCU: 0xA7B, offset: 0x27135, size: 0x4, addend: 0x0, symName: __ZL11patchBuf206, symObjAddr: 0x139, symBinAddr: 0x439D, symSize: 0x0 }
  - { offsetInCU: 0xA94, offset: 0x2714E, size: 0x4, addend: 0x0, symName: __ZL11patchBuf207, symObjAddr: 0x13D, symBinAddr: 0x43A1, symSize: 0x0 }
  - { offsetInCU: 0xAAD, offset: 0x27167, size: 0x4, addend: 0x0, symName: __ZL11patchBuf208, symObjAddr: 0x141, symBinAddr: 0x43A5, symSize: 0x0 }
  - { offsetInCU: 0xAC6, offset: 0x27180, size: 0x4, addend: 0x0, symName: __ZL10patches134, symObjAddr: 0xAB8, symBinAddr: 0x4C40, symSize: 0x0 }
  - { offsetInCU: 0xADF, offset: 0x27199, size: 0x4, addend: 0x0, symName: __ZL10patches135, symObjAddr: 0xAF0, symBinAddr: 0x4C78, symSize: 0x0 }
  - { offsetInCU: 0xAF8, offset: 0x271B2, size: 0x4, addend: 0x0, symName: __ZL10patches136, symObjAddr: 0xB0C, symBinAddr: 0x4C94, symSize: 0x0 }
  - { offsetInCU: 0xB11, offset: 0x271CB, size: 0x4, addend: 0x0, symName: __ZL11patchBuf209, symObjAddr: 0x145, symBinAddr: 0x43A9, symSize: 0x0 }
  - { offsetInCU: 0xB2A, offset: 0x271E4, size: 0x4, addend: 0x0, symName: __ZL11patchBuf210, symObjAddr: 0x149, symBinAddr: 0x43AD, symSize: 0x0 }
  - { offsetInCU: 0xB43, offset: 0x271FD, size: 0x4, addend: 0x0, symName: __ZL11patchBuf211, symObjAddr: 0x14D, symBinAddr: 0x43B1, symSize: 0x0 }
  - { offsetInCU: 0xB5C, offset: 0x27216, size: 0x4, addend: 0x0, symName: __ZL11patchBuf212, symObjAddr: 0x151, symBinAddr: 0x43B5, symSize: 0x0 }
  - { offsetInCU: 0xB75, offset: 0x2722F, size: 0x4, addend: 0x0, symName: __ZL10patches137, symObjAddr: 0xB44, symBinAddr: 0x4CCC, symSize: 0x0 }
  - { offsetInCU: 0xB8E, offset: 0x27248, size: 0x4, addend: 0x0, symName: __ZL11patchBuf213, symObjAddr: 0x155, symBinAddr: 0x43B9, symSize: 0x0 }
  - { offsetInCU: 0xBA7, offset: 0x27261, size: 0x4, addend: 0x0, symName: __ZL10patches138, symObjAddr: 0xB60, symBinAddr: 0x4CE8, symSize: 0x0 }
  - { offsetInCU: 0xBC0, offset: 0x2727A, size: 0x4, addend: 0x0, symName: __ZL10patches139, symObjAddr: 0xB98, symBinAddr: 0x4D20, symSize: 0x0 }
  - { offsetInCU: 0xBD9, offset: 0x27293, size: 0x4, addend: 0x0, symName: __ZL11patchBuf214, symObjAddr: 0x160, symBinAddr: 0x43C4, symSize: 0x0 }
  - { offsetInCU: 0xBF2, offset: 0x272AC, size: 0x4, addend: 0x0, symName: __ZL11patchBuf215, symObjAddr: 0x164, symBinAddr: 0x43C8, symSize: 0x0 }
  - { offsetInCU: 0xC0B, offset: 0x272C5, size: 0x4, addend: 0x0, symName: __ZL10patches140, symObjAddr: 0xBD0, symBinAddr: 0x4D58, symSize: 0x0 }
  - { offsetInCU: 0xC24, offset: 0x272DE, size: 0x4, addend: 0x0, symName: __ZL11patchBuf216, symObjAddr: 0x168, symBinAddr: 0x43CC, symSize: 0x0 }
  - { offsetInCU: 0xC3D, offset: 0x272F7, size: 0x4, addend: 0x0, symName: __ZL11patchBuf217, symObjAddr: 0x16C, symBinAddr: 0x43D0, symSize: 0x0 }
  - { offsetInCU: 0xC56, offset: 0x27310, size: 0x4, addend: 0x0, symName: __ZL10patches141, symObjAddr: 0xC08, symBinAddr: 0x4D90, symSize: 0x0 }
  - { offsetInCU: 0xC6F, offset: 0x27329, size: 0x4, addend: 0x0, symName: __ZL10patches142, symObjAddr: 0xC24, symBinAddr: 0x4DAC, symSize: 0x0 }
  - { offsetInCU: 0xC88, offset: 0x27342, size: 0x4, addend: 0x0, symName: __ZL11patchBuf218, symObjAddr: 0x170, symBinAddr: 0x43D4, symSize: 0x0 }
  - { offsetInCU: 0xCA1, offset: 0x2735B, size: 0x4, addend: 0x0, symName: __ZL11patchBuf219, symObjAddr: 0x174, symBinAddr: 0x43D8, symSize: 0x0 }
  - { offsetInCU: 0xCBA, offset: 0x27374, size: 0x4, addend: 0x0, symName: __ZL10patches143, symObjAddr: 0xC5C, symBinAddr: 0x4DE4, symSize: 0x0 }
  - { offsetInCU: 0xCD3, offset: 0x2738D, size: 0x4, addend: 0x0, symName: __ZL10patches144, symObjAddr: 0xC78, symBinAddr: 0x4E00, symSize: 0x0 }
  - { offsetInCU: 0xCEC, offset: 0x273A6, size: 0x4, addend: 0x0, symName: __ZL10patches145, symObjAddr: 0xC94, symBinAddr: 0x4E1C, symSize: 0x0 }
  - { offsetInCU: 0xD05, offset: 0x273BF, size: 0x4, addend: 0x0, symName: __ZL10patches146, symObjAddr: 0xCB0, symBinAddr: 0x4E38, symSize: 0x0 }
  - { offsetInCU: 0xD1E, offset: 0x273D8, size: 0x4, addend: 0x0, symName: __ZL11patchBuf220, symObjAddr: 0x178, symBinAddr: 0x43DC, symSize: 0x0 }
  - { offsetInCU: 0xD37, offset: 0x273F1, size: 0x4, addend: 0x0, symName: __ZL11patchBuf221, symObjAddr: 0x17C, symBinAddr: 0x43E0, symSize: 0x0 }
  - { offsetInCU: 0xD50, offset: 0x2740A, size: 0x4, addend: 0x0, symName: __ZL10patches147, symObjAddr: 0xCE8, symBinAddr: 0x4E70, symSize: 0x0 }
  - { offsetInCU: 0xD69, offset: 0x27423, size: 0x4, addend: 0x0, symName: __ZL10patches148, symObjAddr: 0xD04, symBinAddr: 0x4E8C, symSize: 0x0 }
  - { offsetInCU: 0xD82, offset: 0x2743C, size: 0x4, addend: 0x0, symName: __ZL11patchBuf222, symObjAddr: 0x180, symBinAddr: 0x43E4, symSize: 0x0 }
  - { offsetInCU: 0xD9B, offset: 0x27455, size: 0x4, addend: 0x0, symName: __ZL10patches149, symObjAddr: 0xD20, symBinAddr: 0x4EA8, symSize: 0x0 }
  - { offsetInCU: 0xDB4, offset: 0x2746E, size: 0x4, addend: 0x0, symName: __ZL10patches150, symObjAddr: 0xD3C, symBinAddr: 0x4EC4, symSize: 0x0 }
  - { offsetInCU: 0xDCD, offset: 0x27487, size: 0x4, addend: 0x0, symName: __ZL10patches151, symObjAddr: 0xD58, symBinAddr: 0x4EE0, symSize: 0x0 }
...
