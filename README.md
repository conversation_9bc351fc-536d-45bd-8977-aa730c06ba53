# EFI for Intel i7-9700K + ASUS ROG Strix Z390-E Gaming + RX 580 - macOS Big Sur

This EFI folder contains a complete OpenCore 1.0.3 configuration for running macOS Big Sur (11) on the following hardware:

## Hardware Configuration

- **CPU**: Intel Core i7-9700K (Coffee Lake-S, 8 cores)
- **Motherboard**: ASUS ROG Strix Z390-E Gaming
- **GPU**: AMD Radeon RX 580 (Primary)
- **iGPU**: Intel UHD Graphics 630 (Headless for compute tasks)
- **WiFi/Bluetooth**: FENVi T919 WiFi Card (BCM94360CD) - Native macOS support
- **Storage**: PNY CS2230 500GB M.2 NVMe SSD
- **RAM**: 16GB (2x8GB) DDR4 2666MHz
- **Audio**: Realtek ALC1220 (Layout ID: 11)
- **Ethernet**: Intel I219-V

## What's Working

✅ **macOS Big Sur (11.x)** - Full compatibility  
✅ **CPU Power Management** - Native Intel SpeedStep  
✅ **Graphics Acceleration** - RX 580 with full Metal support  
✅ **Audio** - All outputs including front panel  
✅ **Ethernet** - Intel I219-V with full speed  
✅ **WiFi & Bluetooth** - Native support via BCM94360CD  
✅ **USB Ports** - All ports mapped correctly  
✅ **Sleep/Wake** - Proper power management  
✅ **iServices** - iMessage, FaceTime, Handoff, etc.  

## BIOS Settings (ASUS ROG Strix Z390-E Gaming)

### Disable
- **Fast Boot**
- **Launch CSM**
- **Secure Boot**
  - Key Management → Clear Secure Boot Keys
- **System Agent (SA) Configuration**
  - VT-d
- **CFG Lock** (if available in advanced menu)
- **Onboard Devices Configuration**
  - Serial Port Configuration → Serial Port: Off

### Enable
- **System Agent (SA) Configuration**
  - Above 4G Decoding
  - Graphics Configuration
    - Primary Display: CPU Graphics (if using iGPU) or PCIe (if using dGPU only)
    - DVMT Pre-Allocated: 64MB
- **CPU Configuration**
  - Hyper-Threading
- **USB Configuration**
  - XHCI Hand-off
- **Secure Boot**
  - OS Type: Windows UEFI Mode

## Installation Instructions

1. **Create macOS Big Sur USB Installer**
   - Download macOS Big Sur from the App Store or use macrecovery
   - Format USB drive as GUID Partition Map
   - Create installer using `createinstallmedia` or similar tool

2. **Copy EFI to USB**
   - Mount the EFI partition of your USB drive
   - Copy the entire `EFI` folder to the root of the EFI partition

3. **Configure BIOS**
   - Apply all BIOS settings listed above
   - Save and exit

4. **Boot from USB**
   - Select the USB drive in BIOS boot menu
   - Choose "Install macOS Big Sur" from OpenCore picker
   - Follow standard macOS installation process

5. **Post-Installation**
   - Copy EFI folder to your internal drive's EFI partition
   - Generate new SMBIOS data for your system (recommended)
   - Update ROM value in config.plist with your ethernet MAC address

## Included Components

### OpenCore
- **Version**: 1.0.3
- **Configuration**: Optimized for Coffee Lake + Z390 + Big Sur

### Kexts
- **Lilu.kext** (1.7.1) - Patching framework
- **VirtualSMC.kext** (1.3.7) - SMC emulation
- **SMCProcessor.kext** (1.3.7) - CPU temperature monitoring
- **SMCSuperIO.kext** (1.3.7) - Fan monitoring
- **WhateverGreen.kext** (1.7.0) - Graphics patching
- **AppleALC.kext** (1.9.5) - Audio codec support
- **IntelMausi.kext** (1.0.8) - Intel ethernet driver
- **USBPorts.kext** - Custom USB mapping for ASUS ROG Strix Z390-E Gaming

### ACPI Patches
- **SSDT-PLUG-DRTNIA.aml** - CPU power management
- **SSDT-EC-USBX-DESKTOP.aml** - Embedded controller and USB power
- **SSDT-AWAC.aml** - System clock compatibility
- **SSDT-PMC.aml** - Power management controller

### Drivers
- **HfsPlus.efi** - HFS+ filesystem support
- **OpenRuntime.efi** - OpenCore runtime support

## SMBIOS Information

- **Model**: iMac19,1 (Optimal for i7-9700K)
- **Serial Numbers**: Pre-generated (should be changed for your system)

## Important Notes

1. **Generate Your Own SMBIOS**: The included serial numbers are placeholders. Generate your own using macserial or similar tools.

2. **Update ROM Value**: Replace the ROM value in config.plist with your ethernet MAC address for proper iServices functionality.

3. **WiFi Card**: This configuration assumes you have the FENVi T919 (BCM94360CD) card. If using Intel WiFi, you'll need additional kexts.

4. **USB Mapping**: The included USB mapping is specific to the ASUS ROG Strix Z390-E Gaming. If you have a different motherboard, you'll need to create your own mapping.

5. **Graphics**: RX 580 is natively supported. The iGPU (UHD 630) is configured for headless operation (compute tasks only).

## Troubleshooting

- **Boot Issues**: Check BIOS settings, ensure Secure Boot is disabled
- **Graphics Issues**: Verify RX 580 is properly seated and powered
- **Audio Issues**: Confirm layout-id=11 in boot-args and device properties
- **USB Issues**: Check if all ports are working, may need custom USB mapping
- **Sleep Issues**: Disable problematic USB ports in mapping

## Credits

- **OpenCore Team** - For the excellent bootloader
- **Acidanthera** - For all the essential kexts
- **Dortania** - For comprehensive installation guides
- **picopock** - For the ASUS ROG Strix Z390-E Gaming USB mapping
- **Community** - For extensive testing and feedback

## Disclaimer

This EFI configuration is provided as-is. Always backup your data before attempting to install macOS. The author is not responsible for any damage to your hardware or data loss.
