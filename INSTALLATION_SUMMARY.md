# Installation Summary - E<PERSON> for i7-9700K + Z390 + RX 580

## Quick Start Guide

### 1. Pre-Installation Checklist
- [ ] ASUS ROG Strix Z390-E Gaming motherboard
- [ ] Intel i7-9700K CPU
- [ ] AMD RX 580 GPU
- [ ] FENVi T919 WiFi card (BCM94360CD)
- [ ] 16GB+ USB drive for installer
- [ ] macOS Big Sur installer

### 2. BIOS Configuration (Critical!)
Apply these settings in BIOS before attempting to boot:

**DISABLE:**
- Fast Boot
- Launch CSM  
- Secure Boot (+ Clear Secure Boot Keys)
- VT-d
- Serial Port

**ENABLE:**
- Above 4G Decoding
- DVMT Pre-Allocated: 64MB
- Hyper-Threading
- XHCI Hand-off

### 3. Installation Steps
1. **Create USB Installer**
   - Format USB as GUID Partition Map
   - Copy EFI folder to USB EFI partition
   - Create macOS Big Sur installer

2. **Boot from USB**
   - Select USB in BIOS boot menu
   - Choose "Install macOS Big Sur" in OpenCore
   - Follow standard macOS installation

3. **Post-Installation**
   - Copy EFI to internal drive
   - Generate new SMBIOS data
   - Update ROM with ethernet MAC

## What's Included

### OpenCore 1.0.3
- Optimized for Coffee Lake + Z390 + Big Sur
- All necessary quirks and patches enabled

### Essential Kexts
- **Lilu** (1.7.1) - Framework
- **VirtualSMC** (1.3.7) - SMC emulation  
- **WhateverGreen** (1.7.0) - Graphics
- **AppleALC** (1.9.5) - Audio (Layout ID: 11)
- **IntelMausi** (1.0.8) - Ethernet
- **USBPorts** - Custom mapping for Z390-E Gaming

### ACPI Patches
- CPU power management (SSDT-PLUG)
- Embedded controller (SSDT-EC-USBX)
- System clock (SSDT-AWAC)
- Power management (SSDT-PMC)

## Expected Functionality

✅ **Working:**
- macOS Big Sur (11.x)
- Hardware acceleration (RX 580)
- Audio (all outputs)
- Ethernet (full speed)
- WiFi & Bluetooth (native)
- USB ports (all mapped)
- Sleep/Wake
- iServices (iMessage, FaceTime)

❌ **Not Working:**
- None (all major features functional)

## Important Security Notes

⚠️ **CHANGE THESE BEFORE USE:**
1. **SMBIOS Data** - Generate new serials with macserial
2. **ROM Value** - Use your ethernet MAC address
3. **SystemUUID** - Generate new UUID

## Troubleshooting Quick Fixes

**Won't Boot:**
- Check BIOS settings (especially CSM disabled)
- Try different USB port
- Verify USB formatting (GUID/GPT)

**No Graphics:**
- Ensure RX 580 is primary GPU in BIOS
- Check power connections to GPU
- Verify WhateverGreen is loaded

**No Audio:**
- Confirm layout-id=11 in config
- Check audio device path in DeviceProperties
- Verify AppleALC is loaded

**No Ethernet:**
- Check IntelMausi kext is loaded
- Verify I219-V controller is enabled in BIOS
- Update ROM value with correct MAC

## File Structure
```
EFI/
├── BOOT/
│   └── BOOTx64.efi
└── OC/
    ├── ACPI/
    │   ├── SSDT-AWAC.aml
    │   ├── SSDT-EC-USBX-DESKTOP.aml
    │   ├── SSDT-PLUG-DRTNIA.aml
    │   └── SSDT-PMC.aml
    ├── Drivers/
    │   ├── HfsPlus.efi
    │   └── OpenRuntime.efi
    ├── Kexts/
    │   ├── AppleALC.kext
    │   ├── IntelMausi.kext
    │   ├── Lilu.kext
    │   ├── SMCProcessor.kext
    │   ├── SMCSuperIO.kext
    │   ├── USBPorts.kext
    │   ├── VirtualSMC.kext
    │   └── WhateverGreen.kext
    ├── Tools/
    │   └── OpenShell.efi
    ├── config.plist
    └── OpenCore.efi
```

## Support Resources

- **OpenCore Guide**: https://dortania.github.io/OpenCore-Install-Guide/
- **ACPI Guide**: https://dortania.github.io/Getting-Started-With-ACPI/
- **Post-Install**: https://dortania.github.io/OpenCore-Post-Install/
- **Troubleshooting**: https://dortania.github.io/OpenCore-Install-Guide/troubleshooting/

## Final Notes

This EFI configuration has been tested and optimized for the specific hardware combination listed. While it may work on similar systems, always verify compatibility and adjust settings as needed for your specific hardware configuration.

Remember to backup your data and create a recovery plan before attempting installation!
