---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/work/WhateverGreen/WhateverGreen/build/Release/WhateverGreen.kext/Contents/MacOS/WhateverGreen'
relocations:
  - { offsetInCU: 0x10, offset: 0x10, size: 0x8, addend: 0x0, symName: _wrapVaddrPreSubmitTrampoline, symObjAddr: 0x0, symBinAddr: 0xE00, symSize: 0x22 }
  - { offsetInCU: 0x18, offset: 0x18, size: 0x8, addend: 0x0, symName: _orgVaddrPreSubmit, symObjAddr: 0x92, symBinAddr: 0x73EC0, symSize: 0x0 }
  - { offsetInCU: 0x44, offset: 0x44, size: 0x8, addend: 0x0, symName: _wrapVaddrPreSubmitTrampoline, symObjAddr: 0x0, symBinAddr: 0xE00, symSize: 0x22 }
  - { offsetInCU: 0x4B, offset: 0x4B, size: 0x8, addend: 0x0, symName: handle_rbx_off, symObjAddr: 0x22, symBinAddr: 0xE22, symSize: 0x21 }
  - { offsetInCU: 0x60, offset: 0x60, size: 0x8, addend: 0x0, symName: handle_r13_off, symObjAddr: 0x43, symBinAddr: 0xE43, symSize: 0x21 }
  - { offsetInCU: 0x75, offset: 0x75, size: 0x8, addend: 0x0, symName: handle_r12_off, symObjAddr: 0x64, symBinAddr: 0xE64, symSize: 0x21 }
  - { offsetInCU: 0x97, offset: 0x97, size: 0x8, addend: 0x0, symName: _orgVaddrPresubmitTrampoline, symObjAddr: 0x85, symBinAddr: 0xE85, symSize: 0xD }
  - { offsetInCU: 0x35, offset: 0xBD, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0x1CA80, symBinAddr: 0x73ED0, symSize: 0x0 }
  - { offsetInCU: 0x206, offset: 0x28E, size: 0x8, addend: 0x0, symName: __realmain, symObjAddr: 0x1CB48, symBinAddr: 0x73F98, symSize: 0x0 }
  - { offsetInCU: 0x21C, offset: 0x2A4, size: 0x8, addend: 0x0, symName: __antimain, symObjAddr: 0x1CB50, symBinAddr: 0x73FA0, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x2D4, size: 0x8, addend: 0x0, symName: __ZN4IGFX25AdvancedI2COverAUXSupportD1Ev, symObjAddr: 0x0, symBinAddr: 0xED0, symSize: 0x10 }
  - { offsetInCU: 0x1B5A4, offset: 0x1B851, size: 0x8, addend: 0x0, symName: __ZN4IGFX25AdvancedI2COverAUXSupportD1Ev, symObjAddr: 0x0, symBinAddr: 0xED0, symSize: 0x10 }
  - { offsetInCU: 0x1B5D9, offset: 0x1B886, size: 0x8, addend: 0x0, symName: __ZN4IGFX25AdvancedI2COverAUXSupportD0Ev, symObjAddr: 0x10, symBinAddr: 0xEE0, symSize: 0x10 }
  - { offsetInCU: 0x1B610, offset: 0x1B8BD, size: 0x8, addend: 0x0, symName: __ZN4IGFX25AdvancedI2COverAUXSupport4initEv, symObjAddr: 0x20, symBinAddr: 0xEF0, symSize: 0x10 }
  - { offsetInCU: 0x1B643, offset: 0x1B8F0, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule6deinitEv, symObjAddr: 0x30, symBinAddr: 0xF00, symSize: 0x10 }
  - { offsetInCU: 0x1B6C0, offset: 0x1B96D, size: 0x8, addend: 0x0, symName: __ZN4IGFX25AdvancedI2COverAUXSupport13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x40, symBinAddr: 0xF10, symSize: 0xB0 }
  - { offsetInCU: 0x1BA3D, offset: 0x1BCEA, size: 0x8, addend: 0x0, symName: __ZN4IGFX25AdvancedI2COverAUXSupport22processFramebufferKextER13KernelPatchermym, symObjAddr: 0xF0, symBinAddr: 0xFC0, symSize: 0xD0 }
  - { offsetInCU: 0x1BC7B, offset: 0x1BF28, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule19processGraphicsKextER13KernelPatchermym, symObjAddr: 0x1C0, symBinAddr: 0x1090, symSize: 0x10 }
  - { offsetInCU: 0x1BCEE, offset: 0x1BF9B, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule26disableDependentSubmodulesEv, symObjAddr: 0x1D0, symBinAddr: 0x10A0, symSize: 0x10 }
  - { offsetInCU: 0x1BD5C, offset: 0x1C009, size: 0x8, addend: 0x0, symName: __ZN4IGFX25AdvancedI2COverAUXSupport18wrapReadI2COverAUXEPvP15IORegistryEntryS1_jtPhbh, symObjAddr: 0x1E0, symBinAddr: 0x10B0, symSize: 0xA0 }
  - { offsetInCU: 0x1BE39, offset: 0x1C0E6, size: 0x8, addend: 0x0, symName: __ZN4IGFX25AdvancedI2COverAUXSupport19wrapWriteI2COverAUXEPvP15IORegistryEntryS1_jtPhb, symObjAddr: 0x280, symBinAddr: 0x1150, symSize: 0xA0 }
  - { offsetInCU: 0x1BFAA, offset: 0x1C257, size: 0x8, addend: 0x0, symName: __ZN4IGFX25AdvancedI2COverAUXSupport17advSeekI2COverAUXEPvP15IORegistryEntryS1_jjh, symObjAddr: 0x320, symBinAddr: 0x11F0, symSize: 0x130 }
  - { offsetInCU: 0x1C25A, offset: 0x1C507, size: 0x8, addend: 0x0, symName: __ZN4IGFX25AdvancedI2COverAUXSupport17advReadI2COverAUXEPvP15IORegistryEntryS1_jjtPhh, symObjAddr: 0x450, symBinAddr: 0x1320, symSize: 0x250 }
  - { offsetInCU: 0x27, offset: 0x1D020, size: 0x8, addend: 0x0, symName: __ZN4IGFX34FramebufferControllerAccessSupportD1Ev, symObjAddr: 0x6A0, symBinAddr: 0x1570, symSize: 0x10 }
  - { offsetInCU: 0x43, offset: 0x1D03C, size: 0x8, addend: 0x0, symName: __ZN4IGFX12callbackIGFXE, symObjAddr: 0x150618, symBinAddr: 0x75888, symSize: 0x0 }
  - { offsetInCU: 0x7260, offset: 0x24259, size: 0x8, addend: 0x0, symName: __ZN4IGFX34FramebufferControllerAccessSupportD1Ev, symObjAddr: 0x6A0, symBinAddr: 0x1570, symSize: 0x10 }
  - { offsetInCU: 0x7295, offset: 0x2428E, size: 0x8, addend: 0x0, symName: __ZN4IGFX34FramebufferControllerAccessSupportD0Ev, symObjAddr: 0x6B0, symBinAddr: 0x1580, symSize: 0x10 }
  - { offsetInCU: 0x72CC, offset: 0x242C5, size: 0x8, addend: 0x0, symName: __ZN4IGFX34FramebufferControllerAccessSupport4initEv, symObjAddr: 0x6C0, symBinAddr: 0x1590, symSize: 0x10 }
  - { offsetInCU: 0x7300, offset: 0x242F9, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule6deinitEv.3, symObjAddr: 0x6D0, symBinAddr: 0x15A0, symSize: 0x10 }
  - { offsetInCU: 0x732F, offset: 0x24328, size: 0x8, addend: 0x0, symName: __ZN4IGFX34FramebufferControllerAccessSupport13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x6E0, symBinAddr: 0x15B0, symSize: 0x1A0 }
  - { offsetInCU: 0x7538, offset: 0x24531, size: 0x8, addend: 0x0, symName: __ZN4IGFX34FramebufferControllerAccessSupport22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x880, symBinAddr: 0x1750, symSize: 0x120 }
  - { offsetInCU: 0x76CE, offset: 0x246C7, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule19processGraphicsKextER13KernelPatchermym.4, symObjAddr: 0x9A0, symBinAddr: 0x1870, symSize: 0x10 }
  - { offsetInCU: 0x7741, offset: 0x2473A, size: 0x8, addend: 0x0, symName: __ZN4IGFX34FramebufferControllerAccessSupport26disableDependentSubmodulesEv, symObjAddr: 0x9B0, symBinAddr: 0x1880, symSize: 0x170 }
  - { offsetInCU: 0x77DB, offset: 0x247D4, size: 0x8, addend: 0x0, symName: __ZN4IGFX24MMIORegistersReadSupportD1Ev, symObjAddr: 0xB20, symBinAddr: 0x19F0, symSize: 0x10 }
  - { offsetInCU: 0x7810, offset: 0x24809, size: 0x8, addend: 0x0, symName: __ZN4IGFX24MMIORegistersReadSupportD0Ev, symObjAddr: 0xB30, symBinAddr: 0x1A00, symSize: 0x10 }
  - { offsetInCU: 0x783D, offset: 0x24836, size: 0x8, addend: 0x0, symName: __ZN4IGFX24MMIORegistersReadSupport4initEv, symObjAddr: 0xB40, symBinAddr: 0x1A10, symSize: 0x10 }
  - { offsetInCU: 0x78AB, offset: 0x248A4, size: 0x8, addend: 0x0, symName: __ZN4IGFX24MMIORegistersReadSupport13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0xB50, symBinAddr: 0x1A20, symSize: 0x220 }
  - { offsetInCU: 0x7B46, offset: 0x24B3F, size: 0x8, addend: 0x0, symName: __ZN4IGFX24MMIORegistersReadSupport22processFramebufferKextER13KernelPatchermym, symObjAddr: 0xD70, symBinAddr: 0x1C40, symSize: 0x140 }
  - { offsetInCU: 0x7D7A, offset: 0x24D73, size: 0x8, addend: 0x0, symName: __ZN4IGFX24MMIORegistersReadSupport26disableDependentSubmodulesEv, symObjAddr: 0xEB0, symBinAddr: 0x1D80, symSize: 0x170 }
  - { offsetInCU: 0x7E14, offset: 0x24E0D, size: 0x8, addend: 0x0, symName: __ZThn16_N4IGFX24MMIORegistersReadSupportD1Ev, symObjAddr: 0x1020, symBinAddr: 0x1EF0, symSize: 0x10 }
  - { offsetInCU: 0x7E60, offset: 0x24E59, size: 0x8, addend: 0x0, symName: __ZThn16_N4IGFX24MMIORegistersReadSupportD0Ev, symObjAddr: 0x1030, symBinAddr: 0x1F00, symSize: 0x10 }
  - { offsetInCU: 0x7F8C, offset: 0x24F85, size: 0x8, addend: 0x0, symName: __ZN4IGFX24MMIORegistersReadSupport18wrapReadRegister32EPvj, symObjAddr: 0x1040, symBinAddr: 0x1F10, symSize: 0xD0 }
  - { offsetInCU: 0x80E0, offset: 0x250D9, size: 0x8, addend: 0x0, symName: __ZN4IGFX25MMIORegistersWriteSupportD1Ev, symObjAddr: 0x1110, symBinAddr: 0x1FE0, symSize: 0x10 }
  - { offsetInCU: 0x8115, offset: 0x2510E, size: 0x8, addend: 0x0, symName: __ZN4IGFX25MMIORegistersWriteSupportD0Ev, symObjAddr: 0x1120, symBinAddr: 0x1FF0, symSize: 0x10 }
  - { offsetInCU: 0x8142, offset: 0x2513B, size: 0x8, addend: 0x0, symName: __ZN4IGFX25MMIORegistersWriteSupport4initEv, symObjAddr: 0x1130, symBinAddr: 0x2000, symSize: 0x10 }
  - { offsetInCU: 0x8176, offset: 0x2516F, size: 0x8, addend: 0x0, symName: __ZN4IGFX25MMIORegistersWriteSupport13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x1140, symBinAddr: 0x2010, symSize: 0x220 }
  - { offsetInCU: 0x8399, offset: 0x25392, size: 0x8, addend: 0x0, symName: __ZN4IGFX25MMIORegistersWriteSupport22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x1360, symBinAddr: 0x2230, symSize: 0x140 }
  - { offsetInCU: 0x85CD, offset: 0x255C6, size: 0x8, addend: 0x0, symName: __ZN4IGFX25MMIORegistersWriteSupport26disableDependentSubmodulesEv, symObjAddr: 0x14A0, symBinAddr: 0x2370, symSize: 0x170 }
  - { offsetInCU: 0x8667, offset: 0x25660, size: 0x8, addend: 0x0, symName: __ZThn16_N4IGFX25MMIORegistersWriteSupportD1Ev, symObjAddr: 0x1610, symBinAddr: 0x24E0, symSize: 0x10 }
  - { offsetInCU: 0x86B3, offset: 0x256AC, size: 0x8, addend: 0x0, symName: __ZThn16_N4IGFX25MMIORegistersWriteSupportD0Ev, symObjAddr: 0x1620, symBinAddr: 0x24F0, symSize: 0x10 }
  - { offsetInCU: 0x8747, offset: 0x25740, size: 0x8, addend: 0x0, symName: __ZN4IGFX25MMIORegistersWriteSupport19wrapWriteRegister32EPvjj, symObjAddr: 0x1630, symBinAddr: 0x2500, symSize: 0xF0 }
  - { offsetInCU: 0x889B, offset: 0x25894, size: 0x8, addend: 0x0, symName: __ZN4IGFX20ForceCompleteModesetD1Ev, symObjAddr: 0x1720, symBinAddr: 0x25F0, symSize: 0x10 }
  - { offsetInCU: 0x88D0, offset: 0x258C9, size: 0x8, addend: 0x0, symName: __ZN4IGFX20ForceCompleteModesetD0Ev, symObjAddr: 0x1730, symBinAddr: 0x2600, symSize: 0x10 }
  - { offsetInCU: 0x8907, offset: 0x25900, size: 0x8, addend: 0x0, symName: __ZN4IGFX20ForceCompleteModeset4initEv, symObjAddr: 0x1740, symBinAddr: 0x2610, symSize: 0x10 }
  - { offsetInCU: 0x8E95, offset: 0x25E8E, size: 0x8, addend: 0x0, symName: __ZN4IGFX20ForceCompleteModeset13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x1750, symBinAddr: 0x2620, symSize: 0x200 }
  - { offsetInCU: 0x916E, offset: 0x26167, size: 0x8, addend: 0x0, symName: __ZN4IGFX20ForceCompleteModeset22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x1950, symBinAddr: 0x2820, symSize: 0x90 }
  - { offsetInCU: 0x92FA, offset: 0x262F3, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule26disableDependentSubmodulesEv.7, symObjAddr: 0x19E0, symBinAddr: 0x28B0, symSize: 0x10 }
  - { offsetInCU: 0x93FE, offset: 0x263F7, size: 0x8, addend: 0x0, symName: __ZN4IGFX20ForceCompleteModeset20wrapHwRegsNeedUpdateEPvP15IORegistryEntryS1_S1_S1_, symObjAddr: 0x19F0, symBinAddr: 0x28C0, symSize: 0x140 }
  - { offsetInCU: 0x9406, offset: 0x263FF, size: 0x8, addend: 0x0, symName: __ZN4IGFX18ForceOnlineDisplayD1Ev, symObjAddr: 0x1B30, symBinAddr: 0x2A00, symSize: 0x10 }
  - { offsetInCU: 0x951A, offset: 0x26513, size: 0x8, addend: 0x0, symName: __ZN4IGFX18ForceOnlineDisplayD1Ev, symObjAddr: 0x1B30, symBinAddr: 0x2A00, symSize: 0x10 }
  - { offsetInCU: 0x954F, offset: 0x26548, size: 0x8, addend: 0x0, symName: __ZN4IGFX18ForceOnlineDisplayD0Ev, symObjAddr: 0x1B40, symBinAddr: 0x2A10, symSize: 0x10 }
  - { offsetInCU: 0x9586, offset: 0x2657F, size: 0x8, addend: 0x0, symName: __ZN4IGFX18ForceOnlineDisplay4initEv, symObjAddr: 0x1B50, symBinAddr: 0x2A20, symSize: 0x10 }
  - { offsetInCU: 0x95BA, offset: 0x265B3, size: 0x8, addend: 0x0, symName: __ZN4IGFX18ForceOnlineDisplay13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x1B60, symBinAddr: 0x2A30, symSize: 0x1D0 }
  - { offsetInCU: 0x9893, offset: 0x2688C, size: 0x8, addend: 0x0, symName: __ZN4IGFX18ForceOnlineDisplay22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x1D30, symBinAddr: 0x2C00, symSize: 0x80 }
  - { offsetInCU: 0x9A25, offset: 0x26A1E, size: 0x8, addend: 0x0, symName: __ZN4IGFX18ForceOnlineDisplay20wrapGetDisplayStatusEP15IORegistryEntryPv, symObjAddr: 0x1DB0, symBinAddr: 0x2C80, symSize: 0xD0 }
  - { offsetInCU: 0x9B17, offset: 0x26B10, size: 0x8, addend: 0x0, symName: __ZN4IGFX12AGDCDisablerD1Ev, symObjAddr: 0x1E80, symBinAddr: 0x2D50, symSize: 0x10 }
  - { offsetInCU: 0x9B4C, offset: 0x26B45, size: 0x8, addend: 0x0, symName: __ZN4IGFX12AGDCDisablerD0Ev, symObjAddr: 0x1E90, symBinAddr: 0x2D60, symSize: 0x10 }
  - { offsetInCU: 0x9B83, offset: 0x26B7C, size: 0x8, addend: 0x0, symName: __ZN4IGFX12AGDCDisabler4initEv, symObjAddr: 0x1EA0, symBinAddr: 0x2D70, symSize: 0x10 }
  - { offsetInCU: 0x9BB7, offset: 0x26BB0, size: 0x8, addend: 0x0, symName: __ZN4IGFX12AGDCDisabler13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x1EB0, symBinAddr: 0x2D80, symSize: 0x50 }
  - { offsetInCU: 0x9D5C, offset: 0x26D55, size: 0x8, addend: 0x0, symName: __ZN4IGFX12AGDCDisabler22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x1F00, symBinAddr: 0x2DD0, symSize: 0x80 }
  - { offsetInCU: 0x9EEE, offset: 0x26EE7, size: 0x8, addend: 0x0, symName: __ZN4IGFX12AGDCDisabler23wrapFBClientDoAttributeEPvjPmmS2_S2_S1_, symObjAddr: 0x1F80, symBinAddr: 0x2E50, symSize: 0x20 }
  - { offsetInCU: 0x9F94, offset: 0x26F8D, size: 0x8, addend: 0x0, symName: __ZN4IGFX18TypeCCheckDisablerD1Ev, symObjAddr: 0x1FA0, symBinAddr: 0x2E70, symSize: 0x10 }
  - { offsetInCU: 0x9FC9, offset: 0x26FC2, size: 0x8, addend: 0x0, symName: __ZN4IGFX18TypeCCheckDisablerD0Ev, symObjAddr: 0x1FB0, symBinAddr: 0x2E80, symSize: 0x10 }
  - { offsetInCU: 0xA000, offset: 0x26FF9, size: 0x8, addend: 0x0, symName: __ZN4IGFX18TypeCCheckDisabler4initEv, symObjAddr: 0x1FC0, symBinAddr: 0x2E90, symSize: 0x10 }
  - { offsetInCU: 0xA034, offset: 0x2702D, size: 0x8, addend: 0x0, symName: __ZN4IGFX18TypeCCheckDisabler13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x1FD0, symBinAddr: 0x2EA0, symSize: 0xA0 }
  - { offsetInCU: 0xA16A, offset: 0x27163, size: 0x8, addend: 0x0, symName: __ZN4IGFX18TypeCCheckDisabler22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x2070, symBinAddr: 0x2F40, symSize: 0xB0 }
  - { offsetInCU: 0xA2BF, offset: 0x272B8, size: 0x8, addend: 0x0, symName: __ZN4IGFX18TypeCCheckDisabler21wrapIsTypeCOnlySystemEPv, symObjAddr: 0x2120, symBinAddr: 0x2FF0, symSize: 0x10 }
  - { offsetInCU: 0xA2F0, offset: 0x272E9, size: 0x8, addend: 0x0, symName: __ZN4IGFX14BlackScreenFixD1Ev, symObjAddr: 0x2130, symBinAddr: 0x3000, symSize: 0x10 }
  - { offsetInCU: 0xA325, offset: 0x2731E, size: 0x8, addend: 0x0, symName: __ZN4IGFX14BlackScreenFixD0Ev, symObjAddr: 0x2140, symBinAddr: 0x3010, symSize: 0x10 }
  - { offsetInCU: 0xA35C, offset: 0x27355, size: 0x8, addend: 0x0, symName: __ZN4IGFX14BlackScreenFix4initEv, symObjAddr: 0x2150, symBinAddr: 0x3020, symSize: 0x10 }
  - { offsetInCU: 0xA3BE, offset: 0x273B7, size: 0x8, addend: 0x0, symName: __ZN4IGFX14BlackScreenFix13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x2160, symBinAddr: 0x3030, symSize: 0x40 }
  - { offsetInCU: 0xA6B8, offset: 0x276B1, size: 0x8, addend: 0x0, symName: __ZN4IGFX14BlackScreenFix22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x21A0, symBinAddr: 0x3070, symSize: 0x100 }
  - { offsetInCU: 0xA9AC, offset: 0x279A5, size: 0x8, addend: 0x0, symName: __ZN4IGFX14BlackScreenFix27wrapComputeLaneCountNouveauEPvS1_iPi, symObjAddr: 0x22A0, symBinAddr: 0x3170, symSize: 0x30 }
  - { offsetInCU: 0xAA35, offset: 0x27A2E, size: 0x8, addend: 0x0, symName: __ZN4IGFX14BlackScreenFix20wrapComputeLaneCountEPvS1_jiPi, symObjAddr: 0x22D0, symBinAddr: 0x31A0, symSize: 0x30 }
  - { offsetInCU: 0xAAD3, offset: 0x27ACC, size: 0x8, addend: 0x0, symName: __ZN4IGFX12PAVPDisablerD1Ev, symObjAddr: 0x2300, symBinAddr: 0x31D0, symSize: 0x10 }
  - { offsetInCU: 0xAB08, offset: 0x27B01, size: 0x8, addend: 0x0, symName: __ZN4IGFX12PAVPDisablerD0Ev, symObjAddr: 0x2310, symBinAddr: 0x31E0, symSize: 0x10 }
  - { offsetInCU: 0xAB3F, offset: 0x27B38, size: 0x8, addend: 0x0, symName: __ZN4IGFX12PAVPDisabler4initEv, symObjAddr: 0x2320, symBinAddr: 0x31F0, symSize: 0x10 }
  - { offsetInCU: 0xAB7C, offset: 0x27B75, size: 0x8, addend: 0x0, symName: __ZN4IGFX12PAVPDisabler13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x2330, symBinAddr: 0x3200, symSize: 0xF0 }
  - { offsetInCU: 0xAC66, offset: 0x27C5F, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x2420, symBinAddr: 0x32F0, symSize: 0x10 }
  - { offsetInCU: 0xAE0C, offset: 0x27E05, size: 0x8, addend: 0x0, symName: __ZN4IGFX12PAVPDisabler19processGraphicsKextER13KernelPatchermym, symObjAddr: 0x2430, symBinAddr: 0x3300, symSize: 0xC0 }
  - { offsetInCU: 0xAFB4, offset: 0x27FAD, size: 0x8, addend: 0x0, symName: __ZN4IGFX12PAVPDisabler23wrapPavpSessionCallbackEPvijPjb, symObjAddr: 0x24F0, symBinAddr: 0x33C0, symSize: 0x20 }
  - { offsetInCU: 0xB031, offset: 0x2802A, size: 0x8, addend: 0x0, symName: __ZN4IGFX19ReadDescriptorPatchD1Ev, symObjAddr: 0x2510, symBinAddr: 0x33E0, symSize: 0x10 }
  - { offsetInCU: 0xB066, offset: 0x2805F, size: 0x8, addend: 0x0, symName: __ZN4IGFX19ReadDescriptorPatchD0Ev, symObjAddr: 0x2520, symBinAddr: 0x33F0, symSize: 0x10 }
  - { offsetInCU: 0xB09D, offset: 0x28096, size: 0x8, addend: 0x0, symName: __ZN4IGFX19ReadDescriptorPatch4initEv, symObjAddr: 0x2530, symBinAddr: 0x3400, symSize: 0x10 }
  - { offsetInCU: 0xB0D1, offset: 0x280CA, size: 0x8, addend: 0x0, symName: __ZN4IGFX19ReadDescriptorPatch13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x2540, symBinAddr: 0x3410, symSize: 0x30 }
  - { offsetInCU: 0xB1CC, offset: 0x281C5, size: 0x8, addend: 0x0, symName: __ZN4IGFX19ReadDescriptorPatch19processGraphicsKextER13KernelPatchermym, symObjAddr: 0x2570, symBinAddr: 0x3440, symSize: 0x80 }
  - { offsetInCU: 0xB2F0, offset: 0x282E9, size: 0x8, addend: 0x0, symName: __ZN4IGFX19ReadDescriptorPatch19globalPageTableReadEPvyRyS2_, symObjAddr: 0x25F0, symBinAddr: 0x34C0, symSize: 0x40 }
  - { offsetInCU: 0xB386, offset: 0x2837F, size: 0x8, addend: 0x0, symName: __ZN4IGFX24wrapCopyExistingServicesEP12OSDictionaryjj, symObjAddr: 0x2630, symBinAddr: 0x3500, symSize: 0xC0 }
  - { offsetInCU: 0xB5BF, offset: 0x285B8, size: 0x8, addend: 0x0, symName: __ZN4IGFX20wrapAcceleratorStartEP9IOServiceS1_, symObjAddr: 0x26F0, symBinAddr: 0x35C0, symSize: 0x530 }
  - { offsetInCU: 0xC1A2, offset: 0x2919B, size: 0x8, addend: 0x0, symName: __ZN4IGFX20wrapGetOSInformationEP9IOService, symObjAddr: 0x2C20, symBinAddr: 0x3AF0, symSize: 0x1F90 }
  - { offsetInCU: 0xD048, offset: 0x2A041, size: 0x8, addend: 0x0, symName: __ZN4IGFX12wrapTrainFDIEP9IOServiceiPv, symObjAddr: 0x4BB0, symBinAddr: 0x5A80, symSize: 0x120 }
  - { offsetInCU: 0xD1C7, offset: 0x2A1C0, size: 0x8, addend: 0x0, symName: __ZN4IGFX18applyDPtoHDMIPatchI14FramebufferCFLEEbjPT_, symObjAddr: 0x4CD0, symBinAddr: 0x5BA0, symSize: 0x80 }
  - { offsetInCU: 0xD2A7, offset: 0x2A2A0, size: 0x8, addend: 0x0, symName: __ZN4IGFX18applyDPtoHDMIPatchI16FramebufferICLLPEEbjPT_, symObjAddr: 0x4D50, symBinAddr: 0x5C20, symSize: 0xD0 }
  - { offsetInCU: 0xD3BB, offset: 0x2A3B4, size: 0x8, addend: 0x0, symName: __ZN4IGFX18applyDPtoHDMIPatchI16FramebufferICLHPEEbjPT_, symObjAddr: 0x4E20, symBinAddr: 0x5CF0, symSize: 0x70 }
  - { offsetInCU: 0xD50B, offset: 0x2A504, size: 0x8, addend: 0x0, symName: __ZN4IGFX33applyPlatformInformationListPatchI14FramebufferCFLEEbjPT_, symObjAddr: 0x4E90, symBinAddr: 0x5D60, symSize: 0x350 }
  - { offsetInCU: 0xD697, offset: 0x2A690, size: 0x8, addend: 0x0, symName: __ZN4IGFX33applyPlatformInformationListPatchI14FramebufferCNLEEbjPT_, symObjAddr: 0x51E0, symBinAddr: 0x60B0, symSize: 0x360 }
  - { offsetInCU: 0xD823, offset: 0x2A81C, size: 0x8, addend: 0x0, symName: __ZN4IGFX33applyPlatformInformationListPatchI16FramebufferICLLPEEbjPT_, symObjAddr: 0x5540, symBinAddr: 0x6410, symSize: 0x480 }
  - { offsetInCU: 0xD9AF, offset: 0x2A9A8, size: 0x8, addend: 0x0, symName: __ZN4IGFX33applyPlatformInformationListPatchI16FramebufferICLHPEEbjPT_, symObjAddr: 0x59C0, symBinAddr: 0x6890, symSize: 0x2C0 }
  - { offsetInCU: 0xDAFF, offset: 0x2AAF8, size: 0x8, addend: 0x0, symName: __ZN4IGFX17wrapLoadGuCBinaryEPvb, symObjAddr: 0x5C80, symBinAddr: 0x6B50, symSize: 0x30 }
  - { offsetInCU: 0xDB5A, offset: 0x2AB53, size: 0x8, addend: 0x0, symName: __ZN4IGFX16wrapLoadFirmwareEP9IOService, symObjAddr: 0x5CB0, symBinAddr: 0x6B80, symSize: 0x30 }
  - { offsetInCU: 0xDB8D, offset: 0x2AB86, size: 0x8, addend: 0x0, symName: __ZN4IGFX20wrapInitSchedControlEPvS0_, symObjAddr: 0x5CE0, symBinAddr: 0x6BB0, symSize: 0x30 }
  - { offsetInCU: 0xDC01, offset: 0x2ABFA, size: 0x8, addend: 0x0, symName: __ZN4IGFX23wrapIgBufferWithOptionsEPvmjj, symObjAddr: 0x5D10, symBinAddr: 0x6BE0, symSize: 0x1E0 }
  - { offsetInCU: 0xDD85, offset: 0x2AD7E, size: 0x8, addend: 0x0, symName: __ZN4IGFX32wrapIgBufferGetGpuVirtualAddressEPv, symObjAddr: 0x5EF0, symBinAddr: 0x6DC0, symSize: 0x70 }
  - { offsetInCU: 0xDDE5, offset: 0x2ADDE, size: 0x8, addend: 0x0, symName: __ZN4IGFX19wrapSystemWillSleepEP9IOService, symObjAddr: 0x5F60, symBinAddr: 0x6E30, symSize: 0x10 }
  - { offsetInCU: 0xDE16, offset: 0x2AE0F, size: 0x8, addend: 0x0, symName: __ZN4IGFX17wrapSystemDidWakeEP9IOService, symObjAddr: 0x5F70, symBinAddr: 0x6E40, symSize: 0x40 }
  - { offsetInCU: 0x153, offset: 0x2C0EE, size: 0x8, addend: 0x0, symName: __ZL8kextList, symObjAddr: 0x1CB60, symBinAddr: 0x73FB0, symSize: 0x0 }
  - { offsetInCU: 0x18C, offset: 0x2C127, size: 0x8, addend: 0x0, symName: __ZL9pathGKHal, symObjAddr: 0x1CCD8, symBinAddr: 0x74128, symSize: 0x0 }
  - { offsetInCU: 0x1C2, offset: 0x2C15D, size: 0x8, addend: 0x0, symName: __ZL9pathGKWeb, symObjAddr: 0x1CCE0, symBinAddr: 0x74130, symSize: 0x0 }
  - { offsetInCU: 0x1E0, offset: 0x2C17B, size: 0x8, addend: 0x0, symName: __ZL9pathGMWeb, symObjAddr: 0x1CCF0, symBinAddr: 0x74140, symSize: 0x0 }
  - { offsetInCU: 0x1FE, offset: 0x2C199, size: 0x8, addend: 0x0, symName: __ZL9pathGPWeb, symObjAddr: 0x1CD00, symBinAddr: 0x74150, symSize: 0x0 }
  - { offsetInCU: 0x21C, offset: 0x2C1B7, size: 0x8, addend: 0x0, symName: __ZL13procInfoYosEC, symObjAddr: 0x1CC00, symBinAddr: 0x74050, symSize: 0x0 }
  - { offsetInCU: 0x23A, offset: 0x2C1D5, size: 0x8, addend: 0x0, symName: __ZL14binaryModYosEC, symObjAddr: 0x1CC18, symBinAddr: 0x74068, symSize: 0x0 }
  - { offsetInCU: 0x258, offset: 0x2C1F3, size: 0x8, addend: 0x0, symName: __ZL17frameworkPatchOld, symObjAddr: 0x1CD10, symBinAddr: 0x74160, symSize: 0x0 }
  - { offsetInCU: 0x272, offset: 0x2C20D, size: 0x8, addend: 0x0, symName: __ZL16frameworkOldFind, symObjAddr: 0x27598, symBinAddr: 0x26B28, symSize: 0x0 }
  - { offsetInCU: 0x2A4, offset: 0x2C23F, size: 0x8, addend: 0x0, symName: __ZL16frameworkOldRepl, symObjAddr: 0x275A2, symBinAddr: 0x26B32, symSize: 0x0 }
  - { offsetInCU: 0x2C6, offset: 0x2C261, size: 0x8, addend: 0x0, symName: __ZL13procInfoSieHS, symObjAddr: 0x1CC50, symBinAddr: 0x740A0, symSize: 0x0 }
  - { offsetInCU: 0x2E4, offset: 0x2C27F, size: 0x8, addend: 0x0, symName: __ZL14binaryModSieHS, symObjAddr: 0x1CC68, symBinAddr: 0x740B8, symSize: 0x0 }
  - { offsetInCU: 0x302, offset: 0x2C29D, size: 0x8, addend: 0x0, symName: __ZL15binaryModHS1034, symObjAddr: 0x1CCA0, symBinAddr: 0x740F0, symSize: 0x0 }
  - { offsetInCU: 0x320, offset: 0x2C2BB, size: 0x8, addend: 0x0, symName: __ZL17frameworkPatchNew, symObjAddr: 0x1CD48, symBinAddr: 0x74198, symSize: 0x0 }
  - { offsetInCU: 0x33A, offset: 0x2C2D5, size: 0x8, addend: 0x0, symName: __ZL16frameworkNewFind, symObjAddr: 0x275AC, symBinAddr: 0x26B3C, symSize: 0x0 }
  - { offsetInCU: 0x36C, offset: 0x2C307, size: 0x8, addend: 0x0, symName: __ZL16frameworkNewRepl, symObjAddr: 0x275B5, symBinAddr: 0x26B45, symSize: 0x0 }
  - { offsetInCU: 0x38E, offset: 0x2C329, size: 0x8, addend: 0x0, symName: __ZL9gk100Find, symObjAddr: 0x27588, symBinAddr: 0x26B18, symSize: 0x0 }
  - { offsetInCU: 0x3AC, offset: 0x2C347, size: 0x8, addend: 0x0, symName: __ZL9gk100Repl, symObjAddr: 0x2758C, symBinAddr: 0x26B1C, symSize: 0x0 }
  - { offsetInCU: 0x3CA, offset: 0x2C365, size: 0x8, addend: 0x0, symName: __ZL10gmp100Find, symObjAddr: 0x27590, symBinAddr: 0x26B20, symSize: 0x0 }
  - { offsetInCU: 0x3E8, offset: 0x2C383, size: 0x8, addend: 0x0, symName: __ZL10gmp100Repl, symObjAddr: 0x27594, symBinAddr: 0x26B24, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x2C688, size: 0x8, addend: 0x0, symName: __ZN4IGFX18DPCDMaxLinkRateFixD1Ev, symObjAddr: 0x5FB0, symBinAddr: 0x6E80, symSize: 0x10 }
  - { offsetInCU: 0x114, offset: 0x2C775, size: 0x8, addend: 0x0, symName: __ZN4IGFX19HDMIDividersCalcFix21wrapComputeHdmiP0P1P2EPNS_31AppleIntelFramebufferControllerEjPvS3_, symObjAddr: 0x6EB0, symBinAddr: 0x7D80, symSize: 0x530 }
  - { offsetInCU: 0x140, offset: 0x2C7A1, size: 0x8, addend: 0x0, symName: __ZZN4IGFX19HDMIDividersCalcFix21wrapComputeHdmiP0P1P2EPNS_31AppleIntelFramebufferControllerEjPvS3_E8dividers, symObjAddr: 0x275C0, symBinAddr: 0x26B50, symSize: 0x0 }
  - { offsetInCU: 0x957, offset: 0x2CFB8, size: 0x8, addend: 0x0, symName: __ZN4IGFX18DPCDMaxLinkRateFixD1Ev, symObjAddr: 0x5FB0, symBinAddr: 0x6E80, symSize: 0x10 }
  - { offsetInCU: 0x98C, offset: 0x2CFED, size: 0x8, addend: 0x0, symName: __ZN4IGFX18DPCDMaxLinkRateFixD0Ev, symObjAddr: 0x5FC0, symBinAddr: 0x6E90, symSize: 0x10 }
  - { offsetInCU: 0x9C3, offset: 0x2D024, size: 0x8, addend: 0x0, symName: __ZN4IGFX18DPCDMaxLinkRateFix4initEv, symObjAddr: 0x5FD0, symBinAddr: 0x6EA0, symSize: 0x10 }
  - { offsetInCU: 0x9CB, offset: 0x2D02C, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule6deinitEv.180, symObjAddr: 0x5FE0, symBinAddr: 0x6EB0, symSize: 0x10 }
  - { offsetInCU: 0x9F6, offset: 0x2D057, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule6deinitEv.180, symObjAddr: 0x5FE0, symBinAddr: 0x6EB0, symSize: 0x10 }
  - { offsetInCU: 0xC0F, offset: 0x2D270, size: 0x8, addend: 0x0, symName: __ZN4IGFX18DPCDMaxLinkRateFix13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x5FF0, symBinAddr: 0x6EC0, symSize: 0x170 }
  - { offsetInCU: 0x11EB, offset: 0x2D84C, size: 0x8, addend: 0x0, symName: __ZN4IGFX18DPCDMaxLinkRateFix22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x6160, symBinAddr: 0x7030, symSize: 0x1C0 }
  - { offsetInCU: 0x1646, offset: 0x2DCA7, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule19processGraphicsKextER13KernelPatchermym.181, symObjAddr: 0x6320, symBinAddr: 0x71F0, symSize: 0x10 }
  - { offsetInCU: 0x16B9, offset: 0x2DD1A, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule26disableDependentSubmodulesEv.182, symObjAddr: 0x6330, symBinAddr: 0x7200, symSize: 0x10 }
  - { offsetInCU: 0x16E8, offset: 0x2DD49, size: 0x8, addend: 0x0, symName: __ZN4IGFX18DPCDMaxLinkRateFix14wrapCFLReadAUXEPNS_31AppleIntelFramebufferControllerEP15IORegistryEntryjtPvS5_, symObjAddr: 0x6340, symBinAddr: 0x7210, symSize: 0x30 }
  - { offsetInCU: 0x18DF, offset: 0x2DF40, size: 0x8, addend: 0x0, symName: __ZN4IGFX18DPCDMaxLinkRateFix11wrapReadAUXEjPvj, symObjAddr: 0x6370, symBinAddr: 0x7240, symSize: 0x450 }
  - { offsetInCU: 0x1B15, offset: 0x2E176, size: 0x8, addend: 0x0, symName: __ZN4IGFX18DPCDMaxLinkRateFix14wrapICLReadAUXEPNS_14AppleIntelPortEjPvj, symObjAddr: 0x67C0, symBinAddr: 0x7690, symSize: 0x20 }
  - { offsetInCU: 0x1B82, offset: 0x2E1E3, size: 0x8, addend: 0x0, symName: __ZN4IGFX19CoreDisplayClockFixD1Ev, symObjAddr: 0x67E0, symBinAddr: 0x76B0, symSize: 0x10 }
  - { offsetInCU: 0x1BB7, offset: 0x2E218, size: 0x8, addend: 0x0, symName: __ZN4IGFX19CoreDisplayClockFixD0Ev, symObjAddr: 0x67F0, symBinAddr: 0x76C0, symSize: 0x10 }
  - { offsetInCU: 0x1BEE, offset: 0x2E24F, size: 0x8, addend: 0x0, symName: __ZN4IGFX19CoreDisplayClockFix4initEv, symObjAddr: 0x6800, symBinAddr: 0x76D0, symSize: 0x10 }
  - { offsetInCU: 0x1BF6, offset: 0x2E257, size: 0x8, addend: 0x0, symName: __ZN4IGFX19CoreDisplayClockFix13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x6810, symBinAddr: 0x76E0, symSize: 0xC0 }
  - { offsetInCU: 0x1C22, offset: 0x2E283, size: 0x8, addend: 0x0, symName: __ZN4IGFX19CoreDisplayClockFix13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x6810, symBinAddr: 0x76E0, symSize: 0xC0 }
  - { offsetInCU: 0x1E7F, offset: 0x2E4E0, size: 0x8, addend: 0x0, symName: __ZN4IGFX19CoreDisplayClockFix22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x68D0, symBinAddr: 0x77A0, symSize: 0x190 }
  - { offsetInCU: 0x2288, offset: 0x2E8E9, size: 0x8, addend: 0x0, symName: __ZN4IGFX19CoreDisplayClockFix25wrapProbeCDClockFrequencyEPNS_31AppleIntelFramebufferControllerE, symObjAddr: 0x6A60, symBinAddr: 0x7930, symSize: 0x2E0 }
  - { offsetInCU: 0x242A, offset: 0x2EA8B, size: 0x8, addend: 0x0, symName: __ZN4IGFX19HDMIDividersCalcFixD1Ev, symObjAddr: 0x6D40, symBinAddr: 0x7C10, symSize: 0x10 }
  - { offsetInCU: 0x245F, offset: 0x2EAC0, size: 0x8, addend: 0x0, symName: __ZN4IGFX19HDMIDividersCalcFixD0Ev, symObjAddr: 0x6D50, symBinAddr: 0x7C20, symSize: 0x10 }
  - { offsetInCU: 0x2496, offset: 0x2EAF7, size: 0x8, addend: 0x0, symName: __ZN4IGFX19HDMIDividersCalcFix4initEv, symObjAddr: 0x6D60, symBinAddr: 0x7C30, symSize: 0x10 }
  - { offsetInCU: 0x24CA, offset: 0x2EB2B, size: 0x8, addend: 0x0, symName: __ZN4IGFX19HDMIDividersCalcFix13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x6D70, symBinAddr: 0x7C40, symSize: 0xC0 }
  - { offsetInCU: 0x2604, offset: 0x2EC65, size: 0x8, addend: 0x0, symName: __ZN4IGFX19HDMIDividersCalcFix22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x6E30, symBinAddr: 0x7D00, symSize: 0x80 }
  - { offsetInCU: 0x27A0, offset: 0x2EE01, size: 0x8, addend: 0x0, symName: __ZN4IGFX21MaxPixelClockOverrideD1Ev, symObjAddr: 0x73E0, symBinAddr: 0x82B0, symSize: 0x10 }
  - { offsetInCU: 0x27D5, offset: 0x2EE36, size: 0x8, addend: 0x0, symName: __ZN4IGFX21MaxPixelClockOverrideD0Ev, symObjAddr: 0x73F0, symBinAddr: 0x82C0, symSize: 0x10 }
  - { offsetInCU: 0x280C, offset: 0x2EE6D, size: 0x8, addend: 0x0, symName: __ZN4IGFX21MaxPixelClockOverride4initEv, symObjAddr: 0x7400, symBinAddr: 0x82D0, symSize: 0x10 }
  - { offsetInCU: 0x2840, offset: 0x2EEA1, size: 0x8, addend: 0x0, symName: __ZN4IGFX21MaxPixelClockOverride13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x7410, symBinAddr: 0x82E0, symSize: 0x140 }
  - { offsetInCU: 0x2AC9, offset: 0x2F12A, size: 0x8, addend: 0x0, symName: __ZN4IGFX21MaxPixelClockOverride22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x7550, symBinAddr: 0x8420, symSize: 0x80 }
  - { offsetInCU: 0x2C5B, offset: 0x2F2BC, size: 0x8, addend: 0x0, symName: __ZN4IGFX21MaxPixelClockOverride19wrapConnectionProbeEP9IOServicejj, symObjAddr: 0x75D0, symBinAddr: 0x84A0, symSize: 0x80 }
  - { offsetInCU: 0x27, offset: 0x2F3AE, size: 0x8, addend: 0x0, symName: __ZN3RAD15wrapSetPropertyEP15IORegistryEntryPKcPvj, symObjAddr: 0x7650, symBinAddr: 0x8520, symSize: 0x90 }
  - { offsetInCU: 0x45, offset: 0x2F3CC, size: 0x8, addend: 0x0, symName: __ZL18kextRadeonHardware, symObjAddr: 0x1CD80, symBinAddr: 0x741D0, symSize: 0x0 }
  - { offsetInCU: 0x78, offset: 0x2F3FF, size: 0x8, addend: 0x0, symName: __ZN3RAD11callbackRADE, symObjAddr: 0x1514F8, symBinAddr: 0x76768, symSize: 0x0 }
  - { offsetInCU: 0x1434, offset: 0x307BB, size: 0x8, addend: 0x0, symName: __ZZN3RAD22reprioritiseConnectorsEPKhhPN13RADConnectors9ConnectorEhE8typeList, symObjAddr: 0x276A0, symBinAddr: 0x26C30, symSize: 0x0 }
  - { offsetInCU: 0x155A, offset: 0x308E1, size: 0x8, addend: 0x0, symName: __ZL15pathRadeonX4000, symObjAddr: 0x1CEC0, symBinAddr: 0x74310, symSize: 0x0 }
  - { offsetInCU: 0x1578, offset: 0x308FF, size: 0x8, addend: 0x0, symName: __ZL15pathRadeonX5000, symObjAddr: 0x1CEC8, symBinAddr: 0x74318, symSize: 0x0 }
  - { offsetInCU: 0x1596, offset: 0x3091D, size: 0x8, addend: 0x0, symName: __ZL15pathRadeonX6000, symObjAddr: 0x1CED0, symBinAddr: 0x74320, symSize: 0x0 }
  - { offsetInCU: 0x15B4, offset: 0x3093B, size: 0x8, addend: 0x0, symName: __ZL15pathRadeonX3000, symObjAddr: 0x1CED8, symBinAddr: 0x74328, symSize: 0x0 }
  - { offsetInCU: 0x15D2, offset: 0x30959, size: 0x8, addend: 0x0, symName: __ZL15pathRadeonX4100, symObjAddr: 0x1CEE0, symBinAddr: 0x74330, symSize: 0x0 }
  - { offsetInCU: 0x15F0, offset: 0x30977, size: 0x8, addend: 0x0, symName: __ZL15pathRadeonX4150, symObjAddr: 0x1CEE8, symBinAddr: 0x74338, symSize: 0x0 }
  - { offsetInCU: 0x160E, offset: 0x30995, size: 0x8, addend: 0x0, symName: __ZL15pathRadeonX4200, symObjAddr: 0x1CEF0, symBinAddr: 0x74340, symSize: 0x0 }
  - { offsetInCU: 0x162C, offset: 0x309B3, size: 0x8, addend: 0x0, symName: __ZL15pathRadeonX4250, symObjAddr: 0x1CEF8, symBinAddr: 0x74348, symSize: 0x0 }
  - { offsetInCU: 0x164A, offset: 0x309D1, size: 0x8, addend: 0x0, symName: __ZL21kextRadeonFramebuffer, symObjAddr: 0x1CF00, symBinAddr: 0x74350, symSize: 0x0 }
  - { offsetInCU: 0x1668, offset: 0x309EF, size: 0x8, addend: 0x0, symName: __ZL15pathFramebuffer, symObjAddr: 0x1D068, symBinAddr: 0x744B8, symSize: 0x0 }
  - { offsetInCU: 0x1686, offset: 0x30A0D, size: 0x8, addend: 0x0, symName: __ZL27kextRadeonLegacyFramebuffer, symObjAddr: 0x1CF28, symBinAddr: 0x74378, symSize: 0x0 }
  - { offsetInCU: 0x16A4, offset: 0x30A2B, size: 0x8, addend: 0x0, symName: __ZL21pathLegacyFramebuffer, symObjAddr: 0x1D070, symBinAddr: 0x744C0, symSize: 0x0 }
  - { offsetInCU: 0x16C2, offset: 0x30A49, size: 0x8, addend: 0x0, symName: __ZL26kextRadeonX6000Framebuffer, symObjAddr: 0x1CF50, symBinAddr: 0x743A0, symSize: 0x0 }
  - { offsetInCU: 0x16E0, offset: 0x30A67, size: 0x8, addend: 0x0, symName: __ZL26pathRedeonX6000Framebuffer, symObjAddr: 0x1D078, symBinAddr: 0x744C8, symSize: 0x0 }
  - { offsetInCU: 0x16FE, offset: 0x30A85, size: 0x8, addend: 0x0, symName: __ZL17kextRadeonSupport, symObjAddr: 0x1CF78, symBinAddr: 0x743C8, symSize: 0x0 }
  - { offsetInCU: 0x171C, offset: 0x30AA3, size: 0x8, addend: 0x0, symName: __ZL11pathSupport, symObjAddr: 0x1D080, symBinAddr: 0x744D0, symSize: 0x0 }
  - { offsetInCU: 0x173A, offset: 0x30AC1, size: 0x8, addend: 0x0, symName: __ZL23kextRadeonLegacySupport, symObjAddr: 0x1CFA0, symBinAddr: 0x743F0, symSize: 0x0 }
  - { offsetInCU: 0x1758, offset: 0x30ADF, size: 0x8, addend: 0x0, symName: __ZL17pathLegacySupport, symObjAddr: 0x1D088, symBinAddr: 0x744D8, symSize: 0x0 }
  - { offsetInCU: 0x1776, offset: 0x30AFD, size: 0x8, addend: 0x0, symName: __ZL21kextPolarisController, symObjAddr: 0x1CFC8, symBinAddr: 0x74418, symSize: 0x0 }
  - { offsetInCU: 0x1794, offset: 0x30B1B, size: 0x8, addend: 0x0, symName: __ZL22patchPolarisController, symObjAddr: 0x1D090, symBinAddr: 0x744E0, symSize: 0x0 }
  - { offsetInCU: 0x17B2, offset: 0x30B39, size: 0x8, addend: 0x0, symName: __ZL16powerGatingFlags.0, symObjAddr: 0x1CFF0, symBinAddr: 0x74440, symSize: 0x0 }
  - { offsetInCU: 0x17BD, offset: 0x30B44, size: 0x8, addend: 0x0, symName: __ZL16powerGatingFlags.1, symObjAddr: 0x1D000, symBinAddr: 0x74450, symSize: 0x0 }
  - { offsetInCU: 0x17C8, offset: 0x30B4F, size: 0x8, addend: 0x0, symName: __ZL16powerGatingFlags.2, symObjAddr: 0x1D010, symBinAddr: 0x74460, symSize: 0x0 }
  - { offsetInCU: 0x17D3, offset: 0x30B5A, size: 0x8, addend: 0x0, symName: __ZL16powerGatingFlags.3, symObjAddr: 0x1D020, symBinAddr: 0x74470, symSize: 0x0 }
  - { offsetInCU: 0x17DE, offset: 0x30B65, size: 0x8, addend: 0x0, symName: __ZL16powerGatingFlags.4, symObjAddr: 0x1D030, symBinAddr: 0x74480, symSize: 0x0 }
  - { offsetInCU: 0x17E9, offset: 0x30B70, size: 0x8, addend: 0x0, symName: __ZL16powerGatingFlags.5, symObjAddr: 0x1D040, symBinAddr: 0x74490, symSize: 0x0 }
  - { offsetInCU: 0x17F4, offset: 0x30B7B, size: 0x8, addend: 0x0, symName: __ZL16powerGatingFlags.6, symObjAddr: 0x1D050, symBinAddr: 0x744A0, symSize: 0x0 }
  - { offsetInCU: 0x17FF, offset: 0x30B86, size: 0x8, addend: 0x0, symName: __ZL16powerGatingFlags.7, symObjAddr: 0x1D060, symBinAddr: 0x744B0, symSize: 0x0 }
  - { offsetInCU: 0x1E9F, offset: 0x31226, size: 0x8, addend: 0x0, symName: __ZN3RAD15wrapSetPropertyEP15IORegistryEntryPKcPvj, symObjAddr: 0x7650, symBinAddr: 0x8520, symSize: 0x90 }
  - { offsetInCU: 0x23DB, offset: 0x31762, size: 0x8, addend: 0x0, symName: __ZN3RAD15wrapGetPropertyEP15IORegistryEntryPKc, symObjAddr: 0x76E0, symBinAddr: 0x85B0, symSize: 0x6C0 }
  - { offsetInCU: 0x26F1, offset: 0x31A78, size: 0x8, addend: 0x0, symName: __ZN3RAD22wrapDcePanelCntlHwInitEPv, symObjAddr: 0x7DA0, symBinAddr: 0x8C70, symSize: 0x120 }
  - { offsetInCU: 0x27AA, offset: 0x31B31, size: 0x8, addend: 0x0, symName: __ZN3RAD50wrapAMDRadeonX6000AmdRadeonFramebufferSetAttributeEP9IOServiceijm, symObjAddr: 0x7EC0, symBinAddr: 0x8D90, symSize: 0x90 }
  - { offsetInCU: 0x2857, offset: 0x31BDE, size: 0x8, addend: 0x0, symName: __ZN3RAD50wrapAMDRadeonX6000AmdRadeonFramebufferGetAttributeEP9IOServiceijPm, symObjAddr: 0x7F50, symBinAddr: 0x8E20, symSize: 0x30 }
  - { offsetInCU: 0x296B, offset: 0x31CF2, size: 0x8, addend: 0x0, symName: __ZN3RAD18process24BitOutputER13KernelPatcherRNS0_8KextInfoEym, symObjAddr: 0x7F80, symBinAddr: 0x8E50, symSize: 0x1E0 }
  - { offsetInCU: 0x2F39, offset: 0x322C0, size: 0x8, addend: 0x0, symName: __ZN3RAD25processConnectorOverridesER13KernelPatcherymb, symObjAddr: 0x8160, symBinAddr: 0x9030, symSize: 0x460 }
  - { offsetInCU: 0x37B5, offset: 0x32B3C, size: 0x8, addend: 0x0, symName: __ZN3RAD13doNotTestVramEP9IOServicejb, symObjAddr: 0x85C0, symBinAddr: 0x9490, symSize: 0x10 }
  - { offsetInCU: 0x3808, offset: 0x32B8F, size: 0x8, addend: 0x0, symName: __ZN3RAD20wrapNotifyLinkChangeEPv31kAGDCRegisterLinkControlEvent_tS0_j, symObjAddr: 0x85D0, symBinAddr: 0x94A0, symSize: 0x40 }
  - { offsetInCU: 0x38B7, offset: 0x32C3E, size: 0x8, addend: 0x0, symName: __ZN3RAD23findProjectByPartNumberEP9IOServicePv, symObjAddr: 0x8610, symBinAddr: 0x94E0, symSize: 0x10 }
  - { offsetInCU: 0x3945, offset: 0x32CCC, size: 0x8, addend: 0x0, symName: __ZN3RAD23wrapGetConnectorsInfoV1EPvPN13RADConnectors9ConnectorEPh, symObjAddr: 0x8620, symBinAddr: 0x94F0, symSize: 0x120 }
  - { offsetInCU: 0x3A3A, offset: 0x32DC1, size: 0x8, addend: 0x0, symName: __ZN3RAD23wrapGetConnectorsInfoV2EPvPN13RADConnectors9ConnectorEPh, symObjAddr: 0x8740, symBinAddr: 0x9610, symSize: 0xF0 }
  - { offsetInCU: 0x3E61, offset: 0x331E8, size: 0x8, addend: 0x0, symName: __ZN3RAD32wrapTranslateAtomConnectorInfoV1EPvPN13RADConnectors17AtomConnectorInfoEPNS1_9ConnectorE, symObjAddr: 0x8830, symBinAddr: 0x9700, symSize: 0x1D0 }
  - { offsetInCU: 0x4206, offset: 0x3358D, size: 0x8, addend: 0x0, symName: __ZN3RAD32wrapTranslateAtomConnectorInfoV2EPvPN13RADConnectors17AtomConnectorInfoEPNS1_9ConnectorE, symObjAddr: 0x8A00, symBinAddr: 0x98D0, symSize: 0x1A0 }
  - { offsetInCU: 0x4623, offset: 0x339AA, size: 0x8, addend: 0x0, symName: __ZN3RAD22wrapATIControllerStartEP9IOServiceS1_, symObjAddr: 0x8BA0, symBinAddr: 0x9A70, symSize: 0x240 }
  - { offsetInCU: 0x479E, offset: 0x33B25, size: 0x8, addend: 0x0, symName: __ZN3RAD27wrapLegacyGetConnectorsInfoEPvPN13RADConnectors9ConnectorEPh, symObjAddr: 0x8DE0, symBinAddr: 0x9CB0, symSize: 0x100 }
  - { offsetInCU: 0x4875, offset: 0x33BFC, size: 0x8, addend: 0x0, symName: __ZN3RAD28wrapLegacyATIControllerStartEP9IOServiceS1_, symObjAddr: 0x8EE0, symBinAddr: 0x9DB0, symSize: 0x250 }
  - { offsetInCU: 0x4DE8, offset: 0x3416F, size: 0x8, addend: 0x0, symName: __ZN3RAD20updateConnectorsInfoEPvPFS0_S0_19AtomObjectTableTypePhEP9IOServicePN13RADConnectors9ConnectorES2_, symObjAddr: 0x9130, symBinAddr: 0xA000, symSize: 0xAC0 }
  - { offsetInCU: 0x56EF, offset: 0x34A76, size: 0x8, addend: 0x0, symName: __ZN3RAD17updateAccelConfigEmP9IOServicePPKc, symObjAddr: 0x9BF0, symBinAddr: 0xAAC0, symSize: 0x8B0 }
  - { offsetInCU: 0x59E6, offset: 0x34D6D, size: 0x8, addend: 0x0, symName: __ZN3RAD15updateGetHWInfoEP9IOServicePv, symObjAddr: 0xA4A0, symBinAddr: 0xB370, symSize: 0x190 }
  - { offsetInCU: 0x27, offset: 0x35BA3, size: 0x8, addend: 0x0, symName: __ZN28BrightnessRequestEventSourceD1Ev, symObjAddr: 0xA630, symBinAddr: 0xB500, symSize: 0x10 }
  - { offsetInCU: 0x2F, offset: 0x35BAB, size: 0x8, addend: 0x0, symName: __ZN4IGFX15RPSControlPatchD1Ev, symObjAddr: 0xCC20, symBinAddr: 0xDAF0, symSize: 0x10 }
  - { offsetInCU: 0xC4, offset: 0x35C40, size: 0x8, addend: 0x0, symName: __ZN4IGFX24BacklightRegistersAltFix22processFramebufferKextER13KernelPatchermym, symObjAddr: 0xAC70, symBinAddr: 0xBB40, symSize: 0x410 }
  - { offsetInCU: 0x46A, offset: 0x35FE6, size: 0x8, addend: 0x0, symName: __ZN4IGFX24BacklightRegistersAltFix23revertInlinedInvocationERKNS0_18FunctionDescriptorERKNS0_12ProbeContextERKNS0_17InvocationContextER13KernelPatchery, symObjAddr: 0xB580, symBinAddr: 0xC450, symSize: 0x290 }
  - { offsetInCU: 0x496, offset: 0x36012, size: 0x8, addend: 0x0, symName: __ZZN4IGFX24BacklightRegistersAltFix23revertInlinedInvocationERKNS0_18FunctionDescriptorERKNS0_12ProbeContextERKNS0_17InvocationContextER13KernelPatcheryE9kPreserve, symObjAddr: 0x276B4, symBinAddr: 0x26C44, symSize: 0x0 }
  - { offsetInCU: 0x4B1, offset: 0x3602D, size: 0x8, addend: 0x0, symName: __ZZN4IGFX24BacklightRegistersAltFix23revertInlinedInvocationERKNS0_18FunctionDescriptorERKNS0_12ProbeContextERKNS0_17InvocationContextER13KernelPatcheryE8kSetArg1, symObjAddr: 0x276C1, symBinAddr: 0x26C51, symSize: 0x0 }
  - { offsetInCU: 0x4CC, offset: 0x36048, size: 0x8, addend: 0x0, symName: __ZZN4IGFX24BacklightRegistersAltFix23revertInlinedInvocationERKNS0_18FunctionDescriptorERKNS0_12ProbeContextERKNS0_17InvocationContextER13KernelPatcheryE12kSetArg1_r12, symObjAddr: 0x276C8, symBinAddr: 0x26C58, symSize: 0x0 }
  - { offsetInCU: 0x4E7, offset: 0x36063, size: 0x8, addend: 0x0, symName: __ZZN4IGFX24BacklightRegistersAltFix23revertInlinedInvocationERKNS0_18FunctionDescriptorERKNS0_12ProbeContextERKNS0_17InvocationContextER13KernelPatcheryE13kSetArg1_Zero, symObjAddr: 0x276D0, symBinAddr: 0x26C60, symSize: 0x0 }
  - { offsetInCU: 0x502, offset: 0x3607E, size: 0x8, addend: 0x0, symName: __ZZN4IGFX24BacklightRegistersAltFix23revertInlinedInvocationERKNS0_18FunctionDescriptorERKNS0_12ProbeContextERKNS0_17InvocationContextER13KernelPatcheryE8kSetArg0, symObjAddr: 0x276D2, symBinAddr: 0x26C62, symSize: 0x0 }
  - { offsetInCU: 0x521, offset: 0x3609D, size: 0x8, addend: 0x0, symName: __ZZN4IGFX24BacklightRegistersAltFix23revertInlinedInvocationERKNS0_18FunctionDescriptorERKNS0_12ProbeContextERKNS0_17InvocationContextER13KernelPatcheryE5kCall, symObjAddr: 0x276D5, symBinAddr: 0x26C65, symSize: 0x0 }
  - { offsetInCU: 0x538, offset: 0x360B4, size: 0x8, addend: 0x0, symName: __ZZN4IGFX24BacklightRegistersAltFix23revertInlinedInvocationERKNS0_18FunctionDescriptorERKNS0_12ProbeContextERKNS0_17InvocationContextER13KernelPatcheryE8kRestore, symObjAddr: 0x276DA, symBinAddr: 0x26C6A, symSize: 0x0 }
  - { offsetInCU: 0x553, offset: 0x360CF, size: 0x8, addend: 0x0, symName: __ZZN4IGFX24BacklightRegistersAltFix23revertInlinedInvocationERKNS0_18FunctionDescriptorERKNS0_12ProbeContextERKNS0_17InvocationContextER13KernelPatcheryE5kJump, symObjAddr: 0x276E7, symBinAddr: 0x26C77, symSize: 0x0 }
  - { offsetInCU: 0x6EB, offset: 0x36267, size: 0x8, addend: 0x0, symName: __ZNK4IGFX27BacklightRegistersAltFixKBL22getFunctionDescriptorsEv, symObjAddr: 0xB2A0, symBinAddr: 0xC170, symSize: 0x10 }
  - { offsetInCU: 0x74E, offset: 0x362CA, size: 0x8, addend: 0x0, symName: __ZZNK4IGFX27BacklightRegistersAltFixKBL22getFunctionDescriptorsEvE20kFunctionDescriptors, symObjAddr: 0x1D0A0, symBinAddr: 0x744F0, symSize: 0x0 }
  - { offsetInCU: 0x77F, offset: 0x362FB, size: 0x8, addend: 0x0, symName: __ZNK4IGFX27BacklightRegistersAltFixCFL22getFunctionDescriptorsEv, symObjAddr: 0xBD10, symBinAddr: 0xCBE0, symSize: 0x10 }
  - { offsetInCU: 0x7D1, offset: 0x3634D, size: 0x8, addend: 0x0, symName: __ZZNK4IGFX27BacklightRegistersAltFixCFL22getFunctionDescriptorsEvE20kFunctionDescriptors, symObjAddr: 0x1D160, symBinAddr: 0x745B0, symSize: 0x0 }
  - { offsetInCU: 0x80C, offset: 0x36388, size: 0x8, addend: 0x0, symName: __ZN28BrightnessRequestEventSource10gMetaClassE, symObjAddr: 0x1505D8, symBinAddr: 0x75848, symSize: 0x0 }
  - { offsetInCU: 0x852, offset: 0x363CE, size: 0x8, addend: 0x0, symName: __ZN28BrightnessRequestEventSourceD1Ev, symObjAddr: 0xA630, symBinAddr: 0xB500, symSize: 0x10 }
  - { offsetInCU: 0x8CF, offset: 0x3644B, size: 0x8, addend: 0x0, symName: __ZN28BrightnessRequestEventSourceD0Ev, symObjAddr: 0xA640, symBinAddr: 0xB510, symSize: 0x30 }
  - { offsetInCU: 0x95F, offset: 0x364DB, size: 0x8, addend: 0x0, symName: __ZNK28BrightnessRequestEventSource12getMetaClassEv, symObjAddr: 0xA670, symBinAddr: 0xB540, symSize: 0x10 }
  - { offsetInCU: 0xACC, offset: 0x36648, size: 0x8, addend: 0x0, symName: __ZN28BrightnessRequestEventSource12checkForWorkEv, symObjAddr: 0xA680, symBinAddr: 0xB550, symSize: 0x200 }
  - { offsetInCU: 0xE98, offset: 0x36A14, size: 0x8, addend: 0x0, symName: __ZN28BrightnessRequestEventSource9MetaClassD1Ev, symObjAddr: 0xA880, symBinAddr: 0xB750, symSize: 0x10 }
  - { offsetInCU: 0xF13, offset: 0x36A8F, size: 0x8, addend: 0x0, symName: __ZN28BrightnessRequestEventSource9MetaClassD0Ev, symObjAddr: 0xA890, symBinAddr: 0xB760, symSize: 0x10 }
  - { offsetInCU: 0xFE8, offset: 0x36B64, size: 0x8, addend: 0x0, symName: __ZNK28BrightnessRequestEventSource9MetaClass5allocEv, symObjAddr: 0xA8A0, symBinAddr: 0xB770, symSize: 0x40 }
  - { offsetInCU: 0xFF0, offset: 0x36B6C, size: 0x8, addend: 0x0, symName: __ZN4IGFX21BacklightRegistersFixD1Ev, symObjAddr: 0xA8E0, symBinAddr: 0xB7B0, symSize: 0x10 }
  - { offsetInCU: 0x1070, offset: 0x36BEC, size: 0x8, addend: 0x0, symName: __ZN4IGFX21BacklightRegistersFixD1Ev, symObjAddr: 0xA8E0, symBinAddr: 0xB7B0, symSize: 0x10 }
  - { offsetInCU: 0x10A5, offset: 0x36C21, size: 0x8, addend: 0x0, symName: __ZN4IGFX21BacklightRegistersFixD0Ev, symObjAddr: 0xA8F0, symBinAddr: 0xB7C0, symSize: 0x10 }
  - { offsetInCU: 0x10DC, offset: 0x36C58, size: 0x8, addend: 0x0, symName: __ZN4IGFX21BacklightRegistersFix4initEv, symObjAddr: 0xA900, symBinAddr: 0xB7D0, symSize: 0x10 }
  - { offsetInCU: 0x10E4, offset: 0x36C60, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule6deinitEv.362, symObjAddr: 0xA910, symBinAddr: 0xB7E0, symSize: 0x10 }
  - { offsetInCU: 0x110F, offset: 0x36C8B, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule6deinitEv.362, symObjAddr: 0xA910, symBinAddr: 0xB7E0, symSize: 0x10 }
  - { offsetInCU: 0x1239, offset: 0x36DB5, size: 0x8, addend: 0x0, symName: __ZN4IGFX21BacklightRegistersFix13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0xA920, symBinAddr: 0xB7F0, symSize: 0x140 }
  - { offsetInCU: 0x1461, offset: 0x36FDD, size: 0x8, addend: 0x0, symName: __ZN4IGFX21BacklightRegistersFix22processFramebufferKextER13KernelPatchermym, symObjAddr: 0xAA60, symBinAddr: 0xB930, symSize: 0x100 }
  - { offsetInCU: 0x15EC, offset: 0x37168, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule19processGraphicsKextER13KernelPatchermym.363, symObjAddr: 0xAB60, symBinAddr: 0xBA30, symSize: 0x10 }
  - { offsetInCU: 0x165F, offset: 0x371DB, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule26disableDependentSubmodulesEv.364, symObjAddr: 0xAB70, symBinAddr: 0xBA40, symSize: 0x10 }
  - { offsetInCU: 0x168E, offset: 0x3720A, size: 0x8, addend: 0x0, symName: __ZN4IGFX24BacklightRegistersAltFixD1Ev, symObjAddr: 0xAB80, symBinAddr: 0xBA50, symSize: 0x10 }
  - { offsetInCU: 0x16C3, offset: 0x3723F, size: 0x8, addend: 0x0, symName: __ZN4IGFX24BacklightRegistersAltFixD0Ev, symObjAddr: 0xAB90, symBinAddr: 0xBA60, symSize: 0x10 }
  - { offsetInCU: 0x16FA, offset: 0x37276, size: 0x8, addend: 0x0, symName: __ZN4IGFX24BacklightRegistersAltFix4initEv, symObjAddr: 0xABA0, symBinAddr: 0xBA70, symSize: 0x10 }
  - { offsetInCU: 0x1702, offset: 0x3727E, size: 0x8, addend: 0x0, symName: __ZN4IGFX24BacklightRegistersAltFix13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0xABB0, symBinAddr: 0xBA80, symSize: 0xC0 }
  - { offsetInCU: 0x172D, offset: 0x372A9, size: 0x8, addend: 0x0, symName: __ZN4IGFX24BacklightRegistersAltFix13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0xABB0, symBinAddr: 0xBA80, symSize: 0xC0 }
  - { offsetInCU: 0x19ED, offset: 0x37569, size: 0x8, addend: 0x0, symName: __ZNK4IGFX24BacklightRegistersAltFix18probeMemberOffsetsEym, symObjAddr: 0xB080, symBinAddr: 0xBF50, symSize: 0x10 }
  - { offsetInCU: 0x1A3E, offset: 0x375BA, size: 0x8, addend: 0x0, symName: __ZNK4IGFX24BacklightRegistersAltFix22getFunctionDescriptorsEv, symObjAddr: 0xB090, symBinAddr: 0xBF60, symSize: 0x10 }
  - { offsetInCU: 0x1A6D, offset: 0x375E9, size: 0x8, addend: 0x0, symName: __ZNK4IGFX24BacklightRegistersAltFix24getHwSetBacklightWrapperEv, symObjAddr: 0xB0A0, symBinAddr: 0xBF70, symSize: 0x10 }
  - { offsetInCU: 0x1A9C, offset: 0x37618, size: 0x8, addend: 0x0, symName: __ZN4IGFX27BacklightRegistersAltFixKBLD1Ev, symObjAddr: 0xB0B0, symBinAddr: 0xBF80, symSize: 0x10 }
  - { offsetInCU: 0x1AD1, offset: 0x3764D, size: 0x8, addend: 0x0, symName: __ZN4IGFX27BacklightRegistersAltFixKBLD0Ev, symObjAddr: 0xB0C0, symBinAddr: 0xBF90, symSize: 0x10 }
  - { offsetInCU: 0x1BAF, offset: 0x3772B, size: 0x8, addend: 0x0, symName: __ZNK4IGFX27BacklightRegistersAltFixKBL18probeMemberOffsetsEym, symObjAddr: 0xB0D0, symBinAddr: 0xBFA0, symSize: 0x1D0 }
  - { offsetInCU: 0x1D4C, offset: 0x378C8, size: 0x8, addend: 0x0, symName: __ZNK4IGFX27BacklightRegistersAltFixKBL24getHwSetBacklightWrapperEv, symObjAddr: 0xB2B0, symBinAddr: 0xC180, symSize: 0x10 }
  - { offsetInCU: 0x1FE7, offset: 0x37B63, size: 0x8, addend: 0x0, symName: __ZN4IGFX27BacklightRegistersAltFixKBL18wrapHwSetBacklightEPvj, symObjAddr: 0xB2C0, symBinAddr: 0xC190, symSize: 0x150 }
  - { offsetInCU: 0x247F, offset: 0x37FFB, size: 0x8, addend: 0x0, symName: __ZN4IGFX27BacklightRegistersAltFixKBL33probeInlinedInvocation_LightUpEDPERKNS_24BacklightRegistersAltFix18FunctionDescriptorERKNS1_12ProbeContextE, symObjAddr: 0xB410, symBinAddr: 0xC2E0, symSize: 0x170 }
  - { offsetInCU: 0x2653, offset: 0x381CF, size: 0x8, addend: 0x0, symName: __ZN4IGFX27BacklightRegistersAltFixKBL37probeInlinedInvocation_DisableDisplayERKNS_24BacklightRegistersAltFix18FunctionDescriptorERKNS1_12ProbeContextE, symObjAddr: 0xB810, symBinAddr: 0xC6E0, symSize: 0x180 }
  - { offsetInCU: 0x2833, offset: 0x383AF, size: 0x8, addend: 0x0, symName: __ZN4IGFX27BacklightRegistersAltFixKBL38probeInlinedInvocation_HwSetPanelPowerERKNS_24BacklightRegistersAltFix18FunctionDescriptorERKNS1_12ProbeContextE, symObjAddr: 0xB990, symBinAddr: 0xC860, symSize: 0x180 }
  - { offsetInCU: 0x29EA, offset: 0x38566, size: 0x8, addend: 0x0, symName: __ZN4IGFX27BacklightRegistersAltFixCFLD1Ev, symObjAddr: 0xBB10, symBinAddr: 0xC9E0, symSize: 0x10 }
  - { offsetInCU: 0x2A1F, offset: 0x3859B, size: 0x8, addend: 0x0, symName: __ZN4IGFX27BacklightRegistersAltFixCFLD0Ev, symObjAddr: 0xBB20, symBinAddr: 0xC9F0, symSize: 0x10 }
  - { offsetInCU: 0x2AA1, offset: 0x3861D, size: 0x8, addend: 0x0, symName: __ZNK4IGFX27BacklightRegistersAltFixCFL18probeMemberOffsetsEym, symObjAddr: 0xBB30, symBinAddr: 0xCA00, symSize: 0x1E0 }
  - { offsetInCU: 0x2C67, offset: 0x387E3, size: 0x8, addend: 0x0, symName: __ZNK4IGFX27BacklightRegistersAltFixCFL24getHwSetBacklightWrapperEv, symObjAddr: 0xBD20, symBinAddr: 0xCBF0, symSize: 0x10 }
  - { offsetInCU: 0x2C99, offset: 0x38815, size: 0x8, addend: 0x0, symName: __ZN4IGFX27BacklightRegistersAltFixCFL18wrapHwSetBacklightEPvj, symObjAddr: 0xBD30, symBinAddr: 0xCC00, symSize: 0x100 }
  - { offsetInCU: 0x2FAC, offset: 0x38B28, size: 0x8, addend: 0x0, symName: __ZN4IGFX27BacklightRegistersAltFixCFL22probeInlinedInvocationERKNS_24BacklightRegistersAltFix18FunctionDescriptorERKNS1_12ProbeContextE, symObjAddr: 0xBE30, symBinAddr: 0xCD00, symSize: 0x160 }
  - { offsetInCU: 0x3169, offset: 0x38CE5, size: 0x8, addend: 0x0, symName: __ZN4IGFX17BacklightSmootherD1Ev, symObjAddr: 0xBF90, symBinAddr: 0xCE60, symSize: 0x10 }
  - { offsetInCU: 0x319E, offset: 0x38D1A, size: 0x8, addend: 0x0, symName: __ZN4IGFX17BacklightSmootherD0Ev, symObjAddr: 0xBFA0, symBinAddr: 0xCE70, symSize: 0x10 }
  - { offsetInCU: 0x31D5, offset: 0x38D51, size: 0x8, addend: 0x0, symName: __ZN4IGFX17BacklightSmoother4initEv, symObjAddr: 0xBFB0, symBinAddr: 0xCE80, symSize: 0x10 }
  - { offsetInCU: 0x31DD, offset: 0x38D59, size: 0x8, addend: 0x0, symName: __ZN4IGFX17BacklightSmoother6deinitEv, symObjAddr: 0xBFC0, symBinAddr: 0xCE90, symSize: 0x70 }
  - { offsetInCU: 0x3209, offset: 0x38D85, size: 0x8, addend: 0x0, symName: __ZN4IGFX17BacklightSmoother6deinitEv, symObjAddr: 0xBFC0, symBinAddr: 0xCE90, symSize: 0x70 }
  - { offsetInCU: 0x3211, offset: 0x38D8D, size: 0x8, addend: 0x0, symName: __ZN4IGFX17BacklightSmoother13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0xC030, symBinAddr: 0xCF00, symSize: 0x540 }
  - { offsetInCU: 0x3271, offset: 0x38DED, size: 0x8, addend: 0x0, symName: __ZN4IGFX17BacklightSmoother13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0xC030, symBinAddr: 0xCF00, symSize: 0x540 }
  - { offsetInCU: 0x36BC, offset: 0x39238, size: 0x8, addend: 0x0, symName: __ZN4IGFX17BacklightSmoother22processFramebufferKextER13KernelPatchermym, symObjAddr: 0xC570, symBinAddr: 0xD440, symSize: 0x100 }
  - { offsetInCU: 0x3857, offset: 0x393D3, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_kern_igfx_backlight.cpp, symObjAddr: 0xC670, symBinAddr: 0xD540, symSize: 0x40 }
  - { offsetInCU: 0x38E9, offset: 0x39465, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0xC6B0, symBinAddr: 0xD580, symSize: 0x20 }
  - { offsetInCU: 0x395B, offset: 0x394D7, size: 0x8, addend: 0x0, symName: __ZN4IGFX21BacklightRegistersFix28wrapKBLWriteRegisterPWMFreq1EPvjj, symObjAddr: 0xC6D0, symBinAddr: 0xD5A0, symSize: 0x140 }
  - { offsetInCU: 0x3B0D, offset: 0x39689, size: 0x8, addend: 0x0, symName: __ZN4IGFX17BacklightSmoother30smoothCFLWriteRegisterPWMDuty1EPvjj, symObjAddr: 0xC810, symBinAddr: 0xD6E0, symSize: 0x60 }
  - { offsetInCU: 0x3B15, offset: 0x39691, size: 0x8, addend: 0x0, symName: __ZN4IGFX21BacklightRegistersFix28wrapKBLWriteRegisterPWMCtrl1EPvjj, symObjAddr: 0xC870, symBinAddr: 0xD740, symSize: 0x90 }
  - { offsetInCU: 0x3B54, offset: 0x396D0, size: 0x8, addend: 0x0, symName: __ZN4IGFX21BacklightRegistersFix28wrapKBLWriteRegisterPWMCtrl1EPvjj, symObjAddr: 0xC870, symBinAddr: 0xD740, symSize: 0x90 }
  - { offsetInCU: 0x3C7E, offset: 0x397FA, size: 0x8, addend: 0x0, symName: __ZN4IGFX21BacklightRegistersFix28wrapCFLWriteRegisterPWMFreq1EPvjj, symObjAddr: 0xC900, symBinAddr: 0xD7D0, symSize: 0xB0 }
  - { offsetInCU: 0x3DD6, offset: 0x39952, size: 0x8, addend: 0x0, symName: __ZN4IGFX21BacklightRegistersFix28wrapCFLWriteRegisterPWMDuty1EPvjj, symObjAddr: 0xC9B0, symBinAddr: 0xD880, symSize: 0x1B0 }
  - { offsetInCU: 0x403C, offset: 0x39BB8, size: 0x8, addend: 0x0, symName: __ZN4IGFX17BacklightSmoother30smoothIVBWriteRegisterPWMCCTRLEPvjj, symObjAddr: 0xCB60, symBinAddr: 0xDA30, symSize: 0x60 }
  - { offsetInCU: 0x4044, offset: 0x39BC0, size: 0x8, addend: 0x0, symName: __ZN4IGFX17BacklightSmoother30smoothHSWWriteRegisterPWMFreq1EPvjj, symObjAddr: 0xCBC0, symBinAddr: 0xDA90, symSize: 0x60 }
  - { offsetInCU: 0x409B, offset: 0x39C17, size: 0x8, addend: 0x0, symName: __ZN4IGFX17BacklightSmoother30smoothHSWWriteRegisterPWMFreq1EPvjj, symObjAddr: 0xCBC0, symBinAddr: 0xDA90, symSize: 0x60 }
  - { offsetInCU: 0x40A3, offset: 0x39C1F, size: 0x8, addend: 0x0, symName: __ZN4IGFX15RPSControlPatchD1Ev, symObjAddr: 0xCC20, symBinAddr: 0xDAF0, symSize: 0x10 }
  - { offsetInCU: 0x35, offset: 0x39CD9, size: 0x8, addend: 0x0, symName: __ZL18GuCFirmwareSKLBlob, symObjAddr: 0x276F0, symBinAddr: 0x26C80, symSize: 0x0 }
  - { offsetInCU: 0x73, offset: 0x39D17, size: 0x8, addend: 0x0, symName: __ZL18GuCFirmwareKBLBlob, symObjAddr: 0x4B730, symBinAddr: 0x4ACC0, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x3A3B9, size: 0x8, addend: 0x0, symName: __ZN4IGFX15RPSControlPatchD1Ev, symObjAddr: 0xCC20, symBinAddr: 0xDAF0, symSize: 0x10 }
  - { offsetInCU: 0x153, offset: 0x3A4E5, size: 0x8, addend: 0x0, symName: __ZN4IGFX15RPSControlPatchD1Ev, symObjAddr: 0xCC20, symBinAddr: 0xDAF0, symSize: 0x10 }
  - { offsetInCU: 0x188, offset: 0x3A51A, size: 0x8, addend: 0x0, symName: __ZN4IGFX15RPSControlPatchD0Ev, symObjAddr: 0xCC30, symBinAddr: 0xDB00, symSize: 0x10 }
  - { offsetInCU: 0x1BF, offset: 0x3A551, size: 0x8, addend: 0x0, symName: __ZN4IGFX15RPSControlPatch4initEv, symObjAddr: 0xCC40, symBinAddr: 0xDB10, symSize: 0x10 }
  - { offsetInCU: 0x1F2, offset: 0x3A584, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule6deinitEv.452, symObjAddr: 0xCC50, symBinAddr: 0xDB20, symSize: 0x10 }
  - { offsetInCU: 0x2E2, offset: 0x3A674, size: 0x8, addend: 0x0, symName: __ZN4IGFX15RPSControlPatch13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0xCC60, symBinAddr: 0xDB30, symSize: 0xD0 }
  - { offsetInCU: 0x4FA, offset: 0x3A88C, size: 0x8, addend: 0x0, symName: __ZN4IGFX15RPSControlPatch22processFramebufferKextER13KernelPatchermym, symObjAddr: 0xCD30, symBinAddr: 0xDC00, symSize: 0x80 }
  - { offsetInCU: 0x8A9, offset: 0x3AC3B, size: 0x8, addend: 0x0, symName: __ZN4IGFX15RPSControlPatch19processGraphicsKextER13KernelPatchermym, symObjAddr: 0xCDB0, symBinAddr: 0xDC80, symSize: 0x250 }
  - { offsetInCU: 0xAE3, offset: 0x3AE75, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule26disableDependentSubmodulesEv.453, symObjAddr: 0xD000, symBinAddr: 0xDED0, symSize: 0x10 }
  - { offsetInCU: 0xB52, offset: 0x3AEE4, size: 0x8, addend: 0x0, symName: __ZN4IGFX15RPSControlPatch19wrapPmNotifyWrapperEjjPyPj, symObjAddr: 0xD010, symBinAddr: 0xDEE0, symSize: 0x60 }
  - { offsetInCU: 0xC0C, offset: 0x3AF9E, size: 0x8, addend: 0x0, symName: __ZN4IGFX19ForceWakeWorkaroundD1Ev, symObjAddr: 0xD070, symBinAddr: 0xDF40, symSize: 0x10 }
  - { offsetInCU: 0xC41, offset: 0x3AFD3, size: 0x8, addend: 0x0, symName: __ZN4IGFX19ForceWakeWorkaroundD0Ev, symObjAddr: 0xD080, symBinAddr: 0xDF50, symSize: 0x10 }
  - { offsetInCU: 0xC78, offset: 0x3B00A, size: 0x8, addend: 0x0, symName: __ZN4IGFX19ForceWakeWorkaround4initEv, symObjAddr: 0xD090, symBinAddr: 0xDF60, symSize: 0x10 }
  - { offsetInCU: 0xCAC, offset: 0x3B03E, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0xD0A0, symBinAddr: 0xDF70, symSize: 0x10 }
  - { offsetInCU: 0xCFD, offset: 0x3B08F, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule22processFramebufferKextER13KernelPatchermym.470, symObjAddr: 0xD0B0, symBinAddr: 0xDF80, symSize: 0x10 }
  - { offsetInCU: 0xE12, offset: 0x3B1A4, size: 0x8, addend: 0x0, symName: __ZN4IGFX19ForceWakeWorkaround19processGraphicsKextER13KernelPatchermym, symObjAddr: 0xD0C0, symBinAddr: 0xDF90, symSize: 0x80 }
  - { offsetInCU: 0x1008, offset: 0x3B39A, size: 0x8, addend: 0x0, symName: __ZN4IGFX19ForceWakeWorkaround9forceWakeEPvhjj, symObjAddr: 0xD140, symBinAddr: 0xE010, symSize: 0x410 }
  - { offsetInCU: 0x1267, offset: 0x3B5F9, size: 0x8, addend: 0x0, symName: __ZN4IGFX19ForceWakeWorkaround24forceWakeWaitAckFallbackEjjj, symObjAddr: 0xD550, symBinAddr: 0xE420, symSize: 0x190 }
  - { offsetInCU: 0x27, offset: 0x3B8E7, size: 0x8, addend: 0x0, symName: __ZN3WEG24processGraphicsPolicyStrEPKc, symObjAddr: 0xD6E0, symBinAddr: 0xE5B0, symSize: 0xB0 }
  - { offsetInCU: 0x43, offset: 0x3B903, size: 0x8, addend: 0x0, symName: __ZN3WEG18appleBacklightDataE, symObjAddr: 0x1D210, symBinAddr: 0x74660, symSize: 0x0 }
  - { offsetInCU: 0x6858, offset: 0x42118, size: 0x8, addend: 0x0, symName: __ZN3WEG11callbackWEGE, symObjAddr: 0x150600, symBinAddr: 0x75870, symSize: 0x0 }
  - { offsetInCU: 0x687A, offset: 0x4213A, size: 0x8, addend: 0x0, symName: __ZL14kextIOGraphics, symObjAddr: 0x1D360, symBinAddr: 0x747B0, symSize: 0x0 }
  - { offsetInCU: 0x6898, offset: 0x42158, size: 0x8, addend: 0x0, symName: __ZL14pathIOGraphics, symObjAddr: 0x1D400, symBinAddr: 0x74850, symSize: 0x0 }
  - { offsetInCU: 0x68B6, offset: 0x42176, size: 0x8, addend: 0x0, symName: __ZL13kextAGDPolicy, symObjAddr: 0x1D388, symBinAddr: 0x747D8, symSize: 0x0 }
  - { offsetInCU: 0x68D4, offset: 0x42194, size: 0x8, addend: 0x0, symName: __ZL13pathAGDPolicy, symObjAddr: 0x1D408, symBinAddr: 0x74858, symSize: 0x0 }
  - { offsetInCU: 0x68F2, offset: 0x421B2, size: 0x8, addend: 0x0, symName: __ZL13kextBacklight, symObjAddr: 0x1D3B0, symBinAddr: 0x74800, symSize: 0x0 }
  - { offsetInCU: 0x6910, offset: 0x421D0, size: 0x8, addend: 0x0, symName: __ZL13pathBacklight, symObjAddr: 0x1D410, symBinAddr: 0x74860, symSize: 0x0 }
  - { offsetInCU: 0x692E, offset: 0x421EE, size: 0x8, addend: 0x0, symName: __ZL15kextMCCSControl, symObjAddr: 0x1D3D8, symBinAddr: 0x74828, symSize: 0x0 }
  - { offsetInCU: 0x694C, offset: 0x4220C, size: 0x8, addend: 0x0, symName: __ZL15pathMCCSControl, symObjAddr: 0x1D418, symBinAddr: 0x74868, symSize: 0x0 }
  - { offsetInCU: 0x6A6D, offset: 0x4232D, size: 0x8, addend: 0x0, symName: __ZN3WEG24processGraphicsPolicyStrEPKc, symObjAddr: 0xD6E0, symBinAddr: 0xE5B0, symSize: 0xB0 }
  - { offsetInCU: 0x6BE5, offset: 0x424A5, size: 0x8, addend: 0x0, symName: '__ZZN3WEG4initEvEN3$_08__invokeEPvR13KernelPatcher', symObjAddr: 0xD790, symBinAddr: 0xE660, symSize: 0x10 }
  - { offsetInCU: 0x72AA, offset: 0x42B6A, size: 0x8, addend: 0x0, symName: '__ZZN3WEG4initEvEN3$_18__invokeEPvR13KernelPatchermym', symObjAddr: 0xD7A0, symBinAddr: 0xE670, symSize: 0x2CB0 }
  - { offsetInCU: 0x945C, offset: 0x44D1C, size: 0x8, addend: 0x0, symName: __ZN3WEG19wrapFramebufferInitEP13IOFramebuffer, symObjAddr: 0x10450, symBinAddr: 0x11320, symSize: 0x280 }
  - { offsetInCU: 0x95A3, offset: 0x44E63, size: 0x8, addend: 0x0, symName: __ZN3WEG22wrapFunctionReturnZeroEv, symObjAddr: 0x106D0, symBinAddr: 0x115A0, symSize: 0x10 }
  - { offsetInCU: 0x95C2, offset: 0x44E82, size: 0x8, addend: 0x0, symName: __ZN3WEG24wrapApplePanelSetDisplayEP9IOServiceP9IODisplay, symObjAddr: 0x106E0, symBinAddr: 0x115B0, symSize: 0x690 }
  - { offsetInCU: 0x95CA, offset: 0x44E8A, size: 0x8, addend: 0x0, symName: __ZN3WEG23wrapGraphicsPolicyStartEP9IOServiceS1_, symObjAddr: 0x10D70, symBinAddr: 0x11C40, symSize: 0x130 }
  - { offsetInCU: 0x9628, offset: 0x44EE8, size: 0x8, addend: 0x0, symName: __ZN3WEG23wrapGraphicsPolicyStartEP9IOServiceS1_, symObjAddr: 0x10D70, symBinAddr: 0x11C40, symSize: 0x130 }
  - { offsetInCU: 0x9726, offset: 0x44FE6, size: 0x8, addend: 0x0, symName: __ZN3WEG23wrapGraphicsPolicyStartEP9IOServiceS1_, symObjAddr: 0x10D70, symBinAddr: 0x11C40, symSize: 0x130 }
  - { offsetInCU: 0x980F, offset: 0x450CF, size: 0x8, addend: 0x0, symName: __ZN3WEG13processKernelER13KernelPatcher, symObjAddr: 0x10EA0, symBinAddr: 0x11D70, symSize: 0x4E10 }
  - { offsetInCU: 0xCD06, offset: 0x485C6, size: 0x8, addend: 0x0, symName: __ZN3WEG24processBuiltinPropertiesEP15IORegistryEntryP10DeviceInfo, symObjAddr: 0x15CB0, symBinAddr: 0x16B80, symSize: 0x410 }
  - { offsetInCU: 0xCFEA, offset: 0x488AA, size: 0x8, addend: 0x0, symName: __ZN3WEG25processExternalPropertiesEP15IORegistryEntryP10DeviceInfoj, symObjAddr: 0x160C0, symBinAddr: 0x16F90, symSize: 0x540 }
  - { offsetInCU: 0xD455, offset: 0x48D15, size: 0x8, addend: 0x0, symName: __ZN3WEG33processManagementEnginePropertiesEP15IORegistryEntry, symObjAddr: 0x16600, symBinAddr: 0x174D0, symSize: 0x180 }
  - { offsetInCU: 0xD5B9, offset: 0x48E79, size: 0x8, addend: 0x0, symName: __ZN3WEG16wrapConfigRead16EP15IORegistryEntryjh, symObjAddr: 0x16780, symBinAddr: 0x17650, symSize: 0x110 }
  - { offsetInCU: 0xD6DE, offset: 0x48F9E, size: 0x8, addend: 0x0, symName: __ZN3WEG16wrapConfigRead32EP15IORegistryEntryjh, symObjAddr: 0x16890, symBinAddr: 0x17760, symSize: 0x120 }
  - { offsetInCU: 0xD81B, offset: 0x490DB, size: 0x8, addend: 0x0, symName: __ZN3WEG16getVideoArgumentEP10DeviceInfoPKcPvi, symObjAddr: 0x169B0, symBinAddr: 0x17880, symSize: 0x170 }
  - { offsetInCU: 0x27, offset: 0x49344, size: 0x8, addend: 0x0, symName: __ZN3WEG14getRadeonModelEtttt, symObjAddr: 0x16B20, symBinAddr: 0x179F0, symSize: 0x1080 }
  - { offsetInCU: 0x46, offset: 0x49363, size: 0x8, addend: 0x0, symName: __ZL8devIntel, symObjAddr: 0x6FE90, symBinAddr: 0x6F4D0, symSize: 0x0 }
  - { offsetInCU: 0xC0, offset: 0x493DD, size: 0x8, addend: 0x0, symName: __ZL7devices, symObjAddr: 0x70470, symBinAddr: 0x6FAB0, symSize: 0x0 }
  - { offsetInCU: 0x1D2, offset: 0x494EF, size: 0x8, addend: 0x0, symName: __ZL7dev6640, symObjAddr: 0x70C70, symBinAddr: 0x702B0, symSize: 0x0 }
  - { offsetInCU: 0x204, offset: 0x49521, size: 0x8, addend: 0x0, symName: __ZL7dev6641, symObjAddr: 0x70CB0, symBinAddr: 0x702F0, symSize: 0x0 }
  - { offsetInCU: 0x236, offset: 0x49553, size: 0x8, addend: 0x0, symName: __ZL7dev6646, symObjAddr: 0x70CC0, symBinAddr: 0x70300, symSize: 0x0 }
  - { offsetInCU: 0x268, offset: 0x49585, size: 0x8, addend: 0x0, symName: __ZL7dev6647, symObjAddr: 0x70CE0, symBinAddr: 0x70320, symSize: 0x0 }
  - { offsetInCU: 0x286, offset: 0x495A3, size: 0x8, addend: 0x0, symName: __ZL7dev665c, symObjAddr: 0x70D00, symBinAddr: 0x70340, symSize: 0x0 }
  - { offsetInCU: 0x2B8, offset: 0x495D5, size: 0x8, addend: 0x0, symName: __ZL7dev665d, symObjAddr: 0x70D80, symBinAddr: 0x703C0, symSize: 0x0 }
  - { offsetInCU: 0x2D6, offset: 0x495F3, size: 0x8, addend: 0x0, symName: __ZL7dev66af, symObjAddr: 0x70D90, symBinAddr: 0x703D0, symSize: 0x0 }
  - { offsetInCU: 0x2F4, offset: 0x49611, size: 0x8, addend: 0x0, symName: __ZL7dev6704, symObjAddr: 0x70DA0, symBinAddr: 0x703E0, symSize: 0x0 }
  - { offsetInCU: 0x312, offset: 0x4962F, size: 0x8, addend: 0x0, symName: __ZL7dev6718, symObjAddr: 0x70DB0, symBinAddr: 0x703F0, symSize: 0x0 }
  - { offsetInCU: 0x330, offset: 0x4964D, size: 0x8, addend: 0x0, symName: __ZL7dev6719, symObjAddr: 0x70DC0, symBinAddr: 0x70400, symSize: 0x0 }
  - { offsetInCU: 0x34E, offset: 0x4966B, size: 0x8, addend: 0x0, symName: __ZL7dev6720, symObjAddr: 0x70DD0, symBinAddr: 0x70410, symSize: 0x0 }
  - { offsetInCU: 0x380, offset: 0x4969D, size: 0x8, addend: 0x0, symName: __ZL7dev6722, symObjAddr: 0x70E40, symBinAddr: 0x70480, symSize: 0x0 }
  - { offsetInCU: 0x39E, offset: 0x496BB, size: 0x8, addend: 0x0, symName: __ZL7dev6738, symObjAddr: 0x70E50, symBinAddr: 0x70490, symSize: 0x0 }
  - { offsetInCU: 0x3BC, offset: 0x496D9, size: 0x8, addend: 0x0, symName: __ZL7dev6739, symObjAddr: 0x70E60, symBinAddr: 0x704A0, symSize: 0x0 }
  - { offsetInCU: 0x3DA, offset: 0x496F7, size: 0x8, addend: 0x0, symName: __ZL7dev6740, symObjAddr: 0x70E70, symBinAddr: 0x704B0, symSize: 0x0 }
  - { offsetInCU: 0x40C, offset: 0x49729, size: 0x8, addend: 0x0, symName: __ZL7dev6741, symObjAddr: 0x70FA0, symBinAddr: 0x705E0, symSize: 0x0 }
  - { offsetInCU: 0x43E, offset: 0x4975B, size: 0x8, addend: 0x0, symName: __ZL7dev6745, symObjAddr: 0x71170, symBinAddr: 0x707B0, symSize: 0x0 }
  - { offsetInCU: 0x45C, offset: 0x49779, size: 0x8, addend: 0x0, symName: __ZL7dev6750, symObjAddr: 0x71180, symBinAddr: 0x707C0, symSize: 0x0 }
  - { offsetInCU: 0x47A, offset: 0x49797, size: 0x8, addend: 0x0, symName: __ZL7dev6758, symObjAddr: 0x711C0, symBinAddr: 0x70800, symSize: 0x0 }
  - { offsetInCU: 0x4AC, offset: 0x497C9, size: 0x8, addend: 0x0, symName: __ZL7dev6759, symObjAddr: 0x71280, symBinAddr: 0x708C0, symSize: 0x0 }
  - { offsetInCU: 0x4CA, offset: 0x497E7, size: 0x8, addend: 0x0, symName: __ZL7dev6760, symObjAddr: 0x71300, symBinAddr: 0x70940, symSize: 0x0 }
  - { offsetInCU: 0x4FD, offset: 0x4981A, size: 0x8, addend: 0x0, symName: __ZL7dev6761, symObjAddr: 0x71810, symBinAddr: 0x70E50, symSize: 0x0 }
  - { offsetInCU: 0x51C, offset: 0x49839, size: 0x8, addend: 0x0, symName: __ZL7dev6768, symObjAddr: 0x71820, symBinAddr: 0x70E60, symSize: 0x0 }
  - { offsetInCU: 0x53B, offset: 0x49858, size: 0x8, addend: 0x0, symName: __ZL7dev6770, symObjAddr: 0x71830, symBinAddr: 0x70E70, symSize: 0x0 }
  - { offsetInCU: 0x56E, offset: 0x4988B, size: 0x8, addend: 0x0, symName: __ZL7dev6779, symObjAddr: 0x71860, symBinAddr: 0x70EA0, symSize: 0x0 }
  - { offsetInCU: 0x5A1, offset: 0x498BE, size: 0x8, addend: 0x0, symName: __ZL7dev6780, symObjAddr: 0x71940, symBinAddr: 0x70F80, symSize: 0x0 }
  - { offsetInCU: 0x5C0, offset: 0x498DD, size: 0x8, addend: 0x0, symName: __ZL7dev6790, symObjAddr: 0x71950, symBinAddr: 0x70F90, symSize: 0x0 }
  - { offsetInCU: 0x5DF, offset: 0x498FC, size: 0x8, addend: 0x0, symName: __ZL7dev6798, symObjAddr: 0x71960, symBinAddr: 0x70FA0, symSize: 0x0 }
  - { offsetInCU: 0x5FE, offset: 0x4991B, size: 0x8, addend: 0x0, symName: __ZL7dev679a, symObjAddr: 0x71A20, symBinAddr: 0x71060, symSize: 0x0 }
  - { offsetInCU: 0x61D, offset: 0x4993A, size: 0x8, addend: 0x0, symName: __ZL7dev679e, symObjAddr: 0x71A90, symBinAddr: 0x710D0, symSize: 0x0 }
  - { offsetInCU: 0x63C, offset: 0x49959, size: 0x8, addend: 0x0, symName: __ZL7dev67b0, symObjAddr: 0x71AA0, symBinAddr: 0x710E0, symSize: 0x0 }
  - { offsetInCU: 0x66F, offset: 0x4998C, size: 0x8, addend: 0x0, symName: __ZL7dev67c0, symObjAddr: 0x71BB0, symBinAddr: 0x711F0, symSize: 0x0 }
  - { offsetInCU: 0x68E, offset: 0x499AB, size: 0x8, addend: 0x0, symName: __ZL7dev67c4, symObjAddr: 0x71BD0, symBinAddr: 0x71210, symSize: 0x0 }
  - { offsetInCU: 0x6AD, offset: 0x499CA, size: 0x8, addend: 0x0, symName: __ZL7dev67c7, symObjAddr: 0x71BE0, symBinAddr: 0x71220, symSize: 0x0 }
  - { offsetInCU: 0x6CC, offset: 0x499E9, size: 0x8, addend: 0x0, symName: __ZL7dev67df, symObjAddr: 0x71BF0, symBinAddr: 0x71230, symSize: 0x0 }
  - { offsetInCU: 0x6EB, offset: 0x49A08, size: 0x8, addend: 0x0, symName: __ZL7dev67e0, symObjAddr: 0x71D20, symBinAddr: 0x71360, symSize: 0x0 }
  - { offsetInCU: 0x70A, offset: 0x49A27, size: 0x8, addend: 0x0, symName: __ZL7dev67e3, symObjAddr: 0x71D30, symBinAddr: 0x71370, symSize: 0x0 }
  - { offsetInCU: 0x729, offset: 0x49A46, size: 0x8, addend: 0x0, symName: __ZL7dev67ef, symObjAddr: 0x71D40, symBinAddr: 0x71380, symSize: 0x0 }
  - { offsetInCU: 0x75C, offset: 0x49A79, size: 0x8, addend: 0x0, symName: __ZL7dev67ff, symObjAddr: 0x71E30, symBinAddr: 0x71470, symSize: 0x0 }
  - { offsetInCU: 0x78F, offset: 0x49AAC, size: 0x8, addend: 0x0, symName: __ZL7dev6800, symObjAddr: 0x71E90, symBinAddr: 0x714D0, symSize: 0x0 }
  - { offsetInCU: 0x7AE, offset: 0x49ACB, size: 0x8, addend: 0x0, symName: __ZL7dev6801, symObjAddr: 0x71EA0, symBinAddr: 0x714E0, symSize: 0x0 }
  - { offsetInCU: 0x7CD, offset: 0x49AEA, size: 0x8, addend: 0x0, symName: __ZL7dev6806, symObjAddr: 0x71EB0, symBinAddr: 0x714F0, symSize: 0x0 }
  - { offsetInCU: 0x7EC, offset: 0x49B09, size: 0x8, addend: 0x0, symName: __ZL7dev6808, symObjAddr: 0x71EC0, symBinAddr: 0x71500, symSize: 0x0 }
  - { offsetInCU: 0x80B, offset: 0x49B28, size: 0x8, addend: 0x0, symName: __ZL7dev6810, symObjAddr: 0x71ED0, symBinAddr: 0x71510, symSize: 0x0 }
  - { offsetInCU: 0x82A, offset: 0x49B47, size: 0x8, addend: 0x0, symName: __ZL7dev6818, symObjAddr: 0x71F50, symBinAddr: 0x71590, symSize: 0x0 }
  - { offsetInCU: 0x849, offset: 0x49B66, size: 0x8, addend: 0x0, symName: __ZL7dev6819, symObjAddr: 0x71F60, symBinAddr: 0x715A0, symSize: 0x0 }
  - { offsetInCU: 0x868, offset: 0x49B85, size: 0x8, addend: 0x0, symName: __ZL7dev6820, symObjAddr: 0x71FD0, symBinAddr: 0x71610, symSize: 0x0 }
  - { offsetInCU: 0x887, offset: 0x49BA4, size: 0x8, addend: 0x0, symName: __ZL7dev6821, symObjAddr: 0x72040, symBinAddr: 0x71680, symSize: 0x0 }
  - { offsetInCU: 0x8BA, offset: 0x49BD7, size: 0x8, addend: 0x0, symName: __ZL7dev6823, symObjAddr: 0x72090, symBinAddr: 0x716D0, symSize: 0x0 }
  - { offsetInCU: 0x8D9, offset: 0x49BF6, size: 0x8, addend: 0x0, symName: __ZL7dev6825, symObjAddr: 0x720A0, symBinAddr: 0x716E0, symSize: 0x0 }
  - { offsetInCU: 0x8F8, offset: 0x49C15, size: 0x8, addend: 0x0, symName: __ZL7dev6827, symObjAddr: 0x72100, symBinAddr: 0x71740, symSize: 0x0 }
  - { offsetInCU: 0x917, offset: 0x49C34, size: 0x8, addend: 0x0, symName: __ZL7dev682b, symObjAddr: 0x72110, symBinAddr: 0x71750, symSize: 0x0 }
  - { offsetInCU: 0x936, offset: 0x49C53, size: 0x8, addend: 0x0, symName: __ZL7dev682d, symObjAddr: 0x72120, symBinAddr: 0x71760, symSize: 0x0 }
  - { offsetInCU: 0x955, offset: 0x49C72, size: 0x8, addend: 0x0, symName: __ZL7dev682f, symObjAddr: 0x72150, symBinAddr: 0x71790, symSize: 0x0 }
  - { offsetInCU: 0x974, offset: 0x49C91, size: 0x8, addend: 0x0, symName: __ZL7dev6835, symObjAddr: 0x72160, symBinAddr: 0x717A0, symSize: 0x0 }
  - { offsetInCU: 0x993, offset: 0x49CB0, size: 0x8, addend: 0x0, symName: __ZL7dev6839, symObjAddr: 0x72170, symBinAddr: 0x717B0, symSize: 0x0 }
  - { offsetInCU: 0x9B2, offset: 0x49CCF, size: 0x8, addend: 0x0, symName: __ZL7dev683b, symObjAddr: 0x72180, symBinAddr: 0x717C0, symSize: 0x0 }
  - { offsetInCU: 0x9D1, offset: 0x49CEE, size: 0x8, addend: 0x0, symName: __ZL7dev683d, symObjAddr: 0x72190, symBinAddr: 0x717D0, symSize: 0x0 }
  - { offsetInCU: 0x9F0, offset: 0x49D0D, size: 0x8, addend: 0x0, symName: __ZL7dev683f, symObjAddr: 0x72200, symBinAddr: 0x71840, symSize: 0x0 }
  - { offsetInCU: 0xA0F, offset: 0x49D2C, size: 0x8, addend: 0x0, symName: __ZL7dev6840, symObjAddr: 0x72240, symBinAddr: 0x71880, symSize: 0x0 }
  - { offsetInCU: 0xA42, offset: 0x49D5F, size: 0x8, addend: 0x0, symName: __ZL7dev6841, symObjAddr: 0x722E0, symBinAddr: 0x71920, symSize: 0x0 }
  - { offsetInCU: 0xA75, offset: 0x49D92, size: 0x8, addend: 0x0, symName: __ZL7dev6861, symObjAddr: 0x723B0, symBinAddr: 0x719F0, symSize: 0x0 }
  - { offsetInCU: 0xA94, offset: 0x49DB1, size: 0x8, addend: 0x0, symName: __ZL7dev6863, symObjAddr: 0x723C0, symBinAddr: 0x71A00, symSize: 0x0 }
  - { offsetInCU: 0xAB3, offset: 0x49DD0, size: 0x8, addend: 0x0, symName: __ZL7dev6868, symObjAddr: 0x723D0, symBinAddr: 0x71A10, symSize: 0x0 }
  - { offsetInCU: 0xAD2, offset: 0x49DEF, size: 0x8, addend: 0x0, symName: __ZL7dev687f, symObjAddr: 0x723F0, symBinAddr: 0x71A30, symSize: 0x0 }
  - { offsetInCU: 0xAF1, offset: 0x49E0E, size: 0x8, addend: 0x0, symName: __ZL7dev6898, symObjAddr: 0x72430, symBinAddr: 0x71A70, symSize: 0x0 }
  - { offsetInCU: 0xB10, offset: 0x49E2D, size: 0x8, addend: 0x0, symName: __ZL7dev6899, symObjAddr: 0x72450, symBinAddr: 0x71A90, symSize: 0x0 }
  - { offsetInCU: 0xB2F, offset: 0x49E4C, size: 0x8, addend: 0x0, symName: __ZL7dev68a0, symObjAddr: 0x72480, symBinAddr: 0x71AC0, symSize: 0x0 }
  - { offsetInCU: 0xB4E, offset: 0x49E6B, size: 0x8, addend: 0x0, symName: __ZL7dev68a1, symObjAddr: 0x724B0, symBinAddr: 0x71AF0, symSize: 0x0 }
  - { offsetInCU: 0xB6D, offset: 0x49E8A, size: 0x8, addend: 0x0, symName: __ZL7dev68b0, symObjAddr: 0x724D0, symBinAddr: 0x71B10, symSize: 0x0 }
  - { offsetInCU: 0xB8C, offset: 0x49EA9, size: 0x8, addend: 0x0, symName: __ZL7dev68b1, symObjAddr: 0x724E0, symBinAddr: 0x71B20, symSize: 0x0 }
  - { offsetInCU: 0xBAB, offset: 0x49EC8, size: 0x8, addend: 0x0, symName: __ZL7dev68b8, symObjAddr: 0x724F0, symBinAddr: 0x71B30, symSize: 0x0 }
  - { offsetInCU: 0xBCA, offset: 0x49EE7, size: 0x8, addend: 0x0, symName: __ZL7dev68c0, symObjAddr: 0x72500, symBinAddr: 0x71B40, symSize: 0x0 }
  - { offsetInCU: 0xBE9, offset: 0x49F06, size: 0x8, addend: 0x0, symName: __ZL7dev68c1, symObjAddr: 0x72540, symBinAddr: 0x71B80, symSize: 0x0 }
  - { offsetInCU: 0xC1C, offset: 0x49F39, size: 0x8, addend: 0x0, symName: __ZL7dev68d8, symObjAddr: 0x726A0, symBinAddr: 0x71CE0, symSize: 0x0 }
  - { offsetInCU: 0xC3B, offset: 0x49F58, size: 0x8, addend: 0x0, symName: __ZL7dev68d9, symObjAddr: 0x72720, symBinAddr: 0x71D60, symSize: 0x0 }
  - { offsetInCU: 0xC5A, offset: 0x49F77, size: 0x8, addend: 0x0, symName: __ZL7dev68e0, symObjAddr: 0x727C0, symBinAddr: 0x71E00, symSize: 0x0 }
  - { offsetInCU: 0xC79, offset: 0x49F96, size: 0x8, addend: 0x0, symName: __ZL7dev68e1, symObjAddr: 0x727F0, symBinAddr: 0x71E30, symSize: 0x0 }
  - { offsetInCU: 0xCAC, offset: 0x49FC9, size: 0x8, addend: 0x0, symName: __ZL7dev6920, symObjAddr: 0x728F0, symBinAddr: 0x71F30, symSize: 0x0 }
  - { offsetInCU: 0xCCB, offset: 0x49FE8, size: 0x8, addend: 0x0, symName: __ZL7dev6921, symObjAddr: 0x72910, symBinAddr: 0x71F50, symSize: 0x0 }
  - { offsetInCU: 0xCEA, offset: 0x4A007, size: 0x8, addend: 0x0, symName: __ZL7dev6938, symObjAddr: 0x72930, symBinAddr: 0x71F70, symSize: 0x0 }
  - { offsetInCU: 0xD09, offset: 0x4A026, size: 0x8, addend: 0x0, symName: __ZL7dev6939, symObjAddr: 0x72950, symBinAddr: 0x71F90, symSize: 0x0 }
  - { offsetInCU: 0xD28, offset: 0x4A045, size: 0x8, addend: 0x0, symName: __ZL7dev7300, symObjAddr: 0x729D0, symBinAddr: 0x72010, symSize: 0x0 }
  - { offsetInCU: 0xD47, offset: 0x4A064, size: 0x8, addend: 0x0, symName: __ZL7dev944a, symObjAddr: 0x72A10, symBinAddr: 0x72050, symSize: 0x0 }
  - { offsetInCU: 0xD66, offset: 0x4A083, size: 0x8, addend: 0x0, symName: __ZL7dev9488, symObjAddr: 0x72A30, symBinAddr: 0x72070, symSize: 0x0 }
  - { offsetInCU: 0xDCF, offset: 0x4A0EC, size: 0x8, addend: 0x0, symName: __ZN3WEG14getRadeonModelEtttt, symObjAddr: 0x16B20, symBinAddr: 0x179F0, symSize: 0x1080 }
  - { offsetInCU: 0x27, offset: 0x4A242, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnablerD1Ev, symObjAddr: 0x17BA0, symBinAddr: 0x18A70, symSize: 0x10 }
  - { offsetInCU: 0x3F, offset: 0x4A25A, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnabler10gMetaClassE, symObjAddr: 0x151500, symBinAddr: 0x757F8, symSize: 0x0 }
  - { offsetInCU: 0x28E, offset: 0x4A4A9, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnabler9metaClassE, symObjAddr: 0x72A50, symBinAddr: 0x72090, symSize: 0x0 }
  - { offsetInCU: 0x2A6, offset: 0x4A4C1, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnabler10superClassE, symObjAddr: 0x72A58, symBinAddr: 0x72098, symSize: 0x0 }
  - { offsetInCU: 0x2E5, offset: 0x4A500, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnablerD1Ev, symObjAddr: 0x17BA0, symBinAddr: 0x18A70, symSize: 0x10 }
  - { offsetInCU: 0x360, offset: 0x4A57B, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnablerD0Ev, symObjAddr: 0x17BB0, symBinAddr: 0x18A80, symSize: 0x30 }
  - { offsetInCU: 0x3EE, offset: 0x4A609, size: 0x8, addend: 0x0, symName: __ZNK12NVHDAEnabler12getMetaClassEv, symObjAddr: 0x17BE0, symBinAddr: 0x18AB0, symSize: 0x10 }
  - { offsetInCU: 0x48E, offset: 0x4A6A9, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnabler5probeEP9IOServicePi, symObjAddr: 0x17BF0, symBinAddr: 0x18AC0, symSize: 0xC0 }
  - { offsetInCU: 0x597, offset: 0x4A7B2, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnabler5startEP9IOService, symObjAddr: 0x17CB0, symBinAddr: 0x18B80, symSize: 0x30 }
  - { offsetInCU: 0x605, offset: 0x4A820, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnabler9MetaClassD1Ev, symObjAddr: 0x17CE0, symBinAddr: 0x18BB0, symSize: 0x10 }
  - { offsetInCU: 0x680, offset: 0x4A89B, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnabler9MetaClassD0Ev, symObjAddr: 0x17CF0, symBinAddr: 0x18BC0, symSize: 0x10 }
  - { offsetInCU: 0x753, offset: 0x4A96E, size: 0x8, addend: 0x0, symName: __ZNK12NVHDAEnabler9MetaClass5allocEv, symObjAddr: 0x17D00, symBinAddr: 0x18BD0, symSize: 0x40 }
  - { offsetInCU: 0x75B, offset: 0x4A976, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_kern_nvhda.cpp, symObjAddr: 0x17D40, symBinAddr: 0x18C10, symSize: 0x40 }
  - { offsetInCU: 0x829, offset: 0x4AA44, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_kern_nvhda.cpp, symObjAddr: 0x17D40, symBinAddr: 0x18C10, symSize: 0x40 }
  - { offsetInCU: 0x8B9, offset: 0x4AAD4, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a.745, symObjAddr: 0x17D80, symBinAddr: 0x18C50, symSize: 0x20 }
  - { offsetInCU: 0x92B, offset: 0x4AB46, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnabler9MetaClassC1Ev, symObjAddr: 0x17DA0, symBinAddr: 0x18C70, symSize: 0x40 }
  - { offsetInCU: 0x986, offset: 0x4ABA1, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnablerC2EPK11OSMetaClass, symObjAddr: 0x17DE0, symBinAddr: 0x18CB0, symSize: 0x20 }
  - { offsetInCU: 0x9F7, offset: 0x4AC12, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnablerC1EPK11OSMetaClass, symObjAddr: 0x17E00, symBinAddr: 0x18CD0, symSize: 0x20 }
  - { offsetInCU: 0xA7F, offset: 0x4AC9A, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnablerD2Ev, symObjAddr: 0x17E20, symBinAddr: 0x18CF0, symSize: 0x10 }
  - { offsetInCU: 0xAAC, offset: 0x4ACC7, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnabler9MetaClassC2Ev, symObjAddr: 0x17E30, symBinAddr: 0x18D00, symSize: 0x40 }
  - { offsetInCU: 0xADB, offset: 0x4ACF6, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnablerC1Ev, symObjAddr: 0x17E70, symBinAddr: 0x18D40, symSize: 0x30 }
  - { offsetInCU: 0xB36, offset: 0x4AD51, size: 0x8, addend: 0x0, symName: __ZN12NVHDAEnablerC2Ev, symObjAddr: 0x17EA0, symBinAddr: 0x18D70, symSize: 0x30 }
  - { offsetInCU: 0x27, offset: 0x4ADB8, size: 0x8, addend: 0x0, symName: __ZN6UNFAIR14csValidatePageEP5vnodeP8ipc_portyPKvPiS6_S6_, symObjAddr: 0x17ED0, symBinAddr: 0x18DA0, symSize: 0x1B0 }
  - { offsetInCU: 0x39, offset: 0x4ADCA, size: 0x8, addend: 0x0, symName: __ZN6UNFAIR14csValidatePageEP5vnodeP8ipc_portyPKvPiS6_S6_, symObjAddr: 0x17ED0, symBinAddr: 0x18DA0, symSize: 0x1B0 }
  - { offsetInCU: 0x63, offset: 0x4ADF4, size: 0x8, addend: 0x0, symName: __ZZN6UNFAIR14csValidatePageEP5vnodeP8ipc_portyPKvPiS6_S6_E4find, symObjAddr: 0x6F8E0, symBinAddr: 0x6EE50, symSize: 0x0 }
  - { offsetInCU: 0x7D, offset: 0x4AE0E, size: 0x8, addend: 0x0, symName: __ZZN6UNFAIR14csValidatePageEP5vnodeP8ipc_portyPKvPiS6_S6_E4find_0, symObjAddr: 0x6F900, symBinAddr: 0x6EE70, symSize: 0x0 }
  - { offsetInCU: 0x9B, offset: 0x4AE2C, size: 0x8, addend: 0x0, symName: __ZZN6UNFAIR14csValidatePageEP5vnodeP8ipc_portyPKvPiS6_S6_E4repl, symObjAddr: 0x6F912, symBinAddr: 0x6EE82, symSize: 0x0 }
  - { offsetInCU: 0xB5, offset: 0x4AE46, size: 0x8, addend: 0x0, symName: __ZZN6UNFAIR14csValidatePageEP5vnodeP8ipc_portyPKvPiS6_S6_E4find_1, symObjAddr: 0x6F917, symBinAddr: 0x6EE87, symSize: 0x0 }
  - { offsetInCU: 0xCF, offset: 0x4AE60, size: 0x8, addend: 0x0, symName: __ZZN6UNFAIR14csValidatePageEP5vnodeP8ipc_portyPKvPiS6_S6_E4repl_0, symObjAddr: 0x6F91F, symBinAddr: 0x6EE8F, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x4B2E8, size: 0x8, addend: 0x0, symName: __ZN4IGFX11DVMTCalcFixD1Ev, symObjAddr: 0x18080, symBinAddr: 0x18F50, symSize: 0x10 }
  - { offsetInCU: 0x101, offset: 0x4B3C2, size: 0x8, addend: 0x0, symName: __ZN4IGFX11DVMTCalcFixD1Ev, symObjAddr: 0x18080, symBinAddr: 0x18F50, symSize: 0x10 }
  - { offsetInCU: 0x136, offset: 0x4B3F7, size: 0x8, addend: 0x0, symName: __ZN4IGFX11DVMTCalcFixD0Ev, symObjAddr: 0x18090, symBinAddr: 0x18F60, symSize: 0x10 }
  - { offsetInCU: 0x16D, offset: 0x4B42E, size: 0x8, addend: 0x0, symName: __ZN4IGFX11DVMTCalcFix4initEv, symObjAddr: 0x180A0, symBinAddr: 0x18F70, symSize: 0x10 }
  - { offsetInCU: 0x1A0, offset: 0x4B461, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule6deinitEv.765, symObjAddr: 0x180B0, symBinAddr: 0x18F80, symSize: 0x10 }
  - { offsetInCU: 0x209, offset: 0x4B4CA, size: 0x8, addend: 0x0, symName: __ZN4IGFX11DVMTCalcFix13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x180C0, symBinAddr: 0x18F90, symSize: 0x180 }
  - { offsetInCU: 0x33C, offset: 0x4B5FD, size: 0x8, addend: 0x0, symName: __ZN4IGFX11DVMTCalcFix22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x18240, symBinAddr: 0x19110, symSize: 0x300 }
  - { offsetInCU: 0x541, offset: 0x4B802, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule19processGraphicsKextER13KernelPatchermym.766, symObjAddr: 0x18540, symBinAddr: 0x19410, symSize: 0x10 }
  - { offsetInCU: 0x5B4, offset: 0x4B875, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule26disableDependentSubmodulesEv.767, symObjAddr: 0x18550, symBinAddr: 0x19420, symSize: 0x10 }
  - { offsetInCU: 0x5E3, offset: 0x4B8A4, size: 0x8, addend: 0x0, symName: __ZN4IGFX31DisplayDataBufferEarlyOptimizerD1Ev, symObjAddr: 0x18560, symBinAddr: 0x19430, symSize: 0x10 }
  - { offsetInCU: 0x618, offset: 0x4B8D9, size: 0x8, addend: 0x0, symName: __ZN4IGFX31DisplayDataBufferEarlyOptimizerD0Ev, symObjAddr: 0x18570, symBinAddr: 0x19440, symSize: 0x10 }
  - { offsetInCU: 0x64F, offset: 0x4B910, size: 0x8, addend: 0x0, symName: __ZN4IGFX31DisplayDataBufferEarlyOptimizer4initEv, symObjAddr: 0x18580, symBinAddr: 0x19450, symSize: 0x10 }
  - { offsetInCU: 0x683, offset: 0x4B944, size: 0x8, addend: 0x0, symName: __ZN4IGFX31DisplayDataBufferEarlyOptimizer13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x18590, symBinAddr: 0x19460, symSize: 0x140 }
  - { offsetInCU: 0x8EA, offset: 0x4BBAB, size: 0x8, addend: 0x0, symName: __ZN4IGFX31DisplayDataBufferEarlyOptimizer22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x186D0, symBinAddr: 0x195A0, symSize: 0x80 }
  - { offsetInCU: 0xA7C, offset: 0x4BD3D, size: 0x8, addend: 0x0, symName: __ZN4IGFX31DisplayDataBufferEarlyOptimizer21wrapGetFeatureControlEP9IOService, symObjAddr: 0x18750, symBinAddr: 0x19620, symSize: 0x130 }
  - { offsetInCU: 0x3A, offset: 0x4BE1C, size: 0x8, addend: 0x0, symName: _kextIntelHD, symObjAddr: 0x1D428, symBinAddr: 0x74878, symSize: 0x0 }
  - { offsetInCU: 0x55, offset: 0x4BE37, size: 0x8, addend: 0x0, symName: _kextIntelHDFb, symObjAddr: 0x1D458, symBinAddr: 0x748A8, symSize: 0x0 }
  - { offsetInCU: 0x70, offset: 0x4BE52, size: 0x8, addend: 0x0, symName: _kextIntelHD3000, symObjAddr: 0x1D488, symBinAddr: 0x748D8, symSize: 0x0 }
  - { offsetInCU: 0x8B, offset: 0x4BE6D, size: 0x8, addend: 0x0, symName: _kextIntelSNBFb, symObjAddr: 0x1D4B8, symBinAddr: 0x74908, symSize: 0x0 }
  - { offsetInCU: 0xA6, offset: 0x4BE88, size: 0x8, addend: 0x0, symName: _kextIntelHD4000, symObjAddr: 0x1D4E8, symBinAddr: 0x74938, symSize: 0x0 }
  - { offsetInCU: 0xC1, offset: 0x4BEA3, size: 0x8, addend: 0x0, symName: _kextIntelCapriFb, symObjAddr: 0x1D518, symBinAddr: 0x74968, symSize: 0x0 }
  - { offsetInCU: 0xDC, offset: 0x4BEBE, size: 0x8, addend: 0x0, symName: _kextIntelHD5000, symObjAddr: 0x1D548, symBinAddr: 0x74998, symSize: 0x0 }
  - { offsetInCU: 0xF7, offset: 0x4BED9, size: 0x8, addend: 0x0, symName: _kextIntelAzulFb, symObjAddr: 0x1D578, symBinAddr: 0x749C8, symSize: 0x0 }
  - { offsetInCU: 0x112, offset: 0x4BEF4, size: 0x8, addend: 0x0, symName: _kextIntelBDW, symObjAddr: 0x1D5A8, symBinAddr: 0x749F8, symSize: 0x0 }
  - { offsetInCU: 0x12D, offset: 0x4BF0F, size: 0x8, addend: 0x0, symName: _kextIntelBDWFb, symObjAddr: 0x1D5D8, symBinAddr: 0x74A28, symSize: 0x0 }
  - { offsetInCU: 0x148, offset: 0x4BF2A, size: 0x8, addend: 0x0, symName: _kextIntelSKL, symObjAddr: 0x1D608, symBinAddr: 0x74A58, symSize: 0x0 }
  - { offsetInCU: 0x163, offset: 0x4BF45, size: 0x8, addend: 0x0, symName: _kextIntelSKLFb, symObjAddr: 0x1D638, symBinAddr: 0x74A88, symSize: 0x0 }
  - { offsetInCU: 0x17E, offset: 0x4BF60, size: 0x8, addend: 0x0, symName: _kextIntelKBL, symObjAddr: 0x1D668, symBinAddr: 0x74AB8, symSize: 0x0 }
  - { offsetInCU: 0x199, offset: 0x4BF7B, size: 0x8, addend: 0x0, symName: _kextIntelKBLFb, symObjAddr: 0x1D698, symBinAddr: 0x74AE8, symSize: 0x0 }
  - { offsetInCU: 0x1B4, offset: 0x4BF96, size: 0x8, addend: 0x0, symName: _kextIntelCFLFb, symObjAddr: 0x1D6C8, symBinAddr: 0x74B18, symSize: 0x0 }
  - { offsetInCU: 0x1CF, offset: 0x4BFB1, size: 0x8, addend: 0x0, symName: _kextIntelCNL, symObjAddr: 0x1D6F8, symBinAddr: 0x74B48, symSize: 0x0 }
  - { offsetInCU: 0x1EA, offset: 0x4BFCC, size: 0x8, addend: 0x0, symName: _kextIntelCNLFb, symObjAddr: 0x1D728, symBinAddr: 0x74B78, symSize: 0x0 }
  - { offsetInCU: 0x205, offset: 0x4BFE7, size: 0x8, addend: 0x0, symName: _kextIntelICL, symObjAddr: 0x1D758, symBinAddr: 0x74BA8, symSize: 0x0 }
  - { offsetInCU: 0x220, offset: 0x4C002, size: 0x8, addend: 0x0, symName: _kextIntelICLLPFb, symObjAddr: 0x1D788, symBinAddr: 0x74BD8, symSize: 0x0 }
  - { offsetInCU: 0x23B, offset: 0x4C01D, size: 0x8, addend: 0x0, symName: _kextIntelICLHPFb, symObjAddr: 0x1D7B8, symBinAddr: 0x74C08, symSize: 0x0 }
  - { offsetInCU: 0x255, offset: 0x4C037, size: 0x8, addend: 0x0, symName: __ZL11pathIntelHD, symObjAddr: 0x1D420, symBinAddr: 0x74870, symSize: 0x0 }
  - { offsetInCU: 0x273, offset: 0x4C055, size: 0x8, addend: 0x0, symName: __ZL13pathIntelHDFb, symObjAddr: 0x1D450, symBinAddr: 0x748A0, symSize: 0x0 }
  - { offsetInCU: 0x291, offset: 0x4C073, size: 0x8, addend: 0x0, symName: __ZL15pathIntelHD3000, symObjAddr: 0x1D480, symBinAddr: 0x748D0, symSize: 0x0 }
  - { offsetInCU: 0x2AF, offset: 0x4C091, size: 0x8, addend: 0x0, symName: __ZL14pathIntelSNBFb, symObjAddr: 0x1D4B0, symBinAddr: 0x74900, symSize: 0x0 }
  - { offsetInCU: 0x2CD, offset: 0x4C0AF, size: 0x8, addend: 0x0, symName: __ZL15pathIntelHD4000, symObjAddr: 0x1D4E0, symBinAddr: 0x74930, symSize: 0x0 }
  - { offsetInCU: 0x2F2, offset: 0x4C0D4, size: 0x8, addend: 0x0, symName: __ZL16pathIntelCapriFb, symObjAddr: 0x1D510, symBinAddr: 0x74960, symSize: 0x0 }
  - { offsetInCU: 0x310, offset: 0x4C0F2, size: 0x8, addend: 0x0, symName: __ZL15pathIntelHD5000, symObjAddr: 0x1D540, symBinAddr: 0x74990, symSize: 0x0 }
  - { offsetInCU: 0x32E, offset: 0x4C110, size: 0x8, addend: 0x0, symName: __ZL15pathIntelAzulFb, symObjAddr: 0x1D570, symBinAddr: 0x749C0, symSize: 0x0 }
  - { offsetInCU: 0x34C, offset: 0x4C12E, size: 0x8, addend: 0x0, symName: __ZL12pathIntelBDW, symObjAddr: 0x1D5A0, symBinAddr: 0x749F0, symSize: 0x0 }
  - { offsetInCU: 0x36A, offset: 0x4C14C, size: 0x8, addend: 0x0, symName: __ZL14pathIntelBDWFb, symObjAddr: 0x1D5D0, symBinAddr: 0x74A20, symSize: 0x0 }
  - { offsetInCU: 0x388, offset: 0x4C16A, size: 0x8, addend: 0x0, symName: __ZL12pathIntelSKL, symObjAddr: 0x1D600, symBinAddr: 0x74A50, symSize: 0x0 }
  - { offsetInCU: 0x3A6, offset: 0x4C188, size: 0x8, addend: 0x0, symName: __ZL14pathIntelSKLFb, symObjAddr: 0x1D630, symBinAddr: 0x74A80, symSize: 0x0 }
  - { offsetInCU: 0x3C4, offset: 0x4C1A6, size: 0x8, addend: 0x0, symName: __ZL12pathIntelKBL, symObjAddr: 0x1D660, symBinAddr: 0x74AB0, symSize: 0x0 }
  - { offsetInCU: 0x3E2, offset: 0x4C1C4, size: 0x8, addend: 0x0, symName: __ZL14pathIntelKBLFb, symObjAddr: 0x1D690, symBinAddr: 0x74AE0, symSize: 0x0 }
  - { offsetInCU: 0x400, offset: 0x4C1E2, size: 0x8, addend: 0x0, symName: __ZL14pathIntelCFLFb, symObjAddr: 0x1D6C0, symBinAddr: 0x74B10, symSize: 0x0 }
  - { offsetInCU: 0x41E, offset: 0x4C200, size: 0x8, addend: 0x0, symName: __ZL12pathIntelCNL, symObjAddr: 0x1D6F0, symBinAddr: 0x74B40, symSize: 0x0 }
  - { offsetInCU: 0x43C, offset: 0x4C21E, size: 0x8, addend: 0x0, symName: __ZL14pathIntelCNLFb, symObjAddr: 0x1D720, symBinAddr: 0x74B70, symSize: 0x0 }
  - { offsetInCU: 0x45A, offset: 0x4C23C, size: 0x8, addend: 0x0, symName: __ZL12pathIntelICL, symObjAddr: 0x1D750, symBinAddr: 0x74BA0, symSize: 0x0 }
  - { offsetInCU: 0x478, offset: 0x4C25A, size: 0x8, addend: 0x0, symName: __ZL16pathIntelICLLPFb, symObjAddr: 0x1D780, symBinAddr: 0x74BD0, symSize: 0x0 }
  - { offsetInCU: 0x496, offset: 0x4C278, size: 0x8, addend: 0x0, symName: __ZL16pathIntelICLHPFb, symObjAddr: 0x1D7B0, symBinAddr: 0x74C00, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x4C2AC, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreenD1Ev, symObjAddr: 0x18880, symBinAddr: 0x19750, symSize: 0x10 }
  - { offsetInCU: 0x2F, offset: 0x4C2B4, size: 0x8, addend: 0x0, symName: __ZN4IGFX19LSPCONDriverSupportD1Ev, symObjAddr: 0x19DF0, symBinAddr: 0x1ACC0, symSize: 0x10 }
  - { offsetInCU: 0x4A, offset: 0x4C2CF, size: 0x8, addend: 0x0, symName: _WhateverGreen_startSuccess, symObjAddr: 0x150608, symBinAddr: 0x75878, symSize: 0x0 }
  - { offsetInCU: 0x6B, offset: 0x4C2F0, size: 0x8, addend: 0x0, symName: _WhateverGreen_debugEnabled, symObjAddr: 0x150609, symBinAddr: 0x75879, symSize: 0x0 }
  - { offsetInCU: 0x86, offset: 0x4C30B, size: 0x8, addend: 0x0, symName: _WhateverGreen_debugPrintDelay, symObjAddr: 0x15060C, symBinAddr: 0x7587C, symSize: 0x0 }
  - { offsetInCU: 0x96, offset: 0x4C31B, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreen10gMetaClassE, symObjAddr: 0x151528, symBinAddr: 0x75820, symSize: 0x0 }
  - { offsetInCU: 0x2EB, offset: 0x4C570, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreen9metaClassE, symObjAddr: 0x733B0, symBinAddr: 0x729F0, symSize: 0x0 }
  - { offsetInCU: 0x303, offset: 0x4C588, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreen10superClassE, symObjAddr: 0x733B8, symBinAddr: 0x729F8, symSize: 0x0 }
  - { offsetInCU: 0x321, offset: 0x4C5A6, size: 0x8, addend: 0x0, symName: _WhateverGreen_selfInstance, symObjAddr: 0x150610, symBinAddr: 0x75880, symSize: 0x0 }
  - { offsetInCU: 0x349, offset: 0x4C5CE, size: 0x8, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0x6F930, symBinAddr: 0x6EEA0, symSize: 0x0 }
  - { offsetInCU: 0x37B, offset: 0x4C600, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreenD1Ev, symObjAddr: 0x18880, symBinAddr: 0x19750, symSize: 0x10 }
  - { offsetInCU: 0x3F6, offset: 0x4C67B, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreenD0Ev, symObjAddr: 0x18890, symBinAddr: 0x19760, symSize: 0x30 }
  - { offsetInCU: 0x484, offset: 0x4C709, size: 0x8, addend: 0x0, symName: __ZNK13WhateverGreen12getMetaClassEv, symObjAddr: 0x188C0, symBinAddr: 0x19790, symSize: 0x10 }
  - { offsetInCU: 0x4B5, offset: 0x4C73A, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreen5probeEP9IOServicePi, symObjAddr: 0x188D0, symBinAddr: 0x197A0, symSize: 0x60 }
  - { offsetInCU: 0x527, offset: 0x4C7AC, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreen5startEP9IOService, symObjAddr: 0x18930, symBinAddr: 0x19800, symSize: 0x60 }
  - { offsetInCU: 0x571, offset: 0x4C7F6, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreen4stopEP9IOService, symObjAddr: 0x18990, symBinAddr: 0x19860, symSize: 0x20 }
  - { offsetInCU: 0x5E2, offset: 0x4C867, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreen9MetaClassD1Ev, symObjAddr: 0x189B0, symBinAddr: 0x19880, symSize: 0x10 }
  - { offsetInCU: 0x65D, offset: 0x4C8E2, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreen9MetaClassD0Ev, symObjAddr: 0x189C0, symBinAddr: 0x19890, symSize: 0x10 }
  - { offsetInCU: 0x730, offset: 0x4C9B5, size: 0x8, addend: 0x0, symName: __ZNK13WhateverGreen9MetaClass5allocEv, symObjAddr: 0x189D0, symBinAddr: 0x198A0, symSize: 0x40 }
  - { offsetInCU: 0x738, offset: 0x4C9BD, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_plugin_start.cpp, symObjAddr: 0x18A10, symBinAddr: 0x198E0, symSize: 0x40 }
  - { offsetInCU: 0x806, offset: 0x4CA8B, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_plugin_start.cpp, symObjAddr: 0x18A10, symBinAddr: 0x198E0, symSize: 0x40 }
  - { offsetInCU: 0x896, offset: 0x4CB1B, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a.883, symObjAddr: 0x18A50, symBinAddr: 0x19920, symSize: 0x20 }
  - { offsetInCU: 0x908, offset: 0x4CB8D, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreen9MetaClassC1Ev, symObjAddr: 0x18A70, symBinAddr: 0x19940, symSize: 0x40 }
  - { offsetInCU: 0x963, offset: 0x4CBE8, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreenC2EPK11OSMetaClass, symObjAddr: 0x18AB0, symBinAddr: 0x19980, symSize: 0x20 }
  - { offsetInCU: 0x9D4, offset: 0x4CC59, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreenC1EPK11OSMetaClass, symObjAddr: 0x18AD0, symBinAddr: 0x199A0, symSize: 0x20 }
  - { offsetInCU: 0xA5C, offset: 0x4CCE1, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreenD2Ev, symObjAddr: 0x18AF0, symBinAddr: 0x199C0, symSize: 0x10 }
  - { offsetInCU: 0xA89, offset: 0x4CD0E, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreen9MetaClassC2Ev, symObjAddr: 0x18B00, symBinAddr: 0x199D0, symSize: 0x40 }
  - { offsetInCU: 0xAB8, offset: 0x4CD3D, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreenC1Ev, symObjAddr: 0x18B40, symBinAddr: 0x19A10, symSize: 0x30 }
  - { offsetInCU: 0xB13, offset: 0x4CD98, size: 0x8, addend: 0x0, symName: __ZN13WhateverGreenC2Ev, symObjAddr: 0x18B70, symBinAddr: 0x19A40, symSize: 0x30 }
  - { offsetInCU: 0xB7C, offset: 0x4CE01, size: 0x8, addend: 0x0, symName: _WhateverGreen_kern_start, symObjAddr: 0x18BA0, symBinAddr: 0x19A70, symSize: 0x1240 }
  - { offsetInCU: 0x19B2, offset: 0x4DC37, size: 0x8, addend: 0x0, symName: _WhateverGreen_kern_stop, symObjAddr: 0x19DE0, symBinAddr: 0x1ACB0, symSize: 0x10 }
  - { offsetInCU: 0x19BA, offset: 0x4DC3F, size: 0x8, addend: 0x0, symName: __ZN4IGFX19LSPCONDriverSupportD1Ev, symObjAddr: 0x19DF0, symBinAddr: 0x1ACC0, symSize: 0x10 }
  - { offsetInCU: 0x35, offset: 0x4DCBA, size: 0x8, addend: 0x0, symName: _WhateverGreen_binaryMod, symObjAddr: 0x1DB10, symBinAddr: 0x74F60, symSize: 0x0 }
  - { offsetInCU: 0x6B, offset: 0x4DCF0, size: 0x8, addend: 0x0, symName: _WhateverGreen_procInfoLegacy, symObjAddr: 0x1DC60, symBinAddr: 0x750B0, symSize: 0x0 }
  - { offsetInCU: 0x9A, offset: 0x4DD1F, size: 0x8, addend: 0x0, symName: _WhateverGreen_procInfoModern, symObjAddr: 0x1DF60, symBinAddr: 0x753B0, symSize: 0x0 }
  - { offsetInCU: 0xC9, offset: 0x4DD4E, size: 0x8, addend: 0x0, symName: __ZL8patches0, symObjAddr: 0x1D7E0, symBinAddr: 0x74C30, symSize: 0x0 }
  - { offsetInCU: 0x100, offset: 0x4DD85, size: 0x8, addend: 0x0, symName: __ZL9patchBuf0, symObjAddr: 0x6F948, symBinAddr: 0x6EEB8, symSize: 0x0 }
  - { offsetInCU: 0x11F, offset: 0x4DDA4, size: 0x8, addend: 0x0, symName: __ZL9patchBuf1, symObjAddr: 0x6F958, symBinAddr: 0x6EEC8, symSize: 0x0 }
  - { offsetInCU: 0x13E, offset: 0x4DDC3, size: 0x8, addend: 0x0, symName: __ZL9patchBuf2, symObjAddr: 0x6F968, symBinAddr: 0x6EED8, symSize: 0x0 }
  - { offsetInCU: 0x15D, offset: 0x4DDE2, size: 0x8, addend: 0x0, symName: __ZL9patchBuf3, symObjAddr: 0x6F978, symBinAddr: 0x6EEE8, symSize: 0x0 }
  - { offsetInCU: 0x178, offset: 0x4DDFD, size: 0x8, addend: 0x0, symName: __ZL9patchBuf4, symObjAddr: 0x6F988, symBinAddr: 0x6EEF8, symSize: 0x0 }
  - { offsetInCU: 0x1AB, offset: 0x4DE30, size: 0x8, addend: 0x0, symName: __ZL9patchBuf5, symObjAddr: 0x6F9A0, symBinAddr: 0x6EF10, symSize: 0x0 }
  - { offsetInCU: 0x1C9, offset: 0x4DE4E, size: 0x8, addend: 0x0, symName: __ZL8patches1, symObjAddr: 0x1D890, symBinAddr: 0x74CE0, symSize: 0x0 }
  - { offsetInCU: 0x200, offset: 0x4DE85, size: 0x8, addend: 0x0, symName: __ZL9patchBuf6, symObjAddr: 0x6F9B8, symBinAddr: 0x6EF28, symSize: 0x0 }
  - { offsetInCU: 0x21F, offset: 0x4DEA4, size: 0x8, addend: 0x0, symName: __ZL9patchBuf7, symObjAddr: 0x6F9C0, symBinAddr: 0x6EF30, symSize: 0x0 }
  - { offsetInCU: 0x239, offset: 0x4DEBE, size: 0x8, addend: 0x0, symName: __ZL8patches2, symObjAddr: 0x1D8D0, symBinAddr: 0x74D20, symSize: 0x0 }
  - { offsetInCU: 0x270, offset: 0x4DEF5, size: 0x8, addend: 0x0, symName: __ZL9patchBuf8, symObjAddr: 0x6F9C8, symBinAddr: 0x6EF38, symSize: 0x0 }
  - { offsetInCU: 0x28F, offset: 0x4DF14, size: 0x8, addend: 0x0, symName: __ZL9patchBuf9, symObjAddr: 0x6F9D0, symBinAddr: 0x6EF40, symSize: 0x0 }
  - { offsetInCU: 0x2AE, offset: 0x4DF33, size: 0x8, addend: 0x0, symName: __ZL10patchBuf10, symObjAddr: 0x6F9D8, symBinAddr: 0x6EF48, symSize: 0x0 }
  - { offsetInCU: 0x2CD, offset: 0x4DF52, size: 0x8, addend: 0x0, symName: __ZL10patchBuf11, symObjAddr: 0x6F9E0, symBinAddr: 0x6EF50, symSize: 0x0 }
  - { offsetInCU: 0x2E7, offset: 0x4DF6C, size: 0x8, addend: 0x0, symName: __ZL8patches3, symObjAddr: 0x1D940, symBinAddr: 0x74D90, symSize: 0x0 }
  - { offsetInCU: 0x30A, offset: 0x4DF8F, size: 0x8, addend: 0x0, symName: __ZL10patchBuf12, symObjAddr: 0x6F9E8, symBinAddr: 0x6EF58, symSize: 0x0 }
  - { offsetInCU: 0x329, offset: 0x4DFAE, size: 0x8, addend: 0x0, symName: __ZL10patchBuf13, symObjAddr: 0x6F9F0, symBinAddr: 0x6EF60, symSize: 0x0 }
  - { offsetInCU: 0x348, offset: 0x4DFCD, size: 0x8, addend: 0x0, symName: __ZL10patchBuf14, symObjAddr: 0x6F9F8, symBinAddr: 0x6EF68, symSize: 0x0 }
  - { offsetInCU: 0x367, offset: 0x4DFEC, size: 0x8, addend: 0x0, symName: __ZL10patchBuf15, symObjAddr: 0x6FA00, symBinAddr: 0x6EF70, symSize: 0x0 }
  - { offsetInCU: 0x381, offset: 0x4E006, size: 0x8, addend: 0x0, symName: __ZL8patches4, symObjAddr: 0x1D9B0, symBinAddr: 0x74E00, symSize: 0x0 }
  - { offsetInCU: 0x3B8, offset: 0x4E03D, size: 0x8, addend: 0x0, symName: __ZL10patchBuf16, symObjAddr: 0x6FA08, symBinAddr: 0x6EF78, symSize: 0x0 }
  - { offsetInCU: 0x3D7, offset: 0x4E05C, size: 0x8, addend: 0x0, symName: __ZL10patchBuf17, symObjAddr: 0x6FA10, symBinAddr: 0x6EF80, symSize: 0x0 }
  - { offsetInCU: 0x3F6, offset: 0x4E07B, size: 0x8, addend: 0x0, symName: __ZL10patchBuf18, symObjAddr: 0x6FA18, symBinAddr: 0x6EF88, symSize: 0x0 }
  - { offsetInCU: 0x415, offset: 0x4E09A, size: 0x8, addend: 0x0, symName: __ZL10patchBuf19, symObjAddr: 0x6FA20, symBinAddr: 0x6EF90, symSize: 0x0 }
  - { offsetInCU: 0x430, offset: 0x4E0B5, size: 0x8, addend: 0x0, symName: __ZL10patchBuf20, symObjAddr: 0x6FA28, symBinAddr: 0x6EF98, symSize: 0x0 }
  - { offsetInCU: 0x463, offset: 0x4E0E8, size: 0x8, addend: 0x0, symName: __ZL10patchBuf21, symObjAddr: 0x6FA30, symBinAddr: 0x6EFA0, symSize: 0x0 }
  - { offsetInCU: 0x486, offset: 0x4E10B, size: 0x8, addend: 0x0, symName: __ZL10patchBuf24, symObjAddr: 0x6FA38, symBinAddr: 0x6EFA8, symSize: 0x0 }
  - { offsetInCU: 0x4A0, offset: 0x4E125, size: 0x8, addend: 0x0, symName: __ZL8patches5, symObjAddr: 0x1DAD0, symBinAddr: 0x74F20, symSize: 0x0 }
  - { offsetInCU: 0x4C3, offset: 0x4E148, size: 0x8, addend: 0x0, symName: __ZL10patchBuf26, symObjAddr: 0x6FA48, symBinAddr: 0x6EFB8, symSize: 0x0 }
  - { offsetInCU: 0x4E2, offset: 0x4E167, size: 0x8, addend: 0x0, symName: __ZL10patchBuf27, symObjAddr: 0x6FA58, symBinAddr: 0x6EFC8, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x4E19B, size: 0x8, addend: 0x0, symName: __ZN4IGFX19LSPCONDriverSupportD1Ev, symObjAddr: 0x19DF0, symBinAddr: 0x1ACC0, symSize: 0x10 }
  - { offsetInCU: 0x40, offset: 0x4E1B4, size: 0x8, addend: 0x0, symName: __ZN4IGFX19LSPCONDriverSupportD1Ev, symObjAddr: 0x19DF0, symBinAddr: 0x1ACC0, symSize: 0x10 }
  - { offsetInCU: 0x75, offset: 0x4E1E9, size: 0x8, addend: 0x0, symName: __ZN4IGFX19LSPCONDriverSupportD0Ev, symObjAddr: 0x19E00, symBinAddr: 0x1ACD0, symSize: 0x10 }
  - { offsetInCU: 0xAC, offset: 0x4E220, size: 0x8, addend: 0x0, symName: __ZN4IGFX19LSPCONDriverSupport4initEv, symObjAddr: 0x19E10, symBinAddr: 0x1ACE0, symSize: 0x10 }
  - { offsetInCU: 0xFC, offset: 0x4E270, size: 0x8, addend: 0x0, symName: __ZN4IGFX19LSPCONDriverSupport6deinitEv, symObjAddr: 0x19E20, symBinAddr: 0x1ACF0, symSize: 0x80 }
  - { offsetInCU: 0x2B0, offset: 0x4E424, size: 0x8, addend: 0x0, symName: __ZN4IGFX19LSPCONDriverSupport13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x19EA0, symBinAddr: 0x1AD70, symSize: 0x270 }
  - { offsetInCU: 0x72B, offset: 0x4E89F, size: 0x8, addend: 0x0, symName: __ZN4IGFX19LSPCONDriverSupport22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x1A110, symBinAddr: 0x1AFE0, symSize: 0x150 }
  - { offsetInCU: 0x9D2, offset: 0x4EB46, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule19processGraphicsKextER13KernelPatchermym.945, symObjAddr: 0x1A260, symBinAddr: 0x1B130, symSize: 0x10 }
  - { offsetInCU: 0xA45, offset: 0x4EBB9, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule26disableDependentSubmodulesEv.946, symObjAddr: 0x1A270, symBinAddr: 0x1B140, symSize: 0x10 }
  - { offsetInCU: 0xF4A, offset: 0x4F0BE, size: 0x8, addend: 0x0, symName: __ZN4IGFX19LSPCONDriverSupport15wrapGetDPCDInfoEPvP15IORegistryEntryS1_, symObjAddr: 0x1A280, symBinAddr: 0x1B150, symSize: 0x490 }
  - { offsetInCU: 0xF52, offset: 0x4F0C6, size: 0x8, addend: 0x0, symName: __ZN6LSPCON7getModeERNS_4ModeE, symObjAddr: 0x1A710, symBinAddr: 0x1B5E0, symSize: 0x130 }
  - { offsetInCU: 0x154B, offset: 0x4F6BF, size: 0x8, addend: 0x0, symName: __ZN6LSPCON7getModeERNS_4ModeE, symObjAddr: 0x1A710, symBinAddr: 0x1B5E0, symSize: 0x130 }
  - { offsetInCU: 0x16A6, offset: 0x4F81A, size: 0x8, addend: 0x0, symName: __ZN6LSPCON7setModeENS_4ModeE, symObjAddr: 0x1A840, symBinAddr: 0x1B710, symSize: 0x5D0 }
  - { offsetInCU: 0x27, offset: 0x4FC8F, size: 0x8, addend: 0x0, symName: __ZN4IGFX23FramebufferDebugSupportD1Ev, symObjAddr: 0x1AE10, symBinAddr: 0x1BCE0, symSize: 0x10 }
  - { offsetInCU: 0x39, offset: 0x4FCA1, size: 0x8, addend: 0x0, symName: __ZN4IGFX23FramebufferDebugSupportD1Ev, symObjAddr: 0x1AE10, symBinAddr: 0x1BCE0, symSize: 0x10 }
  - { offsetInCU: 0x6E, offset: 0x4FCD6, size: 0x8, addend: 0x0, symName: __ZN4IGFX23FramebufferDebugSupportD0Ev, symObjAddr: 0x1AE20, symBinAddr: 0x1BCF0, symSize: 0x10 }
  - { offsetInCU: 0xA5, offset: 0x4FD0D, size: 0x8, addend: 0x0, symName: __ZN4IGFX23FramebufferDebugSupport4initEv, symObjAddr: 0x1AE30, symBinAddr: 0x1BD00, symSize: 0x10 }
  - { offsetInCU: 0xD9, offset: 0x4FD41, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule6deinitEv.978, symObjAddr: 0x1AE40, symBinAddr: 0x1BD10, symSize: 0x10 }
  - { offsetInCU: 0x108, offset: 0x4FD70, size: 0x8, addend: 0x0, symName: __ZN4IGFX23FramebufferDebugSupport13processKernelER13KernelPatcherP10DeviceInfo, symObjAddr: 0x1AE50, symBinAddr: 0x1BD20, symSize: 0x10 }
  - { offsetInCU: 0x15C, offset: 0x4FDC4, size: 0x8, addend: 0x0, symName: __ZN4IGFX23FramebufferDebugSupport22processFramebufferKextER13KernelPatchermym, symObjAddr: 0x1AE60, symBinAddr: 0x1BD30, symSize: 0x20 }
  - { offsetInCU: 0x164, offset: 0x4FDCC, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule19processGraphicsKextER13KernelPatchermym.979, symObjAddr: 0x1AE80, symBinAddr: 0x1BD50, symSize: 0x10 }
  - { offsetInCU: 0x1D2, offset: 0x4FE3A, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule19processGraphicsKextER13KernelPatchermym.979, symObjAddr: 0x1AE80, symBinAddr: 0x1BD50, symSize: 0x10 }
  - { offsetInCU: 0x245, offset: 0x4FEAD, size: 0x8, addend: 0x0, symName: __ZN4IGFX14PatchSubmodule26disableDependentSubmodulesEv.980, symObjAddr: 0x1AE90, symBinAddr: 0x1BD60, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x4FF02, size: 0x8, addend: 0x0, symName: __ZN4NGFX25wrapCsfgGetPlatformBinaryEPv, symObjAddr: 0x1AEA0, symBinAddr: 0x1BD70, symSize: 0x50 }
  - { offsetInCU: 0x45, offset: 0x4FF20, size: 0x8, addend: 0x0, symName: __ZL8kextList.989, symObjAddr: 0x1E290, symBinAddr: 0x756E0, symSize: 0x0 }
  - { offsetInCU: 0x82, offset: 0x4FF5D, size: 0x8, addend: 0x0, symName: __ZL11pathGeForce, symObjAddr: 0x1E358, symBinAddr: 0x757A8, symSize: 0x0 }
  - { offsetInCU: 0xA0, offset: 0x4FF7B, size: 0x8, addend: 0x0, symName: __ZL14pathGeForceWeb, symObjAddr: 0x1E360, symBinAddr: 0x757B0, symSize: 0x0 }
  - { offsetInCU: 0xBE, offset: 0x4FF99, size: 0x8, addend: 0x0, symName: __ZL18pathNVDAStartupWeb, symObjAddr: 0x1E370, symBinAddr: 0x757C0, symSize: 0x0 }
  - { offsetInCU: 0xDC, offset: 0x4FFB7, size: 0x8, addend: 0x0, symName: __ZL14pathNVDAResman, symObjAddr: 0x1E380, symBinAddr: 0x757D0, symSize: 0x0 }
  - { offsetInCU: 0xFA, offset: 0x4FFD5, size: 0x8, addend: 0x0, symName: __ZL17pathIONDRVSupport, symObjAddr: 0x1E388, symBinAddr: 0x757D8, symSize: 0x0 }
  - { offsetInCU: 0xC37, offset: 0x50B12, size: 0x8, addend: 0x0, symName: __ZN4NGFX25wrapCsfgGetPlatformBinaryEPv, symObjAddr: 0x1AEA0, symBinAddr: 0x1BD70, symSize: 0x50 }
  - { offsetInCU: 0xD25, offset: 0x50C00, size: 0x8, addend: 0x0, symName: __ZN4NGFX22wrapSetAccelPropertiesEP9IOService, symObjAddr: 0x1AEF0, symBinAddr: 0x1BDC0, symSize: 0x120 }
  - { offsetInCU: 0xD2D, offset: 0x50C08, size: 0x8, addend: 0x0, symName: __ZN4NGFX19wrapStartupWebProbeEP9IOServiceS1_Pi, symObjAddr: 0x1B010, symBinAddr: 0x1BEE0, symSize: 0x100 }
  - { offsetInCU: 0xDE1, offset: 0x50CBC, size: 0x8, addend: 0x0, symName: __ZN4NGFX19wrapStartupWebProbeEP9IOServiceS1_Pi, symObjAddr: 0x1B010, symBinAddr: 0x1BEE0, symSize: 0x100 }
  - { offsetInCU: 0xE92, offset: 0x50D6D, size: 0x8, addend: 0x0, symName: __ZN4NGFX17wrapNdrvDoControlEP17IONDRVFramebufferjPv, symObjAddr: 0x1B110, symBinAddr: 0x1BFE0, symSize: 0xD0 }
  - { offsetInCU: 0xF19, offset: 0x50DF4, size: 0x8, addend: 0x0, symName: __ZN4NGFX16resmanErrorLogVAEPvjPKcz, symObjAddr: 0x1B1E0, symBinAddr: 0x1C0B0, symSize: 0x110 }
  - { offsetInCU: 0xF9F, offset: 0x50E7A, size: 0x8, addend: 0x0, symName: __ZN4NGFX18wrapVaddrPreSubmitEPv, symObjAddr: 0x1B2F0, symBinAddr: 0x1C1C0, symSize: 0x90 }
  - { offsetInCU: 0x27, offset: 0x51064, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_kern_start.cpp, symObjAddr: 0x1B380, symBinAddr: 0x1C250, symSize: 0x1310 }
  - { offsetInCU: 0x49, offset: 0x51086, size: 0x8, addend: 0x0, symName: __ZL3weg, symObjAddr: 0x150620, symBinAddr: 0x75890, symSize: 0x0 }
  - { offsetInCU: 0xBA, offset: 0x510F7, size: 0x8, addend: 0x0, symName: __ZL10bootargOff, symObjAddr: 0x1E390, symBinAddr: 0x757E0, symSize: 0x0 }
  - { offsetInCU: 0xD8, offset: 0x51115, size: 0x8, addend: 0x0, symName: __ZL12bootargDebug, symObjAddr: 0x1E398, symBinAddr: 0x757E8, symSize: 0x0 }
  - { offsetInCU: 0xF6, offset: 0x51133, size: 0x8, addend: 0x0, symName: __ZL11bootargBeta, symObjAddr: 0x1E3A0, symBinAddr: 0x757F0, symSize: 0x0 }
  - { offsetInCU: 0xEED, offset: 0x51F2A, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_kern_start.cpp, symObjAddr: 0x1B380, symBinAddr: 0x1C250, symSize: 0x1310 }
  - { offsetInCU: 0x1DA7, offset: 0x52DE4, size: 0x8, addend: 0x0, symName: __ZN3RAD17populateGetHWInfoILm0EEEiP9IOServicePv, symObjAddr: 0x1C690, symBinAddr: 0x1D560, symSize: 0x60 }
  - { offsetInCU: 0x1E24, offset: 0x52E61, size: 0x8, addend: 0x0, symName: __ZN3RAD17populateGetHWInfoILm1EEEiP9IOServicePv, symObjAddr: 0x1C6F0, symBinAddr: 0x1D5C0, symSize: 0x70 }
  - { offsetInCU: 0x1EA1, offset: 0x52EDE, size: 0x8, addend: 0x0, symName: __ZN3RAD17populateGetHWInfoILm2EEEiP9IOServicePv, symObjAddr: 0x1C760, symBinAddr: 0x1D630, symSize: 0x70 }
  - { offsetInCU: 0x1F1E, offset: 0x52F5B, size: 0x8, addend: 0x0, symName: __ZN3RAD20populdateAccelConfigILm0EEEvP9IOServicePPKc, symObjAddr: 0x1C7D0, symBinAddr: 0x1D6A0, symSize: 0x60 }
  - { offsetInCU: 0x1F73, offset: 0x52FB0, size: 0x8, addend: 0x0, symName: __ZN3RAD20populdateAccelConfigILm1EEEvP9IOServicePPKc, symObjAddr: 0x1C830, symBinAddr: 0x1D700, symSize: 0x60 }
  - { offsetInCU: 0x1FC8, offset: 0x53005, size: 0x8, addend: 0x0, symName: __ZN3RAD20populdateAccelConfigILm3EEEvP9IOServicePPKc, symObjAddr: 0x1C890, symBinAddr: 0x1D760, symSize: 0x60 }
  - { offsetInCU: 0x201D, offset: 0x5305A, size: 0x8, addend: 0x0, symName: __ZN3RAD20populdateAccelConfigILm4EEEvP9IOServicePPKc, symObjAddr: 0x1C8F0, symBinAddr: 0x1D7C0, symSize: 0x60 }
  - { offsetInCU: 0x2072, offset: 0x530AF, size: 0x8, addend: 0x0, symName: __ZN3RAD20populdateAccelConfigILm5EEEvP9IOServicePPKc, symObjAddr: 0x1C950, symBinAddr: 0x1D820, symSize: 0x60 }
  - { offsetInCU: 0x20C7, offset: 0x53104, size: 0x8, addend: 0x0, symName: __ZN3RAD20populdateAccelConfigILm6EEEvP9IOServicePPKc, symObjAddr: 0x1C9B0, symBinAddr: 0x1D880, symSize: 0x60 }
  - { offsetInCU: 0x211C, offset: 0x53159, size: 0x8, addend: 0x0, symName: __ZN3RAD20populdateAccelConfigILm7EEEvP9IOServicePPKc, symObjAddr: 0x1CA10, symBinAddr: 0x1D8E0, symSize: 0x60 }
  - { offsetInCU: 0x2171, offset: 0x531AE, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a.1080, symObjAddr: 0x1CA70, symBinAddr: 0x1D940, symSize: 0x6 }
...
